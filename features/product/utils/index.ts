import { PRODUCT_V2_TYPE_OPTIONS } from '../constants'
import { ProductV2TypeEnum } from '../enums'
import { SearchProductFilterItem, SearchProductFilterType } from '../stores/ProductStore'

// Helper function to create product type filter items
export const createProductTypeFilterItem = (
  productType: ProductV2TypeEnum,
): SearchProductFilterItem => ({
  id: productType,
  label: PRODUCT_V2_TYPE_OPTIONS[productType]?.label || productType,
  type: SearchProductFilterType.PRODUCT_TYPE,
})

// Helper function to get all product type filter items
export const getAllProductTypeFilterItems = (): SearchProductFilterItem[] => {
  return Object.values(ProductV2TypeEnum).map(createProductTypeFilterItem)
}
