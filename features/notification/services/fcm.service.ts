// Conditional Firebase imports for Expo Go compatibility
let firebaseMessaging: any = null
let FirebaseMessagingTypes: any = null

try {
  if (process.env.EXPO_PUBLIC_APP_ENVIROMENT !== 'expo-go') {
    const firebaseModule = require('@react-native-firebase/messaging')
    firebaseMessaging = firebaseModule
    FirebaseMessagingTypes = firebaseModule.FirebaseMessagingTypes
  }
} catch (error) {
  console.log('Firebase messaging not available in Expo Go')
}
import Constants, { ExecutionEnvironment } from 'expo-constants'
import { Platform } from 'react-native'

export interface FCMNotificationData {
  type?: string
  chatId?: string
  appointmentId?: string
  medicineId?: string
  [key: string]: any
}

export interface FCMMessage {
  messageId: string
  data?: FCMNotificationData
  notification?: {
    title?: string
    body?: string
    imageUrl?: string
  }
  from?: string
  to?: string
  collapseKey?: string
  sentTime?: number
  ttl?: number
}

export type FCMAuthorizationStatus = any // FirebaseMessagingTypes.AuthorizationStatus
export type FCMNotificationOpenedListener = (remoteMessage: any) => void
export type FCMMessageListener = (remoteMessage: any) => void

class FCMService {
  private static instance: FCMService
  private backgroundMessageHandler: FCMMessageListener | null = null
  private foregroundMessageHandler: FCMMessageListener | null = null
  private notificationOpenedHandler: FCMNotificationOpenedListener | null = null
  private unsubscribeForegroundListener: (() => void) | null = null
  private unsubscribeNotificationOpenedListener: (() => void) | null = null
  private isFirebaseAvailable: boolean = false

  private constructor() {
    // Check if Firebase is available
    this.checkFirebaseAvailability()

    // Initialize FCM only if available
    if (this.isFirebaseAvailable) {
      this.initializeFCM()
    }
  }

  public static getInstance(): FCMService {
    if (!FCMService.instance) {
      FCMService.instance = new FCMService()
    }
    return FCMService.instance
  }

  /**
   * Check if Firebase is available (not in Expo Go)
   */
  private checkFirebaseAvailability(): void {
    try {
      if (
        Constants.appOwnership === 'expo' ||
        Constants.executionEnvironment === ExecutionEnvironment.StoreClient
      ) {
        this.isFirebaseAvailable = false
        return
      }

      if (!firebaseMessaging) {
        this.isFirebaseAvailable = false
        return
      }

      const messagingInstance = firebaseMessaging.getMessaging()
      if (messagingInstance) {
        this.isFirebaseAvailable = true
      }
    } catch (error) {
      this.isFirebaseAvailable = false
    }
  }

  /**
   * Initialize FCM with default settings
   */
  private async initializeFCM(): Promise<void> {
    if (!this.isFirebaseAvailable || !firebaseMessaging) {
      return
    }

    try {
      const messagingInstance = firebaseMessaging.getMessaging()

      if (!firebaseMessaging.isDeviceRegisteredForRemoteMessages(messagingInstance)) {
        await firebaseMessaging.registerDeviceForRemoteMessages(messagingInstance)
      }

      await firebaseMessaging.setAutoInitEnabled(messagingInstance, true)
    } catch (error) {
      console.error('Failed to initialize FCM:', error)
    }
  }

  /**
   * Request notification permissions (iOS)
   */
  async requestPermission(): Promise<FCMAuthorizationStatus> {
    if (!this.isFirebaseAvailable || !firebaseMessaging) {
      return 'denied' // AuthorizationStatus.DENIED
    }

    try {
      const messagingInstance = firebaseMessaging.getMessaging()
      const authStatus = await firebaseMessaging.requestPermission(messagingInstance)
      return authStatus
    } catch (error) {
      console.error('Failed to request FCM permissions:', error)
      throw error
    }
  }

  /**
   * Check current permission status
   */
  async checkPermission(): Promise<FCMAuthorizationStatus> {
    if (!this.isFirebaseAvailable || !firebaseMessaging) {
      return 'denied' // AuthorizationStatus.DENIED
    }

    try {
      const messagingInstance = firebaseMessaging.getMessaging()
      return await firebaseMessaging.hasPermission(messagingInstance)
    } catch (error) {
      console.error('Failed to check FCM permissions:', error)
      throw error
    }
  }

  /**
   * Get FCM token for this device
   */
  async getToken(): Promise<string> {
    if (!this.isFirebaseAvailable || !firebaseMessaging) {
      throw new Error('Firebase messaging not available')
    }

    try {
      const messagingInstance = firebaseMessaging.getMessaging()
      const token = await firebaseMessaging.getToken(messagingInstance)

      if (!token) {
        throw new Error('FCM token is null or empty')
      }

      return token
    } catch (error) {
      console.error('FCM Service: Failed to get FCM token:', error)
      throw error
    }
  }

  /**
   * Delete FCM token
   */
  async deleteToken(): Promise<void> {
    if (!this.isFirebaseAvailable || !firebaseMessaging) {
      return
    }

    try {
      const messagingInstance = firebaseMessaging.getMessaging()
      await firebaseMessaging.deleteToken(messagingInstance)
    } catch (error) {
      console.error('Failed to delete FCM token:', error)
      throw error
    }
  }

  /**
   * Subscribe to a topic
   */
  async subscribeToTopic(topic: string): Promise<void> {
    if (!this.isFirebaseAvailable || !firebaseMessaging) {
      return
    }

    try {
      const messagingInstance = firebaseMessaging.getMessaging()
      await firebaseMessaging.subscribeToTopic(messagingInstance, topic)
    } catch (error) {
      console.error(`Failed to subscribe to topic ${topic}:`, error)
      throw error
    }
  }

  /**
   * Unsubscribe from a topic
   */
  async unsubscribeFromTopic(topic: string): Promise<void> {
    if (!this.isFirebaseAvailable || !firebaseMessaging) {
      return
    }

    try {
      const messagingInstance = firebaseMessaging.getMessaging()
      await firebaseMessaging.unsubscribeFromTopic(messagingInstance, topic)
    } catch (error) {
      console.error(`Failed to unsubscribe from topic ${topic}:`, error)
      throw error
    }
  }

  /**
   * Set background message handler
   */
  setBackgroundMessageHandler(handler: FCMMessageListener): void {
    if (!this.isFirebaseAvailable || !firebaseMessaging) {
      return
    }

    this.backgroundMessageHandler = handler
    console.log('FCM background message handler set', this.backgroundMessageHandler)
    const messagingInstance = firebaseMessaging.getMessaging()
    firebaseMessaging.setBackgroundMessageHandler(messagingInstance, async (remoteMessage: any) => {
      console.log('Message handled in the background!', remoteMessage)
      if (this.backgroundMessageHandler) {
        console.log('FCM background message:', remoteMessage.data)
        this.backgroundMessageHandler(remoteMessage)
      }
    })
  }

  /**
   * Set foreground message handler
   */
  setForegroundMessageHandler(handler: FCMMessageListener): void {
    if (!this.isFirebaseAvailable || !firebaseMessaging) {
      return
    }

    this.foregroundMessageHandler = handler

    if (this.unsubscribeForegroundListener) {
      this.unsubscribeForegroundListener()
    }

    const messagingInstance = firebaseMessaging.getMessaging()
    this.unsubscribeForegroundListener = firebaseMessaging.onMessage(
      messagingInstance,
      async (remoteMessage: any) => {
        if (this.foregroundMessageHandler) {
          this.foregroundMessageHandler(remoteMessage)
        }
      },
    )
  }

  /**
   * Set notification opened handler
   */
  setNotificationOpenedHandler(handler: FCMNotificationOpenedListener): void {
    if (!this.isFirebaseAvailable || !firebaseMessaging) {
      console.log('FCMService: Firebase not available, cannot set notification opened handler')
      return
    }

    console.log('🔥 FCMService: Setting up notification opened handler')
    this.notificationOpenedHandler = handler

    if (this.unsubscribeNotificationOpenedListener) {
      this.unsubscribeNotificationOpenedListener()
    }

    const messagingInstance = firebaseMessaging.getMessaging()

    this.unsubscribeNotificationOpenedListener = firebaseMessaging.onNotificationOpenedApp(
      messagingInstance,
      (remoteMessage: any) => {
        console.log('🔥 FCMService: Notification opened app triggered!', remoteMessage)
        if (this.notificationOpenedHandler) {
          try {
            console.log('🔥 FCMService: Calling notification opened handler')
            this.notificationOpenedHandler(remoteMessage)
          } catch (error) {
            console.error('🔥 FCMService: Handler execution failed:', error)
          }
        } else {
          console.log('🔥 FCMService: No notification opened handler set')
        }
      },
    )

    console.log('🔥 FCMService: Checking for initial notification')
    firebaseMessaging.getInitialNotification(messagingInstance).then((remoteMessage: any) => {
      if (remoteMessage) {
        console.log('🔥 FCMService: Initial notification found!', remoteMessage)
        if (this.notificationOpenedHandler) {
          try {
            console.log('🔥 FCMService: Calling initial notification handler')
            this.notificationOpenedHandler(remoteMessage)
          } catch (error) {
            console.error('🔥 FCMService: Initial notification handler failed:', error)
          }
        }
      } else {
        console.log('🔥 FCMService: No initial notification found')
      }
    })
  }

  /**
   * Setup complete FCM listeners
   */
  setupMessageHandlers(
    foregroundHandler?: FCMMessageListener,
    backgroundHandler?: FCMMessageListener,
    notificationOpenedHandler?: FCMNotificationOpenedListener,
  ): void {
    if (!this.isFirebaseAvailable) {
      return
    }

    if (foregroundHandler) {
      this.setForegroundMessageHandler(foregroundHandler)
    }

    if (backgroundHandler) {
      this.setBackgroundMessageHandler(backgroundHandler)
    }

    if (notificationOpenedHandler) {
      this.setNotificationOpenedHandler(notificationOpenedHandler)
    }
  }

  /**
   * Get notification categories for iOS
   */
  async getNotificationCategories(): Promise<any[]> {
    try {
      if (Platform.OS === 'ios') {
        // You can define custom notification categories here
        return []
      }
      return []
    } catch (error) {
      console.error('Failed to get notification categories:', error)
      return []
    }
  }

  /**
   * Set notification categories for iOS
   */
  async setNotificationCategories(categories: any[]): Promise<void> {
    try {
      if (Platform.OS === 'ios') {
        // Set custom notification categories
      }
    } catch (error) {
      console.error('Failed to set notification categories:', error)
    }
  }

  /**
   * Handle token refresh
   */
  onTokenRefresh(callback: (token: string) => void): () => void {
    if (!this.isFirebaseAvailable || !firebaseMessaging) {
      return () => {}
    }

    const messagingInstance = firebaseMessaging.getMessaging()
    return firebaseMessaging.onTokenRefresh(messagingInstance, callback)
  }

  /**
   * Cleanup all listeners
   */
  cleanup(): void {
    if (this.unsubscribeForegroundListener) {
      this.unsubscribeForegroundListener()
      this.unsubscribeForegroundListener = null
    }

    if (this.unsubscribeNotificationOpenedListener) {
      this.unsubscribeNotificationOpenedListener()
      this.unsubscribeNotificationOpenedListener = null
    }

    this.backgroundMessageHandler = null
    this.foregroundMessageHandler = null
    this.notificationOpenedHandler = null
  }

  /**
   * Check if device is registered for remote messages
   */
  async isRegisteredForRemoteMessages(): Promise<boolean> {
    if (!this.isFirebaseAvailable || !firebaseMessaging) {
      return false
    }

    try {
      const messagingInstance = firebaseMessaging.getMessaging()
      return firebaseMessaging.isDeviceRegisteredForRemoteMessages(messagingInstance)
    } catch (error) {
      console.error('Failed to check remote message registration:', error)
      return false
    }
  }

  /**
   * Register device for remote messages
   */
  async registerForRemoteMessages(): Promise<void> {
    if (!this.isFirebaseAvailable || !firebaseMessaging) {
      return
    }

    try {
      const messagingInstance = firebaseMessaging.getMessaging()
      if (!firebaseMessaging.isDeviceRegisteredForRemoteMessages(messagingInstance)) {
        await firebaseMessaging.registerDeviceForRemoteMessages(messagingInstance)
      }
    } catch (error) {
      console.error('Failed to register for remote messages:', error)
      throw error
    }
  }

  /**
   * Unregister device from remote messages
   */
  async unregisterForRemoteMessages(): Promise<void> {
    if (!this.isFirebaseAvailable || !firebaseMessaging) {
      return
    }

    try {
      const messagingInstance = firebaseMessaging.getMessaging()
      if (firebaseMessaging.isDeviceRegisteredForRemoteMessages(messagingInstance)) {
        await firebaseMessaging.unregisterDeviceForRemoteMessages(messagingInstance)
      }
    } catch (error) {
      console.error('Failed to unregister from remote messages:', error)
      throw error
    }
  }

  /**
   * Parse FCM message data
   */
  parseMessageData(remoteMessage: any): FCMMessage {
    return {
      messageId: remoteMessage.messageId || '',
      data: remoteMessage.data as FCMNotificationData,
      notification: remoteMessage.notification
        ? {
            title: remoteMessage.notification.title,
            body: remoteMessage.notification.body,
            imageUrl: remoteMessage.notification.android?.imageUrl,
          }
        : undefined,
      from: remoteMessage.from,
      to: remoteMessage.to,
      collapseKey: remoteMessage.collapseKey,
      sentTime: remoteMessage.sentTime,
      ttl: remoteMessage.ttl,
    }
  }

  /**
   * Check if Firebase is available
   */
  get isAvailable(): boolean {
    return this.isFirebaseAvailable
  }
}

export const fcmService = FCMService.getInstance()
export { FCMService }
