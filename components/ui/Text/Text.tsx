import { typo } from '@/styles/_typography'
import { cn } from '@/utils/cn'
import * as Slot from '@rn-primitives/slot'
import * as React from 'react'
import { Text as RNText } from 'react-native'

const TextClassContext = React.createContext<string | undefined>(undefined)

// Variant mapping: prop value -> Tailwind color class
const variantClassMap = {
  default: 'text-custom-text',
  success: 'text-custom-success-600',
  error: 'text-custom-danger-600',
  warning: 'text-custom-warning-600',
  subdued: 'text-custom-text-subdued',
  disabled: 'text-custom-text-disabled',
  primary: 'text-primary-500',
  white: 'text-white',
} as const

export type VariantKey = keyof typeof variantClassMap

function Text({
  className,
  asChild = false,
  size = 'body4',
  variant = 'default',
  style,

  ...props
}: React.ComponentProps<typeof RNText> & {
  ref?: React.RefObject<RNText>
  asChild?: boolean
  size?: keyof typeof typo
  variant?: VariantKey
}) {
  const textClass = React.useContext(TextClassContext)
  const Component = asChild ? Slot.Text : RNText

  // Ensure size is a valid key
  const validSize = size

  // Use typography styles directly
  const typographyStyle = typo[validSize]

  // Merge styles
  let mergedStyle = [typographyStyle, style]
  if (typo[size]) {
    mergedStyle = [typographyStyle, style]
  }

  // Determine variant class
  const variantClass = variantClassMap[variant] || variantClassMap['default']

  return (
    <Component
      className={cn(variantClass, textClass, className)} // Apply variant class
      style={mergedStyle}
      {...props}
    />
  )
}

export { Text, TextClassContext }
