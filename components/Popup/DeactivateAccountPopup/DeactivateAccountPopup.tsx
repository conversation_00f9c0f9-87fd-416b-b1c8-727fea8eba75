import WarningIcon from '@/assets/icons/warning-2.svg'
import { Button, ButtonText } from '@/components/ui/Button/Button'
import { useDialog } from '@/hooks/dialog/useDialog'

import { useTranslation } from 'react-i18next'
import { Text, View } from 'react-native'
interface DeactivateAccountPopupProps {
  close?: () => void
  onPressDeactivateAccount?: (close?: () => void) => void
}
export const DeactivateAccountPopup = ({
  close,
  onPressDeactivateAccount,
}: DeactivateAccountPopupProps) => {
  const { t } = useTranslation()

  return (
    <View className="flex flex-col items-center justify-center rounded-xl bg-white">
      <WarningIcon width={30} height={30} />
      <Text className="typo-body-3 mt-6 text-center">{t('MES-703')}</Text>
      <Text className="typo-body-7 mt-3">{t('MES-703')}</Text>
      <View className="mt-6 flex w-full flex-col gap-y-2">
        <Button
          className="w-full flex-1 rounded-lg"
          variant="solid"
          action="negative"
          onPress={() => onPressDeactivateAccount?.(close)}
        >
          <ButtonText>{t('MES-169')}</ButtonText>
        </Button>
        <Button
          variant="outline"
          className="w-full flex-1 rounded-lg border-custom-text-disabled"
          onPress={close}
        >
          <ButtonText>{t('MES-224')}</ButtonText>
        </Button>
      </View>
    </View>
  )
}

export const useOpenDeactivateAccountPopup = () => {
  const { openDialog } = useDialog()
  const openDeactivateAccountPopup = (onPressLogout: (close?: () => void) => void) => {
    openDialog({
      children: ({ close }: { close: () => void }) => {
        return (
          <DeactivateAccountPopup
            close={close}
            onPressDeactivateAccount={() => onPressLogout?.(close)}
          />
        )
      },
      variant: 'blank',
      contentSize: 'md',
      bodyClassName: 'p-0 m-0',
    })
  }
  return { openDeactivateAccountPopup }
}
