import { MedicalFacultyWrapper } from '@/features/medical-faculties/containers/MedicalFacultiesWrapper'
import { withAuthentication } from '@/hoc/withAuthentication'

import { SafeAreaView } from 'react-native-safe-area-context'

function MedicalFacultyAppScreen() {
    return (
        <SafeAreaView className="bg-white" style={{ flex: 1 }} edges={['left', 'top', 'right', 'bottom']}>

            <MedicalFacultyWrapper />
        </SafeAreaView>
    )
}

export default withAuthentication(MedicalFacultyAppScreen)
