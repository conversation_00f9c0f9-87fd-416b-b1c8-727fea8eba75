import ArrowLeftIcon from '@/assets/profile/arrow-left.svg'
import CheckIcon from '@/assets/profile/check-icon.svg'
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/FormField/FormField'
import { PasswordInput } from '@/components/ui/PasswordInput/PasswordInput'
import { Text } from '@/components/ui/Text/Text'
import { useLoadingScreen } from '@/hooks/common/useLoadingScreen'
import { userService } from '@/services/user/user.service'
import { zodResolver } from '@hookform/resolvers/zod'
import { useRouter } from 'expo-router'
import React, { useState } from 'react'
import { useForm } from 'react-hook-form'
import { useTranslation } from 'react-i18next'
import { ScrollView, TouchableOpacity, View } from 'react-native'
import Toast from 'react-native-toast-message'
import { z } from 'zod'

export const ChangePasswordForm = () => {
  const { t } = useTranslation()
  const router = useRouter()

  const [isSubmitting, setIsSubmitting] = useState(false)
  const { showLoading, hideLoading } = useLoadingScreen()
  const formSchema = z
    .object({
      currentPassword: z.string().min(1, { message: t('MES-523') }),
      newPassword: z.string().min(8, { message: t('MES-178') }),
      confirmPassword: z.string().min(1, { message: t('MES-179') }),
    })
    .refine((data) => data.newPassword === data.confirmPassword, {
      message: t('MES-117'),
      path: ['confirmPassword'],
    })

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      currentPassword: '',
      newPassword: '',
      confirmPassword: '',
    },
    mode: 'onTouched',
  })

  const handleSave = async (values: z.infer<typeof formSchema>) => {
    setIsSubmitting(true)
    showLoading()
    const { currentPassword, newPassword } = values
    try {
      await userService.changePassword({
        currentPassword,
        password: newPassword,
      })
      Toast.show({
        type: 'success',
        text1: t('MES-181'),
      })
      router.back()
    } catch (error: any) {
      Toast.show({
        type: 'error',
        text1: t('MES-184'),
        text2: error?.message ? t(error?.message, { defaultValue: error?.message }) : '',
      })
      console.error('Password change error:', error)
    } finally {
      setIsSubmitting(false)
      hideLoading()
    }
  }

  const handleBack = () => {
    router.back()
  }

  return (
    <>
      <ChangePasswordHeader onBack={handleBack} onSave={form.handleSubmit(handleSave)} />

      <ScrollView showsVerticalScrollIndicator={false}>
        <Form {...form}>
          <View className="gap-4 p-4">
            <FormField
              control={form.control}
              name="currentPassword"
              render={({ field }) => (
                <FormItem className="flex flex-col gap-1.5">
                  <FormLabel required>{t('MES-110')}</FormLabel>
                  <FormControl>
                    <View className="relative w-full">
                      <PasswordInput
                        placeholder={t('MES-653')}
                        onChangeText={field.onChange}
                        onBlur={field.onBlur}
                        value={field.value}
                        editable={!isSubmitting}
                      />
                    </View>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="newPassword"
              render={({ field }) => (
                <FormItem className="flex flex-col gap-1.5">
                  <FormLabel required>{t('MES-118')}</FormLabel>
                  <FormControl>
                    <PasswordInput
                      placeholder={t('MES-161')}
                      onChangeText={field.onChange}
                      onBlur={field.onBlur}
                      value={field.value}
                      editable={!isSubmitting}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="confirmPassword"
              render={({ field }) => (
                <FormItem className="flex flex-col gap-1.5">
                  <FormLabel required>{t('MES-119')}</FormLabel>
                  <FormControl>
                    <PasswordInput
                      placeholder={t('MES-116')}
                      onChangeText={field.onChange}
                      onBlur={field.onBlur}
                      value={field.value}
                      editable={!isSubmitting}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </View>
        </Form>
      </ScrollView>
    </>
  )
}

// Header

interface EditProfileHeaderProps {
  onBack: () => void
  onSave: () => void
}

export const ChangePasswordHeader = ({ onBack, onSave }: EditProfileHeaderProps) => {
  const { t } = useTranslation()
  return (
    <View className="flex-row items-center justify-between bg-white px-4 py-3">
      <TouchableOpacity
        className="h-6 w-6 items-center justify-center"
        onPress={onBack}
        hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
      >
        <ArrowLeftIcon width={16} height={16} />
      </TouchableOpacity>

      <Text size="body3" variant="primary">
        {t('MES-61')}
      </Text>

      <TouchableOpacity
        className="h-6 w-6 items-center justify-center"
        onPress={onSave}
        hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
      >
        <CheckIcon width={16} height={16} />
      </TouchableOpacity>
    </View>
  )
}
