import MessageIcon from '@/assets/icons/message-icon.svg'
import { Text } from '@/components/ui/Text/Text'
import { APP_ROUTES } from '@/routes/appRoutes'
import { Link, LinkProps } from 'expo-router'
import { useTranslation } from 'react-i18next'
import { TouchableOpacity, View } from 'react-native'
export const TranslatorContact = () => {
  const { t } = useTranslation()

  return (
    <View className="my-2 px-4">
      <View
        className="flex flex-row items-center justify-between gap-x-2 rounded-lg bg-primary-50 p-4"
        style={{
          boxShadow: '2px 2px 8px 0px #00000014',
        }}
      >
        <Text size="body6" variant="primary">
          {t('MES-633')}
        </Text>
        <Link href={APP_ROUTES.CHAT_BOT?.children?.CHAT_WOOT?.path as LinkProps['href']} asChild>
          <TouchableOpacity className="flex min-w-[90px]  flex-row items-center justify-center gap-x-2 rounded-lg bg-primary p-2 ">
            <MessageIcon width={16} height={16} />
            <Text size="button5" variant="white">
              {t('MES-634')}
            </Text>
          </TouchableOpacity>
        </Link>
      </View>
    </View>
  )
}
