import { PRIMARY_LANG } from '@/constants/storage-key.constant'
import { LocaleEnum } from '@/enums/locale.enum'
import ja from '@/messages/ja.json'
import vi from '@/messages/vi.json'
import AsyncStorage from '@react-native-async-storage/async-storage'
import * as Localization from 'expo-localization'
import i18n from 'i18next'
import { initReactI18next } from 'react-i18next'

const resources = {
  vi: {
    translation: vi,
  },
  ja: {
    translation: ja,
  },
}

// We wrap init in a Promise to delay app rendering until i18n is ready (optional but good practice)
const initI18n = async () => {
  let savedLanguage

  try {
    savedLanguage = await AsyncStorage.getItem(PRIMARY_LANG)
  } catch (err) {
    console.warn('Error reading saved language:', err)
  }

  if (!savedLanguage) {
    savedLanguage = Localization.getLocales()[0]?.languageCode || 'vi'
  }

  await i18n.use(initReactI18next).init({
    resources,
    lng: savedLanguage,
    fallbackLng: LocaleEnum.VI,
    interpolation: {
      escapeValue: false,
    },
  })
}

export { initI18n }
export default i18n
