import { configureReanimatedLogger, ReanimatedLogLevel } from 'react-native-reanimated'

// Utility to run code with strict mode off
export function withReanimatedStrictOff<T>(fn: () => T): T {
  const prevConfig = { level: ReanimatedLogLevel.warn, strict: true }

  // Disable strict temporarily
  configureReanimatedLogger({ level: ReanimatedLogLevel.warn, strict: false })
  const result = fn()

  // Restore
  configureReanimatedLogger(prevConfig)
  return result
}
