import ClearInputIcon from '@/assets/icons/clear-input-icon.svg'
import { TextInput } from '@/components/ui/TextInput/TextInput'
import { colors } from '@/styles/_colors'
import { cn } from '@/utils/cn'
import { forwardRef, useImperativeHandle, useRef, useState } from 'react'
import {
  ColorValue,
  TextInput as RNTextInput,
  TextInputProps as RNTextInputProps,
  TouchableOpacity,
  View,
} from 'react-native'

export interface TextInputWithClearProps extends RNTextInputProps {
  placeholder?: string
  value?: string
  onChangeText?: (text: string) => void
  onSubmitEditing?: () => void
  onClear?: () => void
  className?: string
  wrapperClassName?: string
  autoFocus?: boolean
  placeholderTextColor?: string
  defaultValue?: string
}

export interface TextInputWithClearRef {
  focus: () => void
  blur: () => void
  clear: () => void
}

export const TextInputWithClear = forwardRef<TextInputWithClearRef, TextInputWithClearProps>(
  (
    {
      placeholder,
      value: controlledValue,
      onChangeText,
      onSubmitEditing,
      onClear,
      className,
      wrapperClassName,
      style,
      autoFocus = false,
      placeholderTextColor = '#8B8C99',
      defaultValue,
      ...props
    },
    ref,
  ) => {
    const [internalValue, setInternalValue] = useState(defaultValue || '')
    const TextInputWithClearRef = useRef<RNTextInput>(null)

    const searchValue = controlledValue !== undefined ? controlledValue : internalValue
    const isControlled = controlledValue !== undefined

    const handleTextInputWithClearChange = (text: string) => {
      if (!isControlled) {
        setInternalValue(text)
      }
      onChangeText?.(text)
    }

    const handleClearTextInputWithClear = () => {
      if (!isControlled) {
        setInternalValue('')
      }
      onClear?.()
    }

    const handleSubmitEditing = () => {
      onSubmitEditing?.()
    }

    useImperativeHandle(ref, () => ({
      focus: () => TextInputWithClearRef.current?.focus(),
      blur: () => TextInputWithClearRef.current?.blur(),
      clear: handleClearTextInputWithClear,
    }))

    return (
      <View
        className={cn(
          'flex-1 flex-row items-center gap-x-2 overflow-hidden rounded-lg border border-custom-divider bg-white px-3',
          wrapperClassName,
        )}
        style={[
          {
            borderColor: searchValue
              ? (colors.primary[500] as unknown as ColorValue)
              : (colors.divider['border'] as unknown as ColorValue),
          },
        ]}
      >
        <TextInput
          ref={TextInputWithClearRef}
          placeholder={placeholder}
          placeholderTextColor={placeholderTextColor}
          value={searchValue}
          onChangeText={handleTextInputWithClearChange}
          onSubmitEditing={handleSubmitEditing}
          autoFocus={autoFocus}
          className={cn('p-0 pr-3', className)}
          wrapperClassName={cn('flex-1 !px-0 !border-0 !border-none border-transparent')}
          defaultValue={defaultValue}
          style={style} // Pass through style prop for height control
          {...props}
        />
        {searchValue && (
          <TouchableOpacity hitSlop={10} onPress={handleClearTextInputWithClear}>
            <ClearInputIcon width={16} height={16} />
          </TouchableOpacity>
        )}
      </View>
    )
  },
)

TextInputWithClear.displayName = 'TextInputWithClear'
