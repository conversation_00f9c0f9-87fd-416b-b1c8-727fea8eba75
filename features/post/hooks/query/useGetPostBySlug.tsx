import { postService } from '@/features/post/services/post.service'
import { Params } from '@/types/http.type'

import { UseQueryOptions, useQuery } from '@tanstack/react-query'
import { AxiosRequestConfig } from 'axios'
import { Post } from '../../types'
import { postQueryKeys } from './queryKeys'

export const useGetPostBySlug = ({
  slug,
  params = {},
  options = {},
  useQueryOptions,
  overrideKey,
}: {
  slug: string
  params?: Params
  options?: AxiosRequestConfig
  useQueryOptions?: Omit<UseQueryOptions<Post | null>, 'queryKey' | 'queryFn'>
  overrideKey?: (string | Params)[]
}) => {
  const {
    isError: isGetPostBySlugError,
    isPending: isGetPostBySlugLoading,
    data: postDetailsBySlug,
    ...rest
  } = useQuery({
    queryKey: overrideKey
      ? overrideKey
      : [postQueryKeys['post-details-by-slug'].base(), slug, params],
    queryFn: async () =>
      postService.getPostDetailsBySlug({
        slug: slug,
        params: params,
        options: options,
      }),
    ...useQueryOptions,
  })
  return {
    isGetPostBySlugError,
    isGetPostBySlugLoading,
    postDetailsBySlug,
    ...rest,
  }
}
