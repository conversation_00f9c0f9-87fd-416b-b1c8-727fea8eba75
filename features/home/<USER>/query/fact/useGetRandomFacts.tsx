import { factService } from '@/features/home/<USER>/fact.service'
import { Fact } from '@/types/fact.type'
import { PaginatedDocs } from '@/types/global.type'
import { Params } from '@/types/http.type'
import { UseQueryOptions, useQuery } from '@tanstack/react-query'
import { AxiosRequestConfig } from 'axios'
import { factQueryKeys } from './queryKeys'

export const useGetRandomFacts = ({
  params = {},
  options = {},
  useQueryOptions,
}: {
  params?: Params
  options?: AxiosRequestConfig
  useQueryOptions?: Omit<UseQueryOptions<PaginatedDocs<Fact> | null>, 'queryKey' | 'queryFn'>
} = {}) => {
  const {
    isError: isGetRandomFactsError,
    isPending: isGetRandomFactsLoading,
    data: randomFacts,
    ...rest
  } = useQuery({
    queryKey: [factQueryKeys['randomFacts'].base(), params],
    queryFn: async () =>
      factService.getRandomFacts({
        params: params,
        options: options,
      }),
    ...useQueryOptions,
  })
  return {
    isGetRandomFactsError,
    isGetRandomFactsLoading,
    randomFacts,
    ...rest,
  }
}
