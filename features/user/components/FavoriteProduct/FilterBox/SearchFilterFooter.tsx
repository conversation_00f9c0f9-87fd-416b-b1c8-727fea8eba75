import { BottomSheetFooter, BottomSheetFooterProps } from '@gorhom/bottom-sheet'
import * as Haptics from 'expo-haptics'
import { forwardRef, useMemo } from 'react'
import { useTranslation } from 'react-i18next'
import { View } from 'react-native'
import { useShallow } from 'zustand/react/shallow'

import {
  BaseFilterFooter,
  type BaseFilterItem,
} from '@/components/Filter/BaseFilterFooter/BaseFilterFooter'
import {
  FavoriteProductFilterItem,
  FavoriteProductFilterType,
  useFavoriteProductStore,
} from '@/features/user/stores/FavoriteProductStore'

interface SearchFilterFooterProps extends BottomSheetFooterProps {
  closeSheet?: () => void
}

export const SearchFilterFooter = forwardRef<View, SearchFilterFooterProps>(
  ({ closeSheet, ...props }, ref) => {
    return (
      <BottomSheetFooter {...props}>
        <SearchFilterFooterContent ref={ref} closeSheet={closeSheet} {...props} />
      </BottomSheetFooter>
    )
  },
)

interface SearchFilterFooterContentProps extends BottomSheetFooterProps {
  closeSheet?: () => void
}

const SearchFilterFooterContent = forwardRef<View, SearchFilterFooterContentProps>(
  ({ closeSheet }, ref) => {
    const { t } = useTranslation()
    const {
      tempSearchFilters,
      applyFavoriteTempSearchFilters,
      clearAllFavoriteTempSearchFilters,
      toggleFavoriteTempSearchFilter,
    } = useFavoriteProductStore(
      useShallow((state) => ({
        tempSearchFilters: state.tempSearchFilters,
        applyFavoriteTempSearchFilters: state.applyFavoriteTempSearchFilters,
        clearAllFavoriteTempSearchFilters: state.clearAllFavoriteTempSearchFilters,
        toggleFavoriteTempSearchFilter: state.toggleFavoriteTempSearchFilter,
      })),
    )

    const handleClearFilter = (filterItem: BaseFilterItem) => {
      const favoriteFilterItem = filterItem as FavoriteProductFilterItem

      // Handle different filter types
      if (favoriteFilterItem.type === FavoriteProductFilterType.PRODUCT_TYPE) {
        const currentFilters = tempSearchFilters[FavoriteProductFilterType.PRODUCT_TYPE] || []
        const isSelected = currentFilters.some((f) => f.id === favoriteFilterItem.id)

        if (isSelected) {
          // Remove the product type filter
          toggleFavoriteTempSearchFilter(FavoriteProductFilterType.PRODUCT_TYPE, favoriteFilterItem)
        }
      } else if (favoriteFilterItem.type === FavoriteProductFilterType.CATEGORY) {
        const currentFilters = tempSearchFilters[FavoriteProductFilterType.CATEGORY] || []
        const isSelected = currentFilters.some((f) => f.id === favoriteFilterItem.id)

        if (isSelected) {
          toggleFavoriteTempSearchFilter(FavoriteProductFilterType.CATEGORY, favoriteFilterItem)
        }
      } else if (
        favoriteFilterItem.type === FavoriteProductFilterType.AGE_GROUP ||
        favoriteFilterItem.type === FavoriteProductFilterType.MEDICINE_TYPE
      ) {
        const currentFilters = tempSearchFilters[favoriteFilterItem.type] || []
        const isSelected = currentFilters.some((f) => f.id === favoriteFilterItem.id)

        if (isSelected) {
          toggleFavoriteTempSearchFilter(favoriteFilterItem.type, favoriteFilterItem)
        }
      }
    }

    const handleApplyFilters = () => {
      // Apply temporary filters to actual search filters
      applyFavoriteTempSearchFilters()
      closeSheet?.()
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light)
    }

    const handleResetFilters = () => {
      clearAllFavoriteTempSearchFilters()
    }

    const activeFilters = useMemo(() => {
      const filters: FavoriteProductFilterItem[] = []

      // Add age group filters
      if (tempSearchFilters[FavoriteProductFilterType.AGE_GROUP]) {
        filters.push(...tempSearchFilters[FavoriteProductFilterType.AGE_GROUP]!)
      }

      // Add medicine type filters
      if (tempSearchFilters[FavoriteProductFilterType.MEDICINE_TYPE]) {
        filters.push(...tempSearchFilters[FavoriteProductFilterType.MEDICINE_TYPE]!)
      }

      // Add product type filters
      if (tempSearchFilters[FavoriteProductFilterType.PRODUCT_TYPE]) {
        filters.push(...tempSearchFilters[FavoriteProductFilterType.PRODUCT_TYPE]!)
      }

      // Add category filters
      if (tempSearchFilters[FavoriteProductFilterType.CATEGORY]) {
        filters.push(...tempSearchFilters[FavoriteProductFilterType.CATEGORY]!)
      }

      return filters
    }, [tempSearchFilters])

    return (
      <BaseFilterFooter
        ref={ref}
        activeFilters={activeFilters}
        onClearFilter={handleClearFilter}
        maxDisplayCount={5}
        onApply={handleApplyFilters}
        onReset={handleResetFilters}
        applyText={t('MES-281')}
        resetText={t('MES-105')}
        selectedFiltersLabel={t('MES-706')}
        showLessText={t('MES-493')}
      />
    )
  },
)
