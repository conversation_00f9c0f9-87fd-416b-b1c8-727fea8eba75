import StarIcon from '@/assets/icons/start-icon.svg'
import { Text } from '@/components/ui/Text/Text'
import { useTranslation } from 'react-i18next'
import { View } from 'react-native'

export const SearchTipsBox = () => {
  const { t } = useTranslation()
  return (
    <View className="flex flex-row items-start gap-x-2 rounded-[6px] bg-primary-50 p-3">
      <StarIcon></StarIcon>
      <View className="flex flex-1 flex-col gap-y-1 overflow-hidden">
        <Text size="body6" variant="primary">
          {t('MES-555')}
        </Text>
        <Text size="body9">{t('MES-810')}</Text>
      </View>
    </View>
  )
}
