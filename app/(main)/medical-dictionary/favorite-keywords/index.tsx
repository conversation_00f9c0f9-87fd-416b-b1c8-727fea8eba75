import { StackScreenBase } from '@/components/StackBase/StackScreenBase'
import MedicalDictionaryFavoriteKeywordsScreen from '@/features/medical-dictionary/screens/MedicalDictionaryFavoriteKeywordsScreen/MedicalDictionaryFavoriteKeywordsScreen'
import { withAuthentication } from '@/hoc/withAuthentication'

import { useTranslation } from 'react-i18next'
function MedicalDictionaryFavoriteKeywordsAppScreen() {
  const { t } = useTranslation()
  return (
    <>
      <StackScreenBase
        options={{
          headerTitle: t('MES-773'),
        }}
      />
      <MedicalDictionaryFavoriteKeywordsScreen></MedicalDictionaryFavoriteKeywordsScreen>
    </>
  )
}
export default withAuthentication(MedicalDictionaryFavoriteKeywordsAppScreen)
