import { Text } from '@/components/ui/Text/Text'
import { APP_ROUTES, AppRoutesEnum } from '@/routes/appRoutes'
import colors from '@/styles/_colors'
import { generateBackgroundColor } from '@/utils/color'
import * as Haptics from 'expo-haptics'
import { Link } from 'expo-router'
import { TouchableOpacity, View } from 'react-native'
import Svg, { Path } from 'react-native-svg'
import { MedicalDocumentCategory } from '../../types'
interface MedicalDocumentCategoryItemProps {
  medicalDocumentCategory: MedicalDocumentCategory
  customParams?: Record<string, string>
}
export const MedicalDocumentCategoryItem = ({
  medicalDocumentCategory,
  customParams,
}: MedicalDocumentCategoryItemProps) => {
  const { id, color, name } = medicalDocumentCategory || {}
  return (
    <Link
      href={{
        pathname:
          APP_ROUTES.MEDICAL_DOCUMENTS.children?.[AppRoutesEnum.MEDICAL_DOCUMENTS_LIST].path,
        params: {
          id,
          ...customParams,
        },
      }}
      asChild
      onPress={() => {
        Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light)
      }}
    >
      <TouchableOpacity
        className="flex min-h-[96px] flex-col justify-center gap-y-2 rounded-lg  p-3"
        style={{
          backgroundColor: generateBackgroundColor(color || (colors.primary[500] as string)),
        }}
      >
        <Svg width={24} height={24} viewBox="0 0 24 24" fill="none">
          <Path
            d="M21.0169 7.99175C21.4148 8.55833 20.9405 9.25 20.2482 9.25H3C2.44772 9.25 2 8.80228 2 8.25V6.42C2 3.98 3.98 2 6.42 2H8.74C10.37 2 10.88 2.53 11.53 3.4L12.93 5.26C13.24 5.67 13.28 5.72 13.86 5.72H16.65C18.4546 5.72 20.0516 6.61709 21.0169 7.99175Z"
            fill={color || colors.primary[500]}
          />
          <Path
            d="M20.9834 10.7497C21.5343 10.7497 21.9815 11.1954 21.9834 11.7463L22 16.65C22 19.6 19.6 22 16.65 22H7.35C4.4 22 2 19.6 2 16.65V11.75C2 11.1977 2.44771 10.75 2.99999 10.75L20.9834 10.7497Z"
            fill={color || colors.primary[500]}
          />
        </Svg>
        <View className="h-[44px] overflow-hidden">
          <Text size="body6" variant="default" numberOfLines={2}>
            {name}
          </Text>
        </View>
      </TouchableOpacity>
    </Link>
  )
}
