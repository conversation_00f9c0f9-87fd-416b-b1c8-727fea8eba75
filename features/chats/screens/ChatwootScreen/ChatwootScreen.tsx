'use client'
import { LocaleEnum } from '@/enums/locale.enum'
import { useMarkAllChatNotificationsAsViewed } from '@/features/notification/hooks/query/useMarkAllChatNotificationsAsViewed'
import { useAuthentication } from '@/hooks/auth/useAuthentication'
import { useAppLanguage } from '@/hooks/common/useAppLanguage'
import { Media } from '@/types/media.type'
import ChatWootWidget from '@chatwoot/react-native-widget'
import { useRouter } from 'expo-router'
import React, { useEffect, useState } from 'react'
import { Keyboard, TouchableWithoutFeedback, View } from 'react-native'
import { SafeAreaView } from 'react-native-safe-area-context'

export const ChatWootScreen = () => {
  const { user: userResponse } = useAuthentication()
  const router = useRouter()
  const { primaryLanguage: locale } = useAppLanguage()
  const [isModalVisible, setIsModalVisible] = useState(false)
  const avatarMedia = userResponse?.avatar as Media
  const avatarURL = avatarMedia?.url || avatarMedia?.thumbnailURL || userResponse?.oauthAvatar || ''

  const user = {
    identifier: userResponse?.email,
    name: userResponse?.name,
    avatar_url: avatarURL,
    email: userResponse?.email,
    identifier_hash: '',
  }

  const customAttributes = { userId: userResponse?.id }
  const websiteToken = process.env.EXPO_PUBLIC_TOKEN_CHATWOOT ?? ''
  const baseUrl = process.env.EXPO_PUBLIC_BASE_URL_CHATWOOT ?? ''
  const colorScheme = 'light'
  const dismissKeyboard = () => {
    Keyboard.dismiss()
    Keyboard.removeAllListeners('')
  }
  // Mark all chat notifications as viewed
  const { markAllChatNotificationsAsViewedMutation } = useMarkAllChatNotificationsAsViewed()
  useEffect(() => {
    setIsModalVisible(!!user)
  }, [])

  useEffect(() => {
    return () => {
      markAllChatNotificationsAsViewedMutation()
    }
  }, [])
  if (!websiteToken || !baseUrl) {
    return <></>
  }

  return (
    <SafeAreaView className="flex-1 bg-white">
      <TouchableWithoutFeedback onPress={dismissKeyboard} accessible={false}>
        <View className="flex-1">
          <ChatWootWidget
            websiteToken={websiteToken}
            locale={locale ?? LocaleEnum.VI}
            baseUrl={baseUrl}
            closeModal={() => {
              setIsModalVisible(false)
              router.back()
            }}
            isModalVisible={isModalVisible}
            user={user}
            customAttributes={customAttributes}
            colorScheme={colorScheme}
          />
        </View>
      </TouchableWithoutFeedback>
    </SafeAreaView>
  )
}
