import React, { useEffect } from 'react'
import { Text } from '../Text/Text'
import { Dialog, DialogBackdrop, DialogBody, DialogContent, DialogHeader } from './Dialog'

interface DynamicDialogProps {
  isOpen?: boolean
  id: string
  title?: string
  data?: any
  closeDialog: (params?: any) => void
  wrapperClassName?: string
  variant?: 'default' | 'blank'
  modal?: boolean
  onOpen?: () => void
  children: React.ReactNode
  bodyClassName?: string
  contentSize?: 'xs' | 'sm' | 'md' | 'lg' | 'full'
}

export function DynamicDialog({
  isOpen = false,
  title,
  closeDialog,
  wrapperClassName,
  variant = 'default',
  modal = true,
  onOpen,
  children,
  bodyClassName,
  contentSize = 'md',
}: DynamicDialogProps) {
  // React Native Reanimated now handles all animations in Dialog component

  useEffect(() => {
    if (isOpen && onOpen) {
      onOpen()
    }
  }, [isOpen, onOpen])

  const handleBackdropPress = () => {
    if (modal !== false) {
      closeDialog()
    }
  }

  if (!isOpen) {
    return null
  }

  return (
    <Dialog isOpen={isOpen}>
      <DialogBackdrop onPress={handleBackdropPress} />
      <DialogContent className={wrapperClassName} size={contentSize}>
        {variant !== 'blank' && title && (
          <DialogHeader>
            <Text className="text-lg font-semibold text-typography-900">{title}</Text>
          </DialogHeader>
        )}
        <DialogBody className={bodyClassName}>{children}</DialogBody>
      </DialogContent>
    </Dialog>
  )
}
