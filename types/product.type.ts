import { MedicineBuyButton, MedicineType } from '@/features/product/types'
import { BodyPart } from './body-part.type'
import { Keyword } from './keyword.type'
import { Media } from './media.type'
import { PatientGroup } from './patient.group'
import { Subscription } from './subscription.type'
export interface MedicineCategory {
  id: string
  title: string
  bodyPart?: (string | BodyPart)[] | null
  slug?: string | null
  slugLock?: boolean | null
  updatedAt: string
  createdAt: string
}

export interface DietarySupplementCategory {
  id: string
  title: string
  image?: (string | null) | Media
  slug?: string | null
  slugLock?: boolean | null
  updatedAt: string
  createdAt: string
}

export interface Medicine {
  id: string
  title: string
  description?: string | null
  availableIn: (string | Subscription)[]
  isDietarySupplement: boolean
  patientGroup: (string | PatientGroup)[]
  type?: (string | MedicineType)[] | null
  featured?: boolean | null
  stores?:
    | {
        'medicine-store'?: (string | null) | MedicineBuyButton
        url?: string | null
        id?: string | null
      }[]
    | null
  heroImage: string | Media
  uses?: {
    root: {
      type: string
      children: {
        type: string
        version: number
        [k: string]: unknown
      }[]
      direction: ('ltr' | 'rtl') | null
      format: 'left' | 'start' | 'center' | 'right' | 'end' | 'justify' | ''
      indent: number
      version: number
    }
    [k: string]: unknown
  } | null
  dosageForm?: {
    root: {
      type: string
      children: {
        type: string
        version: number
        [k: string]: unknown
      }[]
      direction: ('ltr' | 'rtl') | null
      format: 'left' | 'start' | 'center' | 'right' | 'end' | 'justify' | ''
      indent: number
      version: number
    }
    [k: string]: unknown
  } | null
  specification?: {
    root: {
      type: string
      children: {
        type: string
        version: number
        [k: string]: unknown
      }[]
      direction: ('ltr' | 'rtl') | null
      format: 'left' | 'start' | 'center' | 'right' | 'end' | 'justify' | ''
      indent: number
      version: number
    }
    [k: string]: unknown
  } | null
  ingredient?: {
    root: {
      type: string
      children: {
        type: string
        version: number
        [k: string]: unknown
      }[]
      direction: ('ltr' | 'rtl') | null
      format: 'left' | 'start' | 'center' | 'right' | 'end' | 'justify' | ''
      indent: number
      version: number
    }
    [k: string]: unknown
  } | null
  dosage?: {
    root: {
      type: string
      children: {
        type: string
        version: number
        [k: string]: unknown
      }[]
      direction: ('ltr' | 'rtl') | null
      format: 'left' | 'start' | 'center' | 'right' | 'end' | 'justify' | ''
      indent: number
      version: number
    }
    [k: string]: unknown
  } | null
  contraindications?: {
    root: {
      type: string
      children: {
        type: string
        version: number
        [k: string]: unknown
      }[]
      direction: ('ltr' | 'rtl') | null
      format: 'left' | 'start' | 'center' | 'right' | 'end' | 'justify' | ''
      indent: number
      version: number
    }
    [k: string]: unknown
  } | null
  note?: {
    root: {
      type: string
      children: {
        type: string
        version: number
        [k: string]: unknown
      }[]
      direction: ('ltr' | 'rtl') | null
      format: 'left' | 'start' | 'center' | 'right' | 'end' | 'justify' | ''
      indent: number
      version: number
    }
    [k: string]: unknown
  } | null
  keywords?: (string | Keyword)[] | null
  relatedMedicines?: (string | Medicine)[] | null
  /**
   * For internal use only: helps categorize entries by body part.
   */
  bodyPart?: (string | BodyPart)[] | null
  categories?: (string | MedicineCategory)[] | null
  dietarySupplementCategories?: (string | DietarySupplementCategory)[] | null
  meta?: {
    title?: string | null
    /**
     * Maximum upload file size: 12MB. Recommended file size for images is <500KB.
     */
    image?: (string | null) | Media
    description?: string | null
  }
  publishedAt?: string | null
  unverified?: boolean | null
  AIAnalyzed?: {
    root: {
      type: string
      children: {
        type: string
        version: number
        [k: string]: unknown
      }[]
      direction: ('ltr' | 'rtl') | null
      format: 'left' | 'start' | 'center' | 'right' | 'end' | 'justify' | ''
      indent: number
      version: number
    }
    [k: string]: unknown
  } | null
  slug?: string | null
  slugLock?: boolean | null
  updatedAt: string
  createdAt: string
  isFavorite?: boolean | null
}
