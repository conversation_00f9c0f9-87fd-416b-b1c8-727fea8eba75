import { useLocalSearchParams } from 'expo-router'
import React from 'react'

import { BaseWebView } from '@/components/BaseWebView/BaseWebView'
import { StackScreenBase } from '@/components/StackBase/StackScreenBase'
import { WEBVIEW_APP_ROUTES } from '@/routes/webviewRoutes'
import { useTranslation } from 'react-i18next'

export default function ProductsListScreen() {
  const { id, b, s, type } = useLocalSearchParams<{
    id: string
    b: string
    s: string
    type: string
  }>()
  const { t } = useTranslation()
  const basePath = WEBVIEW_APP_ROUTES.PRODUCTS?.children?.LIST.path || ''
  const uri = `${basePath}?b=${b}&s=${s || 'false'}&${type === 'medicine' ? 'c' : 'sc'}=${encodeURIComponent(id ?? '')}`

  return (
    <>
      <StackScreenBase
        options={{
          headerTitle: t('MES-665'),
        }}
      />
      <BaseWebView source={{ uri }} />
    </>
  )
}
