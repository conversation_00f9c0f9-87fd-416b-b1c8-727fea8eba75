import { API_ENDPOINTS } from '@/constants/endpoint.constant'

import { httpService } from '@/services/http/http.service'
import { PaginatedDocs } from '@/types/global.type'
import { Params } from '@/types/http.type'
import { AxiosRequestConfig } from 'axios'
import {
  CreateHospitalExaminationPayload,
  CreateHospitalExaminationResponse,
  ExaminationForm,
  Hospital,
} from '../types'

// SERVER / CLIENT
class HospitalPartnerService {
  private static instance: HospitalPartnerService

  private constructor() {}

  public static getInstance(): HospitalPartnerService {
    if (!HospitalPartnerService.instance) {
      HospitalPartnerService.instance = new HospitalPartnerService()
    }
    return HospitalPartnerService.instance
  }

  async getHospitalPartners({
    params = {},
    options = {},
  }: {
    params?: Params
    options?: AxiosRequestConfig
  } = {}): Promise<PaginatedDocs<Hospital>> {
    const data = await httpService.get<PaginatedDocs<Hospital>>(
      `/${API_ENDPOINTS.hospitals_api}/list`,
      {
        params,
        ...options,
      },
    )
    return data
  }

  async getHospital({
    id,
    params = {},
    options = {},
  }: {
    id: string
    params?: Params
    options?: AxiosRequestConfig
  }): Promise<Hospital> {
    const data = await httpService.get<Hospital>(`/${API_ENDPOINTS.hospitals_api}/details/${id}`, {
      params,
      ...options,
    })
    return data
  }

  async getHospitalExaminationForms({
    params = {},
    options = {},
  }: {
    params?: Params
    options?: AxiosRequestConfig
  }): Promise<PaginatedDocs<ExaminationForm>> {
    const data = await httpService.get<PaginatedDocs<ExaminationForm>>(
      `/${API_ENDPOINTS.examination_forms_api}`,
      {
        params,
        ...options,
      },
    )
    return data
  }

  async createHospitalExamination(
    payload: CreateHospitalExaminationPayload,
    options?: AxiosRequestConfig,
  ) {
    const formData = new FormData()
    formData.append('_payload', JSON.stringify(payload))
    const data = await httpService.post<CreateHospitalExaminationResponse>(
      `/${API_ENDPOINTS.user_examinations_api}/create`,
      formData,
      options,
    )
    return data
  }
}

export const hospitalPartnerService = HospitalPartnerService.getInstance()
