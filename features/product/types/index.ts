import { Keyword } from '@/types/keyword.type'
import { Media } from '@/types/media.type'

import { Subscription } from '@/types/subscription.type'

/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "products".
 */
export interface Product {
  id: string
  title: string
  jaTitle?: string | null
  description?: string | null
  availableIn: (string | Subscription)[]
  type: 'MEDICINE' | 'DIETARY_SUPPLEMENT' | 'MEDICAL_INTRUMENT'
  ageGroups?: (string | ProductAgeGroup)[] | null
  medicineType?: (string | MedicineType)[] | null
  categories: (string | ProductCategory)[]
  featured?: boolean | null
  stores?:
    | {
        'medicine-store'?: (string | null) | MedicineBuyButton
        url?: string | null
        id?: string | null
      }[]
    | null
  heroImage: string | Media
  uses?: {
    root: {
      type: string
      children: {
        type: string
        version: number
        [k: string]: unknown
      }[]
      direction: ('ltr' | 'rtl') | null
      format: 'left' | 'start' | 'center' | 'right' | 'end' | 'justify' | ''
      indent: number
      version: number
    }
    [k: string]: unknown
  } | null
  dosageForm?: {
    root: {
      type: string
      children: {
        type: string
        version: number
        [k: string]: unknown
      }[]
      direction: ('ltr' | 'rtl') | null
      format: 'left' | 'start' | 'center' | 'right' | 'end' | 'justify' | ''
      indent: number
      version: number
    }
    [k: string]: unknown
  } | null
  specification?: {
    root: {
      type: string
      children: {
        type: string
        version: number
        [k: string]: unknown
      }[]
      direction: ('ltr' | 'rtl') | null
      format: 'left' | 'start' | 'center' | 'right' | 'end' | 'justify' | ''
      indent: number
      version: number
    }
    [k: string]: unknown
  } | null
  ingredient?: {
    root: {
      type: string
      children: {
        type: string
        version: number
        [k: string]: unknown
      }[]
      direction: ('ltr' | 'rtl') | null
      format: 'left' | 'start' | 'center' | 'right' | 'end' | 'justify' | ''
      indent: number
      version: number
    }
    [k: string]: unknown
  } | null
  dosage?: {
    root: {
      type: string
      children: {
        type: string
        version: number
        [k: string]: unknown
      }[]
      direction: ('ltr' | 'rtl') | null
      format: 'left' | 'start' | 'center' | 'right' | 'end' | 'justify' | ''
      indent: number
      version: number
    }
    [k: string]: unknown
  } | null
  contraindications?: {
    root: {
      type: string
      children: {
        type: string
        version: number
        [k: string]: unknown
      }[]
      direction: ('ltr' | 'rtl') | null
      format: 'left' | 'start' | 'center' | 'right' | 'end' | 'justify' | ''
      indent: number
      version: number
    }
    [k: string]: unknown
  } | null
  note?: {
    root: {
      type: string
      children: {
        type: string
        version: number
        [k: string]: unknown
      }[]
      direction: ('ltr' | 'rtl') | null
      format: 'left' | 'start' | 'center' | 'right' | 'end' | 'justify' | ''
      indent: number
      version: number
    }
    [k: string]: unknown
  } | null
  keywords?: (string | Keyword)[] | null
  relatedProducts?: (string | Product)[] | null
  meta?: {
    title?: string | null
    /**
     * Maximum upload file size: 12MB. Recommended file size for images is <500KB.
     */
    image?: (string | null) | Media
    description?: string | null
  }
  publishedAt?: string | null
  unverified?: boolean | null
  AIAnalyzed?: {
    root: {
      type: string
      children: {
        type: string
        version: number
        [k: string]: unknown
      }[]
      direction: ('ltr' | 'rtl') | null
      format: 'left' | 'start' | 'center' | 'right' | 'end' | 'justify' | ''
      indent: number
      version: number
    }
    [k: string]: unknown
  } | null
  slug?: string | null
  slugLock?: boolean | null
  difyDocumentId?: string | null
  mergedText?: string | null
  /**
   * Use this field to trigger Dify document sync (support bulk edit)
   */
  triggerDifyDocumentSync?: boolean | null
  updatedAt: string
  createdAt: string
  isFavorite?: boolean | null
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "product-age-groups".
 */
export interface ProductAgeGroup {
  id: string
  title: string
  icon?: (string | null) | Media
  updatedAt: string
  createdAt: string
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "product-categories".
 */
export interface ProductCategory {
  id: string
  title: string
  icon?: (string | null) | Media
  parent?: (string | null) | ProductCategory
  /**
   * Left boundary for nested set model (auto-calculated)
   */
  lft?: number | null
  /**
   * Right boundary for nested set model (auto-calculated)
   */
  rgt?: number | null
  /**
   * Category level (auto-calculated)
   */
  categoryLevel?: number | null
  type: 'MEDICINE' | 'DIETARY_SUPPLEMENT' | 'MEDICAL_INTRUMENT'
  slug?: string | null
  slugLock?: boolean | null
  updatedAt: string
  createdAt: string
  isFavorite?: boolean | null
}

export interface MedicineType {
  id: string
  name: string
  title: string
  icon?: (string | null) | Media
  note?: string | null
  updatedAt: string
  createdAt: string
}

export interface MedicineBuyButton {
  id: string
  title: string
  logo?: (string | null) | Media
  updatedAt: string
  createdAt: string
}

export interface ProductAgeGroup {
  id: string
  title: string
  icon?: (string | null) | Media
  updatedAt: string
  createdAt: string
}
export interface ProductMedicineType {
  id: string
  name: string
  title: string
  icon?: (string | null) | Media
  note?: string | null
  updatedAt: string
  createdAt: string
}
