import { LinkProps, Tabs, useRouter } from 'expo-router'
import React from 'react'
import { Platform, View } from 'react-native'

import { APP_TABS } from '@/constants/navigation.constant'
import { useAuthentication } from '@/hooks/auth/useAuthentication'
import { APP_ROUTES } from '@/routes/appRoutes'
import { colors, primary } from '@/styles/_colors'
import { getDynamicTabBarHeight } from '@/styles/_variables'
import { cn } from '@/utils/cn'
import { useTranslation } from 'react-i18next'
import { SvgProps } from 'react-native-svg'
import { HapticTab } from '../HapticTab'
export const AppTabs = () => {
  const { t } = useTranslation()
  const router = useRouter()
  const { status, user } = useAuthentication()
  // const segments = useSegments()

  // Memoize current tab calculation
  // const currentTab = useMemo(() => {
  //   // segments structure: ['(tabs)', 'actual-tab-name'] or ['(tabs)'] for index
  //   if (segments.length <= 1 || segments[1] === undefined) {
  //     return 'index'
  //   }
  //   return segments[1]
  // }, [segments])
  return (
    <Tabs
      initialRouteName="index"
      screenOptions={{
        animation: 'fade',
        headerShown: false,
        tabBarStyle: {
          ...Platform.select({
            ios: {
              shadowColor: '#000',
              shadowOffset: { width: 0, height: -1.2 },
              shadowOpacity: 0.1,
              shadowRadius: 1.2,

              borderTopWidth: 0,
            },
            android: {
              elevation: 0,
              borderTopWidth: 0.5,
              borderTopColor: colors.divider.default,
            },
          }),
          height: getDynamicTabBarHeight(),
          paddingBottom: 0,
        },
        tabBarLabelStyle: {
          fontSize: 11,
          fontFamily: 'Inter',
          fontWeight: '400',
          marginTop: 6,
          display: 'flex',
        },
        tabBarItemStyle: {
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          justifyContent: 'center',
        },
        tabBarActiveTintColor: primary[500],
        tabBarInactiveTintColor: '#8B8C99',
        tabBarButton: (props) => {
          return (
            <HapticTab {...props}>
              <View
                style={{
                  height: '100%',
                  width: '100%',
                  flexDirection: 'column',
                  alignItems: 'center',
                }}
              >
                {props.children}
              </View>
            </HapticTab>
          )
        },
      }}
    >
      {/* Tab : Home (Trang chủ)  */}
      <Tabs.Screen
        name={APP_TABS.HOME.name}
        options={{
          title: t(APP_TABS.HOME.keyTranslate),
          tabBarIcon: ({ focused, color }) => (
            <TabBarIcon focused={focused} color={color} icon={APP_TABS.HOME.icon} />
          ),
        }}
      />
      {/* Tab : Từ điển */}
      <Tabs.Screen
        name={APP_TABS.MEDICAL_DICTIONARY.name}
        options={{
          title: t(APP_TABS.MEDICAL_DICTIONARY.keyTranslate),
          tabBarIcon: ({ focused, color }) => (
            <TabBarIcon focused={focused} color={color} icon={APP_TABS.MEDICAL_DICTIONARY.icon} />
          ),
        }}
      />

      {/* Tab Chat */}
      <Tabs.Screen
        name={APP_TABS.CHAT.name}
        options={{
          title: t(APP_TABS.CHAT.keyTranslate),
          tabBarIcon: ({ focused, color }) => (
            <TabBarIcon
              isMainTab={true}
              focused={focused}
              color={color}
              icon={APP_TABS.CHAT.icon}
            />
          ),
        }}
        listeners={{
          tabPress: (e) => {
            if (status === 'loading') {
              e.preventDefault()
              return
            }
            if (status === 'unauthorized' || !user) {
              console.log('unauthorized')
              e.preventDefault()
              router.push({
                pathname: APP_ROUTES.LOGIN.path,
                params: {
                  redirect: APP_ROUTES.CHAT_BOT?.children?.CHAT_WOOT?.path,
                },
              } as LinkProps['href'])
            } else {
              e.preventDefault()
              router.push({
                pathname: APP_ROUTES.CHAT_BOT?.children?.CHAT_WOOT?.path,
              } as LinkProps['href'])
            }
          },
        }}
      />
      {/* Tab : Sổ tay Thuốc */}
      <Tabs.Screen
        name={APP_TABS.PRODUCTS.name}
        options={{
          title: t(APP_TABS.PRODUCTS.keyTranslate),
          tabBarIcon: ({ focused, color }) => (
            <TabBarIcon focused={focused} color={color} icon={APP_TABS.PRODUCTS.icon} />
          ),
        }}
      />
      {/* Tab : Tin tức */}
      <Tabs.Screen
        name={APP_TABS.POSTS.name}
        options={{
          title: t(APP_TABS.POSTS.keyTranslate),
          tabBarIcon: ({ focused, color }) => (
            <TabBarIcon focused={focused} color={color} icon={APP_TABS.POSTS.icon} />
          ),
        }}
      />
    </Tabs>
  )
}

export const TabBarIcon = ({
  color,
  icon,
  isMainTab,
  // isActive,
}: {
  focused: boolean
  color: string
  icon: React.ComponentType<SvgProps>
  isMainTab?: boolean
  isActive?: boolean
}) => {
  const Icon = icon
  return (
    <View
      className={cn('relative flex items-center justify-center', isMainTab && '-mt-4  ')}
      style={{
        borderRadius: '100%',
      }}
    >
      <View
        className={cn(
          'relative flex h-8 w-8 flex-row items-center justify-center',
          isMainTab && 'h-14 w-14 rounded-full bg-primary p-2',
        )}
        // style={{
        //   ...Platform.select({
        //     ios: isMainTab && {
        //       backgroundColor: primary[500],
        //       shadowColor: primary[500],
        //       shadowOffset: { width: 0, height: -2 },
        //       shadowOpacity: isActive ? 0.8 : 0,
        //       shadowRadius: 4,
        //       elevation: 8,
        //     },
        //   }),
        // }}
      >
        <Icon width={24} height={24} color={color} className="h-6 w-6" />
      </View>
    </View>
  )
}
