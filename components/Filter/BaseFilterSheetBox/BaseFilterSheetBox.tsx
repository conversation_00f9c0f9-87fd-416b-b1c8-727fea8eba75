import CloseIcon from '@/assets/icons/close-icon.svg'
import { Text } from '@/components/ui/Text/Text'
import { forwardRef, useCallback, useEffect, useImperativeHandle, useState } from 'react'
import { TouchableOpacity, View } from 'react-native'

// Ref methods that can be called imperatively
export interface BaseFilterSheetBoxRef {
  measureFooter: () => void
  getFooterHeight: () => number
}

interface BaseFilterSheetBoxProps {
  // Header props
  title: string
  onClose?: () => void
  headerClassName?: string
  titleClassName?: string
  closeIconSize?: number

  // Content props
  children: React.ReactNode
  useBottomSheetScrollView?: boolean
  contentContainerStyle?: any
  showScrollIndicator?: boolean

  // Footer props
  footer?: React.ReactNode
  footerRef?: React.RefObject<View | null>
  enableFooterHeightMeasurement?: boolean
  footerHeightPadding?: number

  // Container props
  containerClassName?: string
}

export const BaseFilterSheetBox = forwardRef<BaseFilterSheetBoxRef, BaseFilterSheetBoxProps>(
  (
    {
      // Header props
      title,
      onClose,
      headerClassName = 'relative flex flex-row-reverse items-center justify-center border-b border-gray-200 bg-white px-3 pb-3 pt-1',
      titleClassName = 'heading8',
      closeIconSize = 36,

      // Content props
      children,

      // Footer props
      footer,
      footerRef,
      enableFooterHeightMeasurement = false,

      // Container props
      containerClassName = 'flex-1 flex-col gap-y-2',
    },
    ref,
  ) => {
    const [footerHeight, setFooterHeight] = useState(160)

    // Function to measure footer height
    const measureFooterHeight = useCallback(() => {
      if (footerRef?.current && enableFooterHeightMeasurement) {
        footerRef.current.measure((_, __, ___, height) => {
          if (height > 0 && height !== footerHeight) {
            setFooterHeight(height)
          }
        })
      }
    }, [footerHeight, footerRef, enableFooterHeightMeasurement])

    // Expose the measureFooterHeight method imperatively
    useImperativeHandle(
      ref,
      () => ({
        measureFooter: measureFooterHeight,
        getFooterHeight: () => footerHeight,
      }),
      [measureFooterHeight, footerHeight],
    )

    // Measure height when footer changes
    useEffect(() => {
      if (enableFooterHeightMeasurement) {
        const timer = setTimeout(measureFooterHeight, 50)
        return () => clearTimeout(timer)
      }
    }, [measureFooterHeight, enableFooterHeightMeasurement])

    return (
      <View className={containerClassName}>
        {/* Header */}
        <View className={headerClassName}>
          <Text size={titleClassName as any} variant="primary">
            {title}
          </Text>
          {onClose && (
            <TouchableOpacity
              onPress={onClose}
              className="absolute -top-1 right-3"
              hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
            >
              <CloseIcon width={closeIconSize} height={closeIconSize} />
            </TouchableOpacity>
          )}
        </View>

        {/* Content Area */}
        <View
          className="flex-1 bg-white"
          // contentContainerStyle={scrollContentStyle}
          style={
            enableFooterHeightMeasurement
              ? {
                  marginBottom: footerHeight,
                }
              : undefined
          }
        >
          {children}
        </View>

        {/* Footer */}
        {footer}
      </View>
    )
  },
)

BaseFilterSheetBox.displayName = 'BaseFilterSheetBox'

// Hook for easier usage with sheet actions
export const useFilterSheetBox = () => {
  return {
    // Helper to create consistent sheet props
    createSheetProps: (snapPoints: string[] = ['85%', '100%']) => ({
      snapPoints,
      enableHandlePanningGesture: true,
      enableDynamicSizing: false,
      enableOverDrag: false,
    }),
  }
}
