import { BackButton, StackScreenBase } from '@/components/StackBase/StackScreenBase'
import { ProfileScreen } from '@/features/user/screens/ProfileScreen/ProfileScreen'
import { primary } from '@/styles/_colors'
import React from 'react'
import { useTranslation } from 'react-i18next'

export default function StackProfileScreen() {
  const { t } = useTranslation()
  return (
    <>
      <StackScreenBase
        options={{
          headerTitle: t('MES-649'),
          headerShown: true,
          headerBackTitle: '',
          headerBackButtonDisplayMode: 'minimal',
          headerTintColor: primary[500],
          headerLeft: () => <BackButton />,
        }}
      />
      <ProfileScreen />
    </>
  )
}
