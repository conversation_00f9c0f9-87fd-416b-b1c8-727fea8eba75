import { Text } from '@/components/ui/Text/Text'
import { APP_ROUTES } from '@/routes/appRoutes'
import { LinkProps, router } from 'expo-router'
import React from 'react'
import { useTranslation } from 'react-i18next'
import { TouchableOpacity, View } from 'react-native'

export const RegisterPrompt = () => {
  const { t } = useTranslation()
  const handleRegisterPress = () => {
    router.replace({
      pathname: APP_ROUTES.REGISTER.path,
    } as LinkProps['href'])
  }

  return (
    <View className="flex-row items-center justify-center gap-1">
      <Text size="body6" variant="default">
        {t('MES-08')}
      </Text>
      <TouchableOpacity onPress={handleRegisterPress}>
        <Text size="body6" variant="primary">
          {t('MES-09')}
        </Text>
      </TouchableOpacity>
    </View>
  )
}
