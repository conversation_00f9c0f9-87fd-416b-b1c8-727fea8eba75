import WarningIcon from '@/assets/icons/warning-2.svg'
import { Button, ButtonText } from '@/components/ui/Button/Button'
import { useDialog } from '@/hooks/dialog/useDialog'

import { useTranslation } from 'react-i18next'
import { Text, View } from 'react-native'
interface LogoutPopupProps {
  close?: () => void
  onPressLogout?: (close?: () => void) => void
}
export const LogoutPopup = ({ close, onPressLogout }: LogoutPopupProps) => {
  const { t } = useTranslation()

  return (
    <View className="flex flex-col items-center justify-center rounded-xl bg-white">
      <WarningIcon width={30} height={30} />
      <Text className="typo-body-3 mt-6 text-center">{t('MES-259')}</Text>
      <Text className="typo-body-7 mt-3 text-center">{t('MES-260')}</Text>
      <View className="mt-6 flex w-full flex-col gap-y-2">
        <Button
          className="w-full flex-1 rounded-lg"
          variant="solid"
          action="negative"
          onPress={() => onPressLogout?.(close)}
        >
          <ButtonText>{t('MES-169')}</ButtonText>
        </Button>
        <Button
          variant="outline"
          className="w-full flex-1 rounded-lg border-custom-text-disabled"
          onPress={close}
        >
          <ButtonText>{t('MES-224')}</ButtonText>
        </Button>
      </View>
    </View>
  )
}

export const useOpenLogoutPopup = () => {
  const { openDialog } = useDialog()
  const openLogoutPopup = (onPressLogout: (close?: () => void) => void) => {
    openDialog({
      children: ({ close }: { close: () => void }) => {
        return <LogoutPopup close={close} onPressLogout={() => onPressLogout?.(close)} />
      },
      variant: 'blank',
      contentSize: 'md',
      bodyClassName: 'p-0 m-0',
    })
  }
  return { openLogoutPopup }
}
