import EmptyBoxIcon from '@/assets/icons/empty-box.svg'
import { SearchInput, SearchInputRef } from '@/components/ui/SearchInput/SearchInput'
import { Skeleton } from '@/components/ui/Skeleton/Skeleton'
import { Text } from '@/components/ui/Text/Text'
import { useAppLanguage } from '@/hooks/common/useAppLanguage'
import colors from '@/styles/_colors'
import { useCallback, useMemo, useRef, useState } from 'react'
import { useTranslation } from 'react-i18next'
import { ActivityIndicator, FlatList, RefreshControl, View } from 'react-native'
import { useGetInfiniteMedicalDocumentCategories } from '../../hooks/useGetInfiniteMedicalDocumentCategories'
import { MedicalDocumentCategory } from '../../types'
import { MedicalDocumentCategoryItem } from './MedicalDocumentCategoryItem'

interface MedicalDocumentCategoriesProps {
  hospitalId?: string
  isShowSearchInput?: boolean
  shouldFilterByHospital?: boolean
}

type ListItem =
  | { type: 'medical_document_category_row'; medicalDocumentCategories: MedicalDocumentCategory[] }
  | { type: 'loading' }
  | { type: 'loading_skeleton' }

export const MedicalDocumentCategories = ({
  hospitalId,
  isShowSearchInput = true,
  shouldFilterByHospital = false,
}: MedicalDocumentCategoriesProps) => {
  const { t } = useTranslation()
  const { primaryLanguage } = useAppLanguage()
  const [searchInputValue, setSearchInputValue] = useState('')
  const [searchQuery, setSearchQuery] = useState('')
  const [refreshing, setRefreshing] = useState(false)
  const searchInputRef = useRef<SearchInputRef>(null)

  const params = useMemo(() => {
    return {
      limit: 10,
      locale: primaryLanguage,
      where: {
        name: {
          like: searchQuery ? searchQuery.trim() : undefined,
        },

        // Filter by hospital if shouldFilterByHospital is true
        hospital: shouldFilterByHospital
          ? hospitalId
            ? {
                equals: hospitalId,
              }
            : undefined
          : {
              exists: false,
            },
      },
      sort: '-createdAt',
      select: {
        id: true,
        name: true,
        color: true,
      },
    }
  }, [primaryLanguage, searchQuery, hospitalId, shouldFilterByHospital])

  const {
    medicalDocumentCategories,
    isGetMedicalDocumentCategoriesLoading,
    isGetMedicalDocumentCategoriesError,
    isFetchingNextPage: isGetMedicalDocumentCategoriesFetchingNextPage,
    fetchNextPage,
    hasNextPage,
    refetch,
  } = useGetInfiniteMedicalDocumentCategories({
    params,
    config: {
      staleTime: 5 * 60 * 1000,
    },
  })

  // Flatten all medical document categories from all pages
  const allMedicalDocumentCategories = useMemo(() => {
    if (!medicalDocumentCategories?.pages) return []
    return medicalDocumentCategories.pages.flatMap((page) => page?.docs || [])
  }, [medicalDocumentCategories])

  // Group categories into pairs for 2-column layout
  const categoryRows = useMemo(() => {
    const rows: MedicalDocumentCategory[][] = []
    for (let i = 0; i < allMedicalDocumentCategories.length; i += 2) {
      rows.push(allMedicalDocumentCategories.slice(i, i + 2))
    }
    return rows
  }, [allMedicalDocumentCategories])

  // Handle pull-to-refresh
  const handleRefresh = useCallback(async () => {
    setRefreshing(true)
    try {
      await refetch()
    } finally {
      setRefreshing(false)
    }
  }, [refetch])

  // Handle load more
  const handleLoadMore = useCallback(() => {
    if (hasNextPage && !isGetMedicalDocumentCategoriesFetchingNextPage) {
      fetchNextPage()
    }
  }, [hasNextPage, isGetMedicalDocumentCategoriesFetchingNextPage, fetchNextPage])

  const handleSearchInputChange = (text: string) => {
    setSearchInputValue(text)
  }

  const handleClearSearchInput = () => {
    setSearchInputValue('')
    setSearchQuery('')
  }

  // Build data array with different ListItem types
  const data = useMemo((): ListItem[] => {
    const items: ListItem[] = []

    // Show loading skeletons during initial load or refresh (but not during pagination)
    const isShowingLoadingSkeleton =
      (isGetMedicalDocumentCategoriesLoading || refreshing) &&
      !isGetMedicalDocumentCategoriesFetchingNextPage

    if (isShowingLoadingSkeleton) {
      items.push({ type: 'loading_skeleton' })
    } else {
      // Categories in rows (2 columns)
      categoryRows.forEach((row) => {
        items.push({ type: 'medical_document_category_row', medicalDocumentCategories: row })
      })

      // Loading indicator for pagination
      if (isGetMedicalDocumentCategoriesFetchingNextPage) {
        items.push({ type: 'loading' })
      }
    }

    return items
  }, [
    categoryRows,
    isGetMedicalDocumentCategoriesFetchingNextPage,
    isGetMedicalDocumentCategoriesLoading,
    refreshing,
  ])

  // Render item based on ListItem type
  const renderItem = useCallback(({ item }: { item: ListItem }) => {
    switch (item.type) {
      case 'medical_document_category_row':
        return (
          <View className="mb-1 flex w-full">
            <View className="flex flex-row gap-3">
              {item.medicalDocumentCategories.map((medicalDocumentCategory) => (
                <View key={medicalDocumentCategory.id} className="flex-1">
                  <MedicalDocumentCategoryItem medicalDocumentCategory={medicalDocumentCategory} />
                </View>
              ))}
              {/* Fill empty space if odd number of categories */}
              {item.medicalDocumentCategories.length === 1 && <View className="flex-1" />}
            </View>
          </View>
        )

      case 'loading':
        return (
          <View className="items-center py-4">
            <ActivityIndicator size="small" />
          </View>
        )

      case 'loading_skeleton':
        return (
          <View className="flex flex-col gap-y-4">
            {new Array(4).fill(0).map((_, rowIndex) => (
              <View key={rowIndex} className="flex flex-row gap-3">
                {new Array(2).fill(0).map((_, colIndex) => (
                  <View key={colIndex} className="flex-1">
                    <Skeleton className="h-32 w-full rounded-lg" />
                  </View>
                ))}
              </View>
            ))}
          </View>
        )

      default:
        return null
    }
  }, [])

  const renderEmptyComponent = useCallback(() => {
    if (isGetMedicalDocumentCategoriesError) {
      return (
        <View className="items-center  py-8">
          <Text size="body6" className="text-red-500">
            {t('MES-197')}
          </Text>
        </View>
      )
    }
    if (isGetMedicalDocumentCategoriesLoading || isGetMedicalDocumentCategoriesFetchingNextPage) {
      return null
    }

    return (
      <View className="items-center  py-8">
        <EmptyBoxIcon />
        <Text size="body6" variant="default">
          {t('MES-803')}
        </Text>
      </View>
    )
  }, [
    isGetMedicalDocumentCategoriesError,
    isGetMedicalDocumentCategoriesLoading,
    isGetMedicalDocumentCategoriesFetchingNextPage,
  ])

  const renderSeparator = useCallback(() => {
    return <View className="h-2" />
  }, [])

  const keyExtractor = useCallback((item: ListItem, index: number) => {
    if (item.type === 'medical_document_category_row') {
      return `category-row-${item.medicalDocumentCategories.map((c) => c.id).join('-')}-${index}`
    }
    return `${item.type}-${index}`
  }, [])

  return (
    <View className="flex-1 flex-col gap-y-6 bg-white ">
      {/* Header */}
      {isShowSearchInput && (
        <View className="flex flex-col gap-y-3 px-4">
          <View className="flex  flex-col gap-y-3 bg-white ">
            <View className="flex flex-col gap-y-3">
              <View className="flex flex-row items-center gap-x-2">
                {/* Search Input */}
                <SearchInput
                  ref={searchInputRef}
                  placeholder={t('MES-815')}
                  value={searchInputValue}
                  onChangeText={handleSearchInputChange}
                  onClear={handleClearSearchInput}
                  onSubmitEditing={() => {
                    setSearchQuery(searchInputValue)
                    setSearchInputValue(searchInputValue)
                  }}
                />
              </View>
            </View>
          </View>
        </View>
      )}

      {/* List */}
      <View className="flex-1 px-4 pb-4">
        <FlatList
          showsVerticalScrollIndicator={false}
          data={data}
          renderItem={renderItem}
          keyExtractor={keyExtractor}
          contentContainerStyle={{
            paddingBottom: 16,
          }}
          ItemSeparatorComponent={renderSeparator}
          ListEmptyComponent={renderEmptyComponent}
          onEndReached={handleLoadMore}
          onEndReachedThreshold={0.1}
          refreshControl={
            <RefreshControl
              refreshing={refreshing}
              onRefresh={handleRefresh}
              colors={[colors.primary['500']]}
              tintColor={colors.primary['500']}
              progressBackgroundColor="#FFFFFF"
            />
          }
        />
      </View>
    </View>
  )
}
