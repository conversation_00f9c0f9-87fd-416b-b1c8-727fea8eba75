import { APP_ROUTES } from '@/routes/appRoutes'
import { LinkP<PERSON>, useRouter } from 'expo-router'
import { Platform } from 'react-native'

export const useNewProductNotiAction = () => {
  const router = useRouter()

  const handleOpenNewProductNoti = (slug: string) => {
    const navigateToProduct = () => {
      router.push({
        pathname: APP_ROUTES.PRODUCTS.children?.PRODUCTS_DETAIL_V2.path + '/[slug]',
        params: {
          slug: slug,
        },
      } as LinkProps['href'])
    }

    if (Platform.OS === 'ios') {
      setTimeout(() => {
        navigateToProduct()
      }, 1)
    } else {
      setImmediate(() => {
        navigateToProduct()
      })
    }
  }

  return {
    handleOpenNewProductNoti,
  }
}
