import { Text } from '@/components/ui/Text/Text'
import { LocaleEnum } from '@/enums/locale.enum'
import { useAppLanguage } from '@/hooks/common/useAppLanguage'
import { cn } from '@/utils/cn'
import dayjs from 'dayjs'
import 'dayjs/locale/ja'
import 'dayjs/locale/vi'
import relativeTime from 'dayjs/plugin/relativeTime'
import { useCallback, useMemo } from 'react'
import { useTranslation } from 'react-i18next'
import { TouchableOpacity, View } from 'react-native'
import { useNotificationActions } from '../../actions/useNotificationActions'
import { useMarkNotificationAsViewed } from '../../hooks/query/useMarkNotificationAsViewed'
import { UserPushNotification } from '../../types'
import { getNotificationContent } from '../../utils/getNotificationContent'

// Extend dayjs with relativeTime plugin
dayjs.extend(relativeTime)

interface NotificationItemProps {
  notification: UserPushNotification
}

export const NotificationItem = ({ notification }: NotificationItemProps) => {
  const { createdAt, trackingId, viewed } = notification
  const { t } = useTranslation()
  const { primaryLanguage } = useAppLanguage()

  // Get notification content based on type
  const notificationContent = useMemo(
    () => getNotificationContent(notification, t, primaryLanguage as LocaleEnum),
    [notification, t, primaryLanguage],
  )

  const formatDate = useCallback(
    (dateString: string) => {
      // Set locale based on primary language
      const locale = primaryLanguage === LocaleEnum.VI ? LocaleEnum.VI : LocaleEnum.JA
      dayjs.locale(locale)

      return dayjs(dateString).fromNow()
    },
    [primaryLanguage],
  )

  const { markNotificationAsViewedMutation } = useMarkNotificationAsViewed()
  const { handleNotificationAction } = useNotificationActions()

  const handlePress = useCallback(() => {
    // if (!viewed) {
    //   markNotificationAsViewedMutation({ trackingId })
    // }

    // Handle notification action
    handleNotificationAction(notification)
  }, [trackingId, viewed, notification, markNotificationAsViewedMutation, handleNotificationAction])

  return (
    <TouchableOpacity
      onPress={handlePress}
      className={cn(
        'rounded-lg px-4 py-3',
        notification.viewed ? 'bg-custom-background-form' : 'bg-custom-warning-100',
      )}
    >
      <View className="flex flex-col gap-y-2">
        <View className="flex flex-row items-start justify-between gap-x-1">
          <View className="flex-1 flex-row gap-x-2">
            <View
              className="flex aspect-square shrink-0 items-center justify-center rounded-full p-3"
              style={{
                backgroundColor: notificationContent.backgroundColor,
              }}
            >
              <notificationContent.icon width={18} height={18} />
            </View>
            <View className="flex-1">
              <View>
                <Text size="body8" variant="primary" className="mb-1" numberOfLines={2}>
                  {notificationContent.title}
                </Text>
              </View>
              <View>
                {notificationContent.description && (
                  <Text size="body7" numberOfLines={3}>
                    {notificationContent.description}
                  </Text>
                )}
              </View>
            </View>
          </View>

          {/* Indicator */}
          {!notification.viewed && (
            <View className="mt-2 h-[6px] w-[6px] rounded-full bg-custom-danger-600" />
          )}
        </View>
        {createdAt && (
          <View>
            <Text size="body9" variant="subdued" numberOfLines={1}>
              {formatDate(createdAt)}
            </Text>
          </View>
        )}
      </View>
    </TouchableOpacity>
  )
}
