# Polyfills

This directory contains polyfills for compatibility with older libraries.

## BackHandler Polyfill

**File:** `backhandler.polyfill.js`

### Purpose

React Native 0.65+ deprecated `BackHandler.removeEventListener()` in favor of a subscription-based API. However, some older libraries (like `react-native-modal@13.0.1` used by `@chatwoot/react-native-widget`) still use the deprecated API.

### How it works

The polyfill:

1. Tracks subscriptions returned by `BackHandler.addEventListener()`
2. Maps them to their handler functions
3. Provides the deprecated `removeEventListener()` method that calls `.remove()` on the tracked subscriptions

### Usage

The polyfill is automatically imported at the very top of `app/_layout.tsx` to ensure it's loaded before any library code runs:

```typescript
import '@/polyfills/backhandler.polyfill'
```

### Libraries that need this

- `@chatwoot/react-native-widget@^0.0.21` (uses `react-native-modal@13.0.1`)

### Alternative solutions

If you want to avoid the polyfill, you can:

1. Wait for library updates that support the new BackHandler API
2. Use a different chat solution
3. Fork and patch the affected libraries
