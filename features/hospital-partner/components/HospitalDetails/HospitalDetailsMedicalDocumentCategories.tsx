import { Text } from '@/components/ui/Text/Text'
import { MedicalDocumentCategoryItem } from '@/features/medical-document/components/MedicalDocumentCategories/MedicalDocumentCategoryItem'
import { MedicalDocumentCategory } from '@/features/medical-document/types'
import { useAppLanguage } from '@/hooks/common/useAppLanguage'

import { Skeleton } from '@/components/ui/Skeleton/Skeleton'
import { useGetMedicalDocumentCategories } from '@/features/medical-document/hooks/useGetMedicalDocumentCategories'
import { APP_ROUTES } from '@/routes/appRoutes'
import { Link, LinkProps } from 'expo-router'
import { useEffect, useMemo } from 'react'
import { useTranslation } from 'react-i18next'
import { TouchableOpacity, View } from 'react-native'
import { Hospital } from '../../types'

interface HospitalDetailsMedicalDocumentCategoriesProps {
  hospital: Hospital
  isRefreshing: boolean
}
export const HospitalDetailsMedicalDocumentCategories = ({
  hospital,
  isRefreshing,
}: HospitalDetailsMedicalDocumentCategoriesProps) => {
  const { t } = useTranslation()
  const { primaryLanguage } = useAppLanguage()
  const params = useMemo(() => {
    return {
      limit: 4,
      locale: primaryLanguage,
      where: {
        hospital: hospital.id
          ? {
              equals: hospital.id,
            }
          : undefined,
      },
      sort: '-createdAt',
      select: {
        id: true,
        name: true,
        color: true,
      },
    }
  }, [primaryLanguage, hospital.id])

  const {
    medicalDocumentCategories,
    isGetMedicalDocumentCategoriesLoading,
    isRefetching,
    isFetching,
    refetch,
  } = useGetMedicalDocumentCategories({
    params,
    useQueryOptions: {
      staleTime: 0,
    },
  })

  // Group categories into pairs for 2-column layout
  const categoryRows = useMemo(() => {
    const rows: MedicalDocumentCategory[][] = []
    if (!medicalDocumentCategories?.docs) return rows
    for (let i = 0; i < medicalDocumentCategories?.docs.length; i += 2) {
      rows.push(medicalDocumentCategories?.docs.slice(i, i + 2) || [])
    }
    return rows
  }, [medicalDocumentCategories])
  useEffect(() => {
    if (isRefreshing) {
      refetch()
    }
  }, [isRefreshing])
  if (isGetMedicalDocumentCategoriesLoading || isRefetching || isFetching || isRefreshing) {
    return <HospitalDetailsMedicalDocumentCategoriesSkeleton />
  }

  if (!medicalDocumentCategories?.docs.length) {
    return null
  }

  return (
    <View className="flex flex-col gap-y-3 px-4">
      <View className="flex flex-row items-center justify-between">
        <Text size="body3" variant="default">
          {t('MES-814')}
        </Text>

        <Link
          href={
            {
              pathname: APP_ROUTES.MEDICAL_DOCUMENTS.path,
              params: {
                hospitalId: hospital.id,
                shouldFilterByHospital: 'true',
              },
            } as LinkProps['href']
          }
          asChild
        >
          <TouchableOpacity>
            <Text size="body6" variant="primary">
              {t('MES-141')}
            </Text>
          </TouchableOpacity>
        </Link>
      </View>
      {/* Categories */}
      <View className="flex flex-col gap-1">
        {categoryRows.map((row, index) => (
          <View
            key={row.map((cat) => cat.id).join('-') || index.toString()}
            className="flex w-full"
          >
            <View className="flex flex-row gap-3">
              {row.map((medicalDocumentCategory) => (
                <View key={medicalDocumentCategory.id} className="flex-1">
                  <MedicalDocumentCategoryItem
                    medicalDocumentCategory={medicalDocumentCategory}
                    customParams={{
                      hospital: hospital.id,
                    }}
                  />
                </View>
              ))}
              {/* Fill empty space if odd number of categories */}
              {row.length === 1 && <View className="flex-1" />}
            </View>
          </View>
        ))}
      </View>
    </View>
  )
}

const HospitalDetailsMedicalDocumentCategoriesSkeleton = () => {
  return (
    <View className="flex flex-col gap-y-3 px-4">
      <View>
        <Skeleton className="h-6 w-[240px] rounded-lg" />
      </View>
      <View className="flex flex-col gap-y-4">
        {new Array(4).fill(0).map((_, rowIndex) => (
          <View key={rowIndex} className="flex flex-row gap-3">
            {new Array(2).fill(0).map((_, colIndex) => (
              <View key={colIndex} className="flex-1">
                <Skeleton className="h-32 w-full rounded-lg" />
              </View>
            ))}
          </View>
        ))}
      </View>
    </View>
  )
}
