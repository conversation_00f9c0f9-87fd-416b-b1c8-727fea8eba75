import { versionService } from '@/services/version/version.service'
import { Version } from '@/types/version.type'
import { UseQueryOptions, useQuery } from '@tanstack/react-query'
import { AxiosRequestConfig } from 'axios'
import { versionQueryKeys } from './queryKeys'

export const useGetNewestVersion = ({
  options = {},
  useQueryOptions,
}: {
  options?: AxiosRequestConfig
  useQueryOptions?: Omit<UseQueryOptions<Version | null>, 'queryKey' | 'queryFn'>
} = {}) => {
  const {
    isError: isGetNewestVersionError,
    isPending: isGetNewestVersionLoading,
    data: newestVersion,
    ...rest
  } = useQuery({
    queryKey: [versionQueryKeys['newest-version'].base()],
    queryFn: async () => versionService.getNewestAppVersion(options),
    ...useQueryOptions,
  })
  return {
    isGetNewestVersionError,
    isGetNewestVersionLoading,
    newestVersion,
    ...rest,
  }
}
