import { API_ENDPOINTS } from '@/constants/endpoint.constant'
import { httpService } from '@/services/http/http.service'
import { PaginatedDocs } from '@/types/global.type'
import { Params } from '@/types/http.type'
import { Medicine, MedicineCategory } from '@/types/product.type'

import { AxiosRequestConfig } from 'axios'

// SERVER / CLIENT
class MedicineService {
  private static instance: MedicineService

  private constructor() {}

  public static getInstance(): MedicineService {
    if (!MedicineService.instance) {
      MedicineService.instance = new MedicineService()
    }
    return MedicineService.instance
  }

  /**
   * Fetch medicines using the HTTP service with the fetch API.
   *
   * @param params - Optional query parameters for filtering or paginating edicines.
   * @param options - Optional fetch options such as headers, method, etc.
   * @returns A promise resolving to PaginatedDocs<medicine> or null in case of an error.
   */
  public async getMedicines({
    params = {},
    options = {},
  }: {
    params?: Params
    options?: AxiosRequestConfig
  } = {}): Promise<PaginatedDocs<Medicine> | null> {
    const data = await httpService.get<PaginatedDocs<Medicine>>(`/${API_ENDPOINTS.medicines_api}`, {
      params,
      ...options,
    })
    return data
  }

  /**
   * Fetch medicines using the HTTP service with the fetch API.
   *
   * This version (V2) is used to filter results based on the user's subscription package.
   * @param params - Optional query parameters for filtering or paginating edicines.
   * @param options - Optional fetch options such as headers, method, etc.
   * @returns A promise resolving to PaginatedDocs<medicine> or null in case of an error.
   */
  public async getMedicinesV2({
    params = {},
    options = {},
  }: {
    params?: Params
    options?: AxiosRequestConfig
  } = {}): Promise<PaginatedDocs<Medicine> | null> {
    const data = await httpService.get<PaginatedDocs<Medicine>>(
      `/${API_ENDPOINTS.medicines_api}/v2/list`,
      {
        params,
        ...options,
      },
    )
    return data
  }

  /**
   * Fetch medicine categories using the HTTP service with the fetch API.
   *
   * @param params - Optional query parameters for filtering or paginating medicine categories.
   * @param options - Optional fetch options such as headers, method, etc.
   * @returns A promise resolving to PaginatedDocs<MedicineCategory> or null in case of an error.
   */
  public async getMedicineCategories({
    params = {},
    options = {},
  }: {
    params?: Params
    options?: AxiosRequestConfig
  } = {}): Promise<PaginatedDocs<MedicineCategory> | null> {
    const data = await httpService.get<PaginatedDocs<MedicineCategory>>(
      `/${API_ENDPOINTS.medicine_categories_api}`,
      {
        params,
        ...options,
      },
    )
    return data
  }
  /**
   * Fetch medicine categories using the HTTP service with the fetch API.
   *
   * @param params - Optional query parameters for filtering or paginating medicine categories.
   * @param options - Optional fetch options such as headers, method, etc.
   * @returns A promise resolving to PaginatedDocs<MedicineCategory> or null in case of an error.
   */
  public async getMedicineCategory({
    id,
    params = {},
    options = {},
  }: {
    id: string
    params?: Params
    options?: AxiosRequestConfig
  }): Promise<MedicineCategory | null> {
    const data = await httpService.get<MedicineCategory>(
      `/${API_ENDPOINTS.medicine_categories_api}/${id}`,
      {
        params,
        ...options,
      },
    )
    return data
  }

  public async getMedicineById({
    id,
    params = {},
    options = {},
  }: {
    id: string
    params?: Params
    options?: AxiosRequestConfig
  }): Promise<MedicineCategory | null> {
    const data = await httpService.get<Medicine>(`/${API_ENDPOINTS.medicines_api}/${id}`, {
      params,
      ...options,
    })
    return data
  }

  public async getMedicineBySlugV2({
    slug,
    options = {},
    params = {},
  }: {
    slug: string
    options?: AxiosRequestConfig
    params?: Params
  }): Promise<Medicine | null> {
    const data = await httpService.get<Medicine>(
      `/${API_ENDPOINTS.medicines_api}/v2/detail/${slug}`,
      {
        params,
        ...options,
      },
    )
    return data
  }
}

export const medicineService = MedicineService.getInstance()
