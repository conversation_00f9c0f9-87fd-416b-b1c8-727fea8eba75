import { keywordService } from '@/services/handbook/keyword.service'
import { PaginatedDocs } from '@/types/global.type'
import { Params } from '@/types/http.type'
import { Keyword } from '@/types/keyword.type'
import { UseQueryOptions, useQuery } from '@tanstack/react-query'
import { AxiosRequestConfig } from 'axios'
import { keywordQueryKeys } from './queryKeys'

export const useGetRandomKeywords = ({
  params = {},
  options = {},
  useQueryOptions,
}: {
  params?: Params
  options?: AxiosRequestConfig
  useQueryOptions?: Omit<UseQueryOptions<PaginatedDocs<Keyword> | null>, 'queryKey' | 'queryFn'>
} = {}) => {
  const {
    isError: isGetRandomKeywordsError,
    isPending: isGetRandomKeywordsLoading,
    data: randomKeywords,
    ...rest
  } = useQuery({
    queryKey: [keywordQueryKeys['randomKeywords'].base(), params],
    queryFn: async () =>
      keywordService.getRandomKeywords({
        params: params,
        options: options,
      }),
    ...useQueryOptions,
  })
  return {
    isGetRandomKeywordsError,
    isGetRandomKeywordsLoading,
    randomKeywords,
    ...rest,
  }
}
