import { Text } from '@/components/ui/Text/Text'
import { useSheetActions } from '@/contexts/SheetContext/SheetContext'
import { useAppLanguage } from '@/hooks/common/useAppLanguage'
import { useCallback, useEffect, useMemo, useRef, useState } from 'react'
import { useTranslation } from 'react-i18next'
import { TouchableOpacity, View } from 'react-native'
import { useShallow } from 'zustand/react/shallow'

import Svg, { Path } from 'react-native-svg'

import {
  BaseFilterSheetBox,
  type BaseFilterSheetBoxRef,
} from '@/components/Filter/BaseFilterSheetBox/BaseFilterSheetBox'
import { CategoryListFilterSection } from '@/features/product/components/FilterProductBox/CategoryListFilterSection'
import { MedicineTypeNotes } from '@/features/product/components/FilterProductBox/MedicineTypeNotes'
import { PRODUCT_V2_TYPE_OPTIONS } from '@/features/product/constants'
import { ProductV2TypeEnum } from '@/features/product/enums'
import { useGetProductAgeGroups } from '@/features/product/hooks/query/product/useGetProductAgeGroups'
import { useGetProductCategories } from '@/features/product/hooks/query/product/useGetProductCategories'
import { useGetProductMedicineTypes } from '@/features/product/hooks/query/product/useGetProductMedicineTypes'
import { ProductAgeGroup, ProductMedicineType } from '@/features/product/types'
import {
  FavoriteProductFilterItem,
  FavoriteProductFilterType,
  useFavoriteProductStore,
} from '@/features/user/stores/FavoriteProductStore'
import { BottomSheetScrollView } from '@gorhom/bottom-sheet'

import { BaseFilterListSection } from '@/components/Filter/BaseFilterListSection/BaseFilterListSection'
import { SearchFilterFooter } from './SearchFilterFooter'

interface FilterSearchBoxProps {
  closeSheet?: () => void
  sharedFooterRef: React.RefObject<View | null>
  sharedSheetBoxRef?: React.RefObject<BaseFilterSheetBoxRef | null>
}

export const FilterSearchBox = ({
  closeSheet,
  sharedFooterRef,
  sharedSheetBoxRef,
}: FilterSearchBoxProps) => {
  const { primaryLanguage } = useAppLanguage()
  const { t } = useTranslation()
  const [showAllCategories, setShowAllCategories] = useState(false)
  const localSheetBoxRef = useRef<BaseFilterSheetBoxRef>(null)
  const sheetBoxRef = sharedSheetBoxRef || localSheetBoxRef

  const { productAgeGroups, isGetProductAgeGroupsLoading } = useGetProductAgeGroups({
    params: { locale: primaryLanguage, pagination: false },
    useQueryOptions: {
      staleTime: 5 * 60 * 1000,
    },
  })
  const { productMedicineTypes, isGetProductMedicineTypesLoading } = useGetProductMedicineTypes({
    params: { locale: primaryLanguage, pagination: false },
    useQueryOptions: {
      staleTime: 5 * 60 * 1000,
    },
  })

  const { productCategories, isGetProductCategoriesLoading } = useGetProductCategories({
    params: {
      locale: primaryLanguage,
      pagination: false,
      select: {
        id: true,
        title: true,
        type: true,
      },
    },
    useQueryOptions: {
      staleTime: 5 * 60 * 1000,
    },
  })

  const {
    toggleFavoriteTempSearchFilter,
    initFavoriteTempSearchFilters,
    tempSearchFilters,
    clearAllFavoriteTempSearchFiltersMedicineType,
    filterCategoriesByFavoriteProductType,
  } = useFavoriteProductStore(
    useShallow((state) => ({
      toggleFavoriteTempSearchFilter: state.toggleFavoriteTempSearchFilter,
      initFavoriteTempSearchFilters: state.initFavoriteTempSearchFilters,
      tempSearchFilters: state.tempSearchFilters,
      clearAllFavoriteTempSearchFiltersMedicineType:
        state.clearAllFavoriteTempSearchFiltersMedicineType,
      filterCategoriesByFavoriteProductType: state.filterCategoriesByFavoriteProductType,
    })),
  )

  // Initialize temp search filters when component mounts
  useEffect(() => {
    initFavoriteTempSearchFilters()
  }, [initFavoriteTempSearchFilters])

  const activeAgeGroupIds = useFavoriteProductStore(
    useShallow(
      (state) =>
        state.tempSearchFilters[FavoriteProductFilterType.AGE_GROUP]?.map((filter) => filter.id) ||
        [],
    ),
  )

  const activeMedicineTypeIds = useFavoriteProductStore(
    useShallow(
      (state) =>
        state.tempSearchFilters[FavoriteProductFilterType.MEDICINE_TYPE]?.map(
          (filter) => filter.id,
        ) || [],
    ),
  )

  const activeProductTypeIds = useFavoriteProductStore(
    useShallow(
      (state) =>
        state.tempSearchFilters[FavoriteProductFilterType.PRODUCT_TYPE]?.map(
          (filter) => filter.id,
        ) || [],
    ),
  )

  const activeCategoryIds = useFavoriteProductStore(
    useShallow(
      (state) =>
        state.tempSearchFilters[FavoriteProductFilterType.CATEGORY]?.map((filter) => filter.id) ||
        [],
    ),
  )

  const handleToggleAgeGroup = useCallback(
    (ageGroup: ProductAgeGroup) => {
      toggleFavoriteTempSearchFilter(FavoriteProductFilterType.AGE_GROUP, {
        id: ageGroup.id,
        label: ageGroup.title,
        type: FavoriteProductFilterType.AGE_GROUP,
      })
    },
    [toggleFavoriteTempSearchFilter],
  )

  const handleToggleMedicineType = useCallback(
    (medicineType: ProductMedicineType) => {
      toggleFavoriteTempSearchFilter(FavoriteProductFilterType.MEDICINE_TYPE, {
        id: medicineType.id,
        label: medicineType.title,
        type: FavoriteProductFilterType.MEDICINE_TYPE,
      })
    },
    [toggleFavoriteTempSearchFilter],
  )

  const handleToggleProductType = useCallback(
    (filterItem: FavoriteProductFilterItem) => {
      toggleFavoriteTempSearchFilter(FavoriteProductFilterType.PRODUCT_TYPE, filterItem)
      // After toggling product type, filter categories to only keep those that match selected product types
      if (productCategories?.docs) {
        const categoryData = productCategories.docs.map((cat) => ({
          id: cat.id,
          type: cat.type,
        }))
        // Use setTimeout to ensure the toggle action completes first
        setTimeout(() => {
          filterCategoriesByFavoriteProductType(categoryData)
        }, 0)
      }
    },
    [
      toggleFavoriteTempSearchFilter,
      filterCategoriesByFavoriteProductType,
      productCategories?.docs,
    ],
  )

  const handleToggleCategory = useCallback(
    (filterItem: FavoriteProductFilterItem) => {
      toggleFavoriteTempSearchFilter(FavoriteProductFilterType.CATEGORY, filterItem)
    },
    [toggleFavoriteTempSearchFilter],
  )

  const ageGroupData = useMemo(() => productAgeGroups?.docs || [], [productAgeGroups?.docs])

  const medicineTypeData = useMemo(
    () => productMedicineTypes?.docs || [],
    [productMedicineTypes?.docs],
  )
  const productTypeData = useMemo(
    () =>
      Object.values(PRODUCT_V2_TYPE_OPTIONS).map((item) => ({
        id: item.value,
        label: t(item.translationKey || ''),
        type: FavoriteProductFilterType.PRODUCT_TYPE,
      })),
    [],
  )

  // Filter categories based on active product type for the main display
  const filteredProductCategoryData = useMemo(() => {
    if (!productCategories?.docs) return []

    let filtered = productCategories.docs

    // Filter by active product type if any is selected
    if (activeProductTypeIds.length > 0) {
      filtered = filtered.filter((category) => activeProductTypeIds.includes(category.type))
    }

    // Transform to FavoriteProductFilterItem format and limit to 8 items
    return filtered.slice(0, 8).map((item) => ({
      id: item.id,
      label: item.title,
      type: FavoriteProductFilterType.CATEGORY,
    }))
  }, [productCategories?.docs, activeProductTypeIds])

  const shouldShowMedicineTypeFilter = useMemo(() => {
    // Show medicine type filter if product type includes medicine OR if no product type is selected
    const isFilterProductTypeContainMedicineType = tempSearchFilters?.[
      FavoriteProductFilterType.PRODUCT_TYPE
    ]?.some((filter) => filter.id === ProductV2TypeEnum.MEDICINE)
    const isFilterProductTypeEmpty =
      !tempSearchFilters?.[FavoriteProductFilterType.PRODUCT_TYPE] ||
      tempSearchFilters?.[FavoriteProductFilterType.PRODUCT_TYPE]?.length === 0

    return isFilterProductTypeContainMedicineType || isFilterProductTypeEmpty
  }, [tempSearchFilters])

  // Track active filters count to trigger footer height re-measurement
  const activeFiltersCount =
    activeAgeGroupIds.length +
    activeMedicineTypeIds.length +
    activeProductTypeIds.length +
    activeCategoryIds.length

  // Trigger manual footer measurement when filters change (no re-render!)
  useEffect(() => {
    // Small delay to allow footer DOM updates to complete
    const timer = setTimeout(() => {
      sheetBoxRef.current?.measureFooter()
    }, 100)

    return () => clearTimeout(timer)
  }, [activeFiltersCount])

  useEffect(() => {
    // Clear medicine type filters when they shouldn't be shown
    if (!shouldShowMedicineTypeFilter) {
      clearAllFavoriteTempSearchFiltersMedicineType()
    }
  }, [shouldShowMedicineTypeFilter])

  return (
    <BaseFilterSheetBox
      ref={sheetBoxRef}
      title={t('MES-481')}
      onClose={closeSheet}
      footerRef={sharedFooterRef}
      enableFooterHeightMeasurement={true}
      footerHeightPadding={40}
      useBottomSheetScrollView={true}
    >
      {!showAllCategories ? (
        <BottomSheetScrollView className="relative" showsVerticalScrollIndicator={false}>
          <View className="flex flex-col gap-y-3 px-4">
            <BaseFilterListSection
              title={t('MES-711')}
              data={productTypeData}
              onSelectFilter={handleToggleProductType}
              activeFilters={activeProductTypeIds}
              idKey="id"
              labelKey="label"
            />
            <BaseFilterListSection
              title={t('MES-128')}
              data={filteredProductCategoryData}
              onSelectFilter={handleToggleCategory}
              activeFilters={activeCategoryIds}
              idKey="id"
              labelKey="label"
              isLoading={isGetProductCategoriesLoading}
            >
              <TouchableOpacity
                className="mx-auto mt-3 flex flex-row items-center gap-x-1"
                onPress={() => setShowAllCategories(true)}
              >
                <Text size="body6" variant="primary">
                  {t('MES-476')}
                </Text>
                <Svg width={18} height={18} viewBox="0 0 18 18" fill="none">
                  <Path
                    d="M7.06836 13.9498L11.1434 9.8748C11.6246 9.39355 11.6246 8.60605 11.1434 8.1248L7.06836 4.0498"
                    stroke="#1157C8"
                    strokeWidth={1.5}
                    strokeMiterlimit={10}
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  />
                </Svg>
              </TouchableOpacity>
            </BaseFilterListSection>
            <BaseFilterListSection
              title={t('MES-707')}
              data={ageGroupData}
              onSelectFilter={handleToggleAgeGroup}
              activeFilters={activeAgeGroupIds}
              idKey="id"
              labelKey="title"
              isLoading={isGetProductAgeGroupsLoading}
            />
            {shouldShowMedicineTypeFilter && (
              <BaseFilterListSection
                title={t('MES-708')}
                data={medicineTypeData}
                onSelectFilter={handleToggleMedicineType}
                activeFilters={activeMedicineTypeIds}
                idKey="id"
                labelKey="title"
                isLoading={isGetProductMedicineTypesLoading}
              >
                <MedicineTypeNotes medicineType={medicineTypeData} />
              </BaseFilterListSection>
            )}
          </View>
        </BottomSheetScrollView>
      ) : (
        <CategoryListFilterSection
          footerHeight={sheetBoxRef.current?.getFooterHeight() || 160}
          setShowAllCategories={setShowAllCategories}
          productCategoryData={productCategories?.docs || []}
          handleToggleCategory={(category) => {
            handleToggleCategory({
              id: category.id,
              label: category.title,
              type: FavoriteProductFilterType.CATEGORY,
            })
          }}
          activeCategoryIds={activeCategoryIds}
          activeProductTypeIds={activeProductTypeIds}
          isGetProductCategoriesLoading={isGetProductCategoriesLoading}
        />
      )}
    </BaseFilterSheetBox>
  )
}

export const useOpenFilterSearchBox = () => {
  const { openCustomSheet, closeSheet } = useSheetActions()

  const sharedFooterRef = useRef<View>(null)
  const sharedSheetBoxRef = useRef<BaseFilterSheetBoxRef>(null)

  const handleOpenFilterSearchBox = useCallback(() => {
    openCustomSheet({
      children: ({ close }) => (
        <FilterSearchBox
          closeSheet={close}
          sharedFooterRef={sharedFooterRef}
          sharedSheetBoxRef={sharedSheetBoxRef}
        />
      ),
      baseProps: {
        snapPoints: ['85%', '100%'],
        enableHandlePanningGesture: true,
        enableDynamicSizing: false,
        enableOverDrag: false,
      },
      options: {
        footerComponent: (props) => {
          return <SearchFilterFooter ref={sharedFooterRef} closeSheet={closeSheet} {...props} />
        },
      },
    })
  }, [openCustomSheet])

  return { handleOpenFilterSearchBox }
}
