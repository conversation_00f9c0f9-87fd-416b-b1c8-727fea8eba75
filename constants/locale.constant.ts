import { LocaleEnum } from '@/enums/locale.enum'

export const LOCALE_MAP: Record<
  LocaleEnum,
  { code: string; label: { [key in LocaleEnum]: string } }
> = {
  [LocaleEnum.VI]: {
    code: 'vi',
    label: {
      [LocaleEnum.VI]: 'Tiếng Việt',
      [LocaleEnum.JA]: 'ベトナム語',
    },
  },
  [LocaleEnum.JA]: {
    code: 'ja',
    label: {
      [LocaleEnum.VI]: 'Tiếng Nhật',
      [LocaleEnum.JA]: '日本語',
    },
  },
}

export const LOCALE_MAP_I18N: Record<
  LocaleEnum,
  { code: string; label: { [key in LocaleEnum]: string }; fixedLabel: string }
> = {
  [LocaleEnum.VI]: {
    code: 'vi',
    label: {
      [LocaleEnum.VI]: 'MES-46',
      [LocaleEnum.JA]: 'MES-46',
    },
    fixedLabel: 'Tiếng Việt',
  },
  [LocaleEnum.JA]: {
    code: 'ja',
    label: {
      [LocaleEnum.VI]: 'MES-47',
      [LocaleEnum.JA]: 'MES-47',
    },
    fixedLabel: '日本語',
  },
}

export const SUPPORTED_TEXT_TO_SPEECH_VOICE = {
  [LocaleEnum.VI]: {
    languageCode: 'vi-VN',
    name: 'vi-VN-Standard-A',
  },
  [LocaleEnum.JA]: {
    languageCode: 'ja-JP',
    name: 'ja-JP-Standard-B',
  },
}

// Map each primary language to its fallback/secondary language
export const LOCALE_FALLBACK_MAP: Record<LocaleEnum, LocaleEnum> = {
  [LocaleEnum.VI]: LocaleEnum.JA,
  [LocaleEnum.JA]: LocaleEnum.VI,
}
