import { firebaseAnalytics } from '@/firebase'
import Constants, { ExecutionEnvironment } from 'expo-constants'
import { isNil, isObject, isString } from 'lodash-es'
import { ANALYTICS_EVENTS } from '../constants'

// Conditional Firebase Analytics imports for Expo Go compatibility
let logEvent: any = null
let setUserId: any = null
let setUserProperties: any = null

try {
  if (process.env.EXPO_PUBLIC_APP_ENVIROMENT !== 'expo-go') {
    const analyticsModule = require('@react-native-firebase/analytics')
    logEvent = analyticsModule.logEvent
    setUserId = analyticsModule.setUserId
    setUserProperties = analyticsModule.setUserProperties
  }
} catch (error) {
  console.log('Firebase Analytics module not available')
}

class AnalyticsService {
  private static instance: AnalyticsService
  private isAnalyticsAvailable: boolean = false

  private constructor() {
    this.checkAnalyticsAvailability()
  }

  private checkAnalyticsAvailability(): void {
    try {
      // Check if running in Expo Go
      if (
        Constants.appOwnership === 'expo' ||
        Constants.executionEnvironment === ExecutionEnvironment.StoreClient
      ) {
        this.isAnalyticsAvailable = false
        return
      }

      // Check if Firebase Analytics is available
      if (!firebaseAnalytics || !logEvent || !setUserId || !setUserProperties) {
        this.isAnalyticsAvailable = false
        return
      }

      this.isAnalyticsAvailable = true
    } catch (error) {
      this.isAnalyticsAvailable = false
    }
  }

  public static getInstance(): AnalyticsService {
    if (!AnalyticsService.instance) {
      AnalyticsService.instance = new AnalyticsService()
    }
    return AnalyticsService.instance
  }

  // ────────────── Generic Logger ──────────────
  public async logEvent(eventName: string, params?: Record<string, any>) {
    if (!this.isAnalyticsAvailable) {
      if (__DEV__) console.log(`[Analytics] Skipped (Expo Go): ${eventName}`, params)
      return
    }

    try {
      await logEvent(firebaseAnalytics, eventName, params)
      if (__DEV__) console.log(`[Analytics] Logged event: ${eventName}`, params)
    } catch (error) {
      console.error('[Analytics] Failed to log event:', error)
    }
  }

  // ────────────── User Management ──────────────
  public async setUserId(userId: string) {
    if (!this.isAnalyticsAvailable) {
      if (__DEV__) console.log(`[Analytics] Skipped (Expo Go): setUserId ${userId}`)
      return
    }

    try {
      await setUserId(firebaseAnalytics, userId)
      if (__DEV__) console.log(`[Analytics] User ID set: ${userId}`)
    } catch (error) {
      console.error('[Analytics] Failed to set user ID:', error)
    }
  }

  public async setUserProperties(properties: Record<string, any>) {
    if (!this.isAnalyticsAvailable) {
      if (__DEV__) console.log('[Analytics] Skipped (Expo Go): setUserProperties', properties)
      return
    }

    try {
      await setUserProperties(firebaseAnalytics, properties)
      if (__DEV__) console.log('[Analytics] User properties:', properties)
    } catch (error) {
      console.error('[Analytics] Failed to set user properties:', error)
    }
  }

  public async clearUser() {
    try {
      await this.setUserId('')
      await this.setUserProperties({})
      if (__DEV__) console.log('[Analytics] Cleared user data')
    } catch (error) {
      console.error('[Analytics] Failed to clear user:', error)
    }
  }

  // ────────────── Screen & Navigation ──────────────
  public async trackScreenRoute(screenRoute: string) {
    try {
      await this.logEvent(ANALYTICS_EVENTS.NAVIGATION, { route: screenRoute })
      if (__DEV__) console.log(`[Analytics] Screen viewed: ${screenRoute}`)
    } catch (error) {
      console.error('[Analytics] Failed to track screen route:', error)
    }
  }

  // ────────────── Common Events ──────────────
  public async logSignUp(method: string) {
    void this.logEvent(ANALYTICS_EVENTS.SIGN_UP, { method })
  }

  public async logLogin(method: string) {
    void this.logEvent(ANALYTICS_EVENTS.LOGIN, { method })
  }

  public async logLogout() {
    void this.logEvent(ANALYTICS_EVENTS.LOGOUT)
  }

  public async logSearch(query: string) {
    void this.logEvent(ANALYTICS_EVENTS.SEARCH, { search_term: query })
  }

  public async logViewItem(itemId: string, itemName: string, category?: string) {
    void this.logEvent(ANALYTICS_EVENTS.VIEW_ITEM, {
      item_id: itemId,
      item_name: itemName,
      item_category: category,
    })
  }

  public async logShare(contentType: string, itemId: string, method: string) {
    void this.logEvent(ANALYTICS_EVENTS.SHARE, {
      content_type: contentType,
      item_id: itemId,
      method,
    })
  }

  public async logFeatureUse(featureName: string) {
    void this.logEvent(ANALYTICS_EVENTS.FEATURE_USE, { feature_name: featureName })
  }

  public async logError(errorMessage: string, screen?: string) {
    void this.logEvent(ANALYTICS_EVENTS.ERROR, { message: errorMessage, screen })
  }

  // Notification Events
  // Notification Events
  public async logNotificationOpen(notificationType: string, data?: Record<string, any>) {
    try {
      const mergedData = {
        notification_type: notificationType,
        ...data,
      }

      // ✅ Safely sanitize values before logging
      const safeParams = Object.entries(mergedData).reduce<Record<string, string>>(
        (acc, [key, value]) => {
          if (isNil(value)) return acc // skip null/undefined

          if (isObject(value) && !isString(value)) {
            try {
              acc[key] = JSON.stringify(value)
            } catch {
              acc[key] = '[unserializable_object]'
            }
          } else {
            acc[key] = String(value)
          }

          return acc
        },
        {},
      )

      void this.logEvent(ANALYTICS_EVENTS.NOTIFICATION_OPEN, safeParams)
    } catch (error) {
      console.error('[Analytics] Failed to log notification open event:', error)
    }
  }
}

export const analyticsService = AnalyticsService.getInstance()
