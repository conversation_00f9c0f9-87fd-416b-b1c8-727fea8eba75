import { MutationOptions, useMutation } from '@tanstack/react-query'
import { useRef } from 'react'
import { commentService } from '../../services/comment.service'
import { EditCommentPayload } from '../../types'
import { commentMutationKeys } from './queryKeys'

type EditCommentVariables = { commentId: string; payload: EditCommentPayload }

export const useEditComment = ({
  options,
}: {
  options?: Omit<MutationOptions | undefined, 'mutationFn' | 'mutationKey'>
} = {}) => {
  // Define an AbortController to cancel previous requests
  const abortControllerRef = useRef<AbortController | null>(null)

  const {
    isError: isEditCommentError,
    isPending: isEditCommentPending,
    mutate: editCommentMutation,
    isSuccess: isEditCommentSuccess,
    ...rest
  } = useMutation({
    mutationKey: commentMutationKeys['editComment'].base(),
    mutationFn: async ({ commentId, payload }: EditCommentVariables) => {
      // Abort any ongoing request before initiating a new one
      if (abortControllerRef.current) {
        abortControllerRef.current.abort()
      }

      // Create a new AbortController for the new request
      const abortController = new AbortController()
      abortControllerRef.current = abortController

      // Pass the signal from AbortController to the service method
      return commentService.editComment(commentId, payload, {
        signal: abortControllerRef.current.signal,
      })
    },
    ...options,
  })

  return {
    isEditCommentError,
    isEditCommentPending,
    editCommentMutation,
    isEditCommentSuccess,
    ...rest,
  }
}
