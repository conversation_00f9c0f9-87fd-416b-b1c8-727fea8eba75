import { StackScreenBase } from '@/components/StackBase/StackScreenBase'
import { ChatWootScreen } from '@/features/chats/screens/ChatwootScreen/ChatwootScreen'
import { withAuthentication } from '@/hoc/withAuthentication'
import React from 'react'
function ChatWootAppScreen() {
  return (
    <>
      <StackScreenBase
        options={{
          headerTitle: 'cHATS',
          headerShown: false,
        }}
      />
      <ChatWootScreen />
    </>
  )
}
export default withAuthentication(ChatWootAppScreen)
