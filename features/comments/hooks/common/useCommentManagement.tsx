import { useAuthentication } from '@/hooks/auth/useAuthentication'
import { User } from '@/types/user.type'
import { useQueryClient } from '@tanstack/react-query'
import { useCallback, useEffect, useMemo, useRef, useState } from 'react'
import { BaseCommentInputRef } from '../../components/CommentInput'
import { CommentCacheManager, CommentQueryParams } from '../../services/commentCacheManager.service'
import {
  AddCommentResponse,
  Comment,
  DeleteCommentResponse,
  EditCommentResponse,
} from '../../types'
import { commentQueryKeys } from '../query/queryKeys'
import { useAddComment } from '../query/useAddComment'
import { useDeleteComment } from '../query/useDeleteComment'
import { useEditComment } from '../query/useEditComment'
import { CommentsQueryConfig, useGetInfiniteComments } from '../query/useGetInfiniteComments'
import { TotalCommentsQueryConfig, useGetTotalComments } from '../query/useGetTotalComments'

interface UseCommentManagementProps {
  id: string
  relationTo: Comment['commentedOn']['relationTo']
  customMainParams?: CommentQueryParams
  customNestedParams?: CommentQueryParams
  onAddCommentSuccess?: (response: AddCommentResponse) => void
  onAddCommentError?: (error: any, variables: any) => void
  onDeleteCommentSuccess?: (response: DeleteCommentResponse) => void
  onDeleteCommentError?: (error: any, variables: any) => void
  onEditCommentSuccess?: (response: EditCommentResponse) => void
  onEditCommentError?: (error: any, variables: any) => void
  customConfig?: CommentsQueryConfig
  listLimit?: number
  nestedLimit?: number
  commentId?: string
  nestedCommentId?: string
  externalCommentInputRef?: React.RefObject<any>
}

export const useCommentManagement = ({
  id,
  relationTo,
  customMainParams,
  customNestedParams,
  onAddCommentSuccess,
  onAddCommentError,
  onDeleteCommentSuccess,
  onDeleteCommentError,
  onEditCommentSuccess,
  onEditCommentError,
  customConfig,
  listLimit = 20,
  nestedLimit = 10,
  commentId,
  nestedCommentId,
  externalCommentInputRef,
}: UseCommentManagementProps) => {
  const { gcTime, staleTime } = customConfig || {}
  const queryClient = useQueryClient()
  const { user } = useAuthentication()

  const isAdmin = user?.roles?.includes('admin') || false
  const [showCommentInput, setShowCommentInput] = useState(false)
  const [replyingTo, setReplyingTo] = useState<Comment | null>(null)
  const currentInputContentRef = useRef<string>('')
  const commentInputRef = externalCommentInputRef || useRef<BaseCommentInputRef>(null)
  const tempCommentIdRef = useRef<string | null>(null)

  // Global editing state - only one comment can be edited at a time
  const [editingCommentId, setEditingCommentId] = useState<string | null>(null)

  const mainParams = useMemo(
    () => ({ page: 1, limit: listLimit, commentId, ...(customMainParams || {}) }),
    [customMainParams, listLimit, commentId],
  )
  const nestedParams = useMemo(
    () => ({ page: 1, limit: nestedLimit, nestedCommentId, ...(customNestedParams || {}) }),
    [customNestedParams, nestedLimit, nestedCommentId],
  )

  const queryKeys = useMemo(
    () => [commentQueryKeys['comments'].base(), relationTo, id, mainParams],
    [relationTo, id, mainParams],
  )

  const cacheManager = useMemo(
    () => new CommentCacheManager({ documentId: id, relationTo, queryClient }),
    [id, relationTo, queryClient],
  )

  const commentsQuery = useGetInfiniteComments({
    id,
    params: mainParams,
    overrideKey: queryKeys,
    config: {
      staleTime: staleTime || 0,
      gcTime: gcTime || 0,
    } as CommentsQueryConfig,
  })

  const totalCommentsQueryKeys = useMemo(
    () => [commentQueryKeys['totalComments'].base(), id, relationTo, mainParams],
    [id, relationTo, mainParams],
  )

  const totalCommentsQuery = useGetTotalComments({
    documentId: id,
    relationTo,
    params: mainParams,
    overrideKey: totalCommentsQueryKeys,
    useQueryOptions: {
      staleTime: staleTime || 0,
      gcTime: gcTime || 0,
    } as TotalCommentsQueryConfig,
  })

  const { addCommentMutation, isAddCommentPending, isAddCommentError, isAddCommentSuccess } =
    useAddComment({
      options: {
        onSuccess: (response: AddCommentResponse) => {
          // Replace optimistic comment with real server data
          if (tempCommentIdRef.current && response.comment) {
            cacheManager.updateOptimisticComment(
              tempCommentIdRef.current,
              response.comment,
              mainParams,
              nestedParams,
              isAdmin,
            )
            tempCommentIdRef.current = null
          }
          onAddCommentSuccess?.(response)
        },
        onError: (error: any, variables: any) => {
          // Mark optimistic comment as having an error instead of removing it
          if (tempCommentIdRef.current) {
            const parentId = variables.payload.parentId
            cacheManager.markOptimisticCommentAsError(
              tempCommentIdRef.current,
              mainParams,
              nestedParams,
              parentId,
            )
            // Don't clear tempCommentIdRef - we need it for retry functionality
          }
          // Don't revert total count - keep the comment in the list with error state
          onAddCommentError?.(error, variables)
        },
      },
    })

  const { deleteCommentMutation, isDeleteCommentPending, isDeleteCommentError } = useDeleteComment({
    options: {
      onSuccess: (response: DeleteCommentResponse) => {
        // Use server's exact count of deleted comments (includes nested replies)
        updateTotalCommentsCache(-response.totalCommentsDeleted)
        onDeleteCommentSuccess?.(response)
      },
      onError: (error: any, variables: any) => {
        onDeleteCommentError?.(error, variables)
      },
    },
  })

  const { editCommentMutation, isEditCommentPending, isEditCommentError } = useEditComment({
    options: {
      onSuccess: (response: EditCommentResponse) => {
        onEditCommentSuccess?.(response)
      },
      onError: (error: any, variables: any) => {
        onEditCommentError?.(error, variables)
      },
    },
  })

  const updateTotalCommentsCache = (increment: number) => {
    cacheManager.updateTotalCommentsCount(increment, mainParams)
  }

  const totalComments = useMemo(() => {
    return totalCommentsQuery.totalComments?.totalComments || 0
  }, [totalCommentsQuery.totalComments])

  const allComments = useMemo(() => {
    if (!commentsQuery.comments?.pages) return []
    return commentsQuery.comments.pages.flatMap((page) => page?.docs || [])
  }, [commentsQuery.comments])

  const handleAddCommentPress = () => {
    // Cancel any active edit mode when adding a new comment
    if (editingCommentId) {
      setEditingCommentId(null)
    }
    setReplyingTo(null)
    setShowCommentInput(true)
    // Delay focus to ensure proper keyboard timing on mobile
    requestAnimationFrame(() => {
      setTimeout(() => {
        commentInputRef.current?.focus()
      }, 100)
    })
  }

  const handleReplyPress = (comment: Comment) => {
    // Cancel any active edit mode when replying
    if (editingCommentId) {
      setEditingCommentId(null)
    }
    setReplyingTo(comment)
    setShowCommentInput(true)
    // Delay focus to ensure proper keyboard timing on mobile
    requestAnimationFrame(() => {
      setTimeout(() => {
        commentInputRef.current?.focus()
      }, 100)
    })
  }

  const handleCommentSubmit = (content: string) => {
    // Create optimistic comment for immediate UI feedback
    const tempId = `temp-${Date.now()}`
    const optimisticComment: Comment = {
      id: tempId,
      content,
      author: user as User,
      commentedOn: {
        relationTo,
        value: id,
      } as Comment['commentedOn'],
      totalLikes: 0,
      isLiked: false,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      isOptimistic: true,
      isLoading: true,
      parent: replyingTo?.id || null,
      replies: { docs: [], hasNextPage: false, totalDocs: 0 },
    }

    // Store temp ID to update with real data on success
    tempCommentIdRef.current = tempId

    // Add to appropriate cache based on comment type
    if (replyingTo) {
      cacheManager.addOptimisticNestedComment(
        optimisticComment,
        replyingTo.id,
        mainParams,
        nestedParams,
      )
    } else {
      cacheManager.addOptimisticRootComment(optimisticComment, mainParams)
    }

    // Optimistically increment total count for immediate feedback
    updateTotalCommentsCache(1)

    setShowCommentInput(false)
    setReplyingTo(null)

    addCommentMutation({
      documentId: id,
      relationTo,
      payload: {
        content,
        parentId: replyingTo?.id,
      },
    })
  }

  const handleCommentCancel = () => {
    commentInputRef.current?.blur()
    setShowCommentInput(false)
    setReplyingTo(null)
    currentInputContentRef.current = ''
  }

  const handleInputContentChange = useCallback((content: string) => {
    currentInputContentRef.current = content
  }, [])

  const handleCancelReply = () => {
    setReplyingTo(null)
  }

  const handleEditComment = (
    commentId: string,
    content: string,
    parentId?: string,
    callbacks?: {
      onSuccess?: () => void
      onError?: () => void
    },
  ) => {
    editCommentMutation(
      { commentId, payload: { content } },
      {
        onSuccess: (response) => {
          cacheManager.updateCommentContent(commentId, content, mainParams, nestedParams, parentId)
          setEditingCommentId(null) // Exit editing mode on success
          callbacks?.onSuccess?.()
        },
        onError: (error) => {
          callbacks?.onError?.()
        },
      },
    )
  }

  const handleStartEdit = (commentId: string) => {
    // If another comment is already being edited, cancel it first
    if (editingCommentId && editingCommentId !== commentId) {
      // The previous edit will be cancelled automatically when we set a new editingCommentId
    }
    setEditingCommentId(commentId)
  }

  const handleCancelEdit = () => {
    setEditingCommentId(null)
  }

  const handleDeleteComment = (
    commentId: string,
    parentId?: string,
    callbacks?: {
      onSuccess?: () => void
      onError?: () => void
    },
  ) => {
    deleteCommentMutation(
      { commentId },
      {
        onSuccess: (response) => {
          cacheManager.deleteComment(commentId, mainParams, nestedParams, parentId)
          callbacks?.onSuccess?.()
        },
        onError: (error) => {
          callbacks?.onError?.()
        },
      },
    )
  }

  const handleRetryAddComment = (content: string, parentId?: string) => {
    // Find the existing failed optimistic comment and get its ID
    const mainQueryKeys = [commentQueryKeys['comments'].base(), relationTo, id, mainParams]
    const existingData = queryClient.getQueryData(mainQueryKeys) as any

    let existingCommentId: string | null = null

    if (existingData?.pages) {
      // Find the failed optimistic comment
      for (const page of existingData.pages || []) {
        for (const comment of page.docs || []) {
          if (typeof comment === 'string') continue

          if (comment.isOptimistic && comment.hasError && comment.content === content) {
            existingCommentId = comment.id
            break
          }
        }
        if (existingCommentId) break
      }
    }

    if (existingCommentId) {
      // Set the temp ID to the existing comment so mutation callbacks can update it
      tempCommentIdRef.current = existingCommentId

      // Update the existing comment to loading state
      const updateCommentToLoading = (comments: Comment[]): Comment[] => {
        return comments.map((comment) => {
          if (typeof comment === 'string') return comment

          if (comment.id === existingCommentId) {
            return {
              ...comment,
              isLoading: true,
              hasError: false,
            }
          }
          return comment
        })
      }

      queryClient.setQueryData(mainQueryKeys, (oldData: any) => {
        if (!oldData) return oldData

        const newPages = oldData.pages.map((page: any) => ({
          ...page,
          docs: updateCommentToLoading(page.docs || []),
        }))

        return {
          ...oldData,
          pages: newPages,
        }
      })
    }

    // Retry the mutation
    addCommentMutation({
      documentId: id,
      relationTo,
      payload: {
        content,
        parentId,
      },
    })
  }

  useEffect(() => {
    return () => {
      setShowCommentInput(false)
      setReplyingTo(null)
      currentInputContentRef.current = ''
      commentInputRef.current?.blur()
    }
  }, [])

  const handleDeleteCommentSuccess = (response: DeleteCommentResponse) => {
    onDeleteCommentSuccess?.(response)
  }

  const invalidateQueries = () => {
    queryClient.invalidateQueries({
      queryKey: queryKeys,
      type: 'active',
      refetchType: 'active',
      exact: false,
    })

    queryClient.invalidateQueries({
      queryKey: [commentQueryKeys['totalComments'].base(), id, relationTo],
      type: 'active',
      refetchType: 'active',
      exact: false,
    })
  }

  return {
    ...commentsQuery,
    allComments,
    totalComments,
    totalCommentsQuery,
    isGetTotalCommentsLoading: totalCommentsQuery.isGetTotalCommentsLoading,
    isGetTotalCommentsError: totalCommentsQuery.isGetTotalCommentsError,
    cacheManager,
    queryKeys,
    showCommentInput,
    replyingTo,
    commentInputRef,
    isAddCommentPending,
    isAddCommentError,
    isAddCommentSuccess,
    isDeleteCommentPending,
    isDeleteCommentError,
    isEditCommentPending,
    isEditCommentError,
    editingCommentId,
    handleAddCommentPress,
    handleReplyPress,
    handleCommentSubmit,
    handleCommentCancel,
    handleInputContentChange,
    handleCancelReply,
    handleEditComment,
    handleStartEdit,
    handleCancelEdit,
    handleDeleteComment,
    handleRetryAddComment,
    invalidateQueries,
    updateTotalCommentsCache,
    currentInputContent: currentInputContentRef.current,
    mainParams,
    nestedParams,
    totalCommentsQueryKeys,
  }
}
