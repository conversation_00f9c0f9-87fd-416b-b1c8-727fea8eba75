export enum PushNotificationMessagesSendToEnum {
  ALL = 'all',
  PLATFORM_GROUPS = 'platform-groups',
  SPECIFIC_USERS = 'specific-users',
}

export enum PushNotificationMessagesTypeEnum {
  NEW_POST = 'new-post',
  CHAT_MESSAGE = 'chat-message',
  NEW_PRODUCT = 'new-product',
  APP_SYSTEM = 'app-system',
  GENERAL = 'general',
  COMMENT_REPLY = 'comment-reply',
  NEW_COMMENT = 'new-comment',
}

export enum PushNotificationMessagesBlockEnum {
  NEW_POST = 'new-post',
  NEW_PRODUCT = 'new-product',
  APP_SYSTEM = 'app-system',
  GENERAL = 'general',
}

export enum PushNotificationMessagesPlatformGroupsEnum {
  IOS = 'ios',
  ANDROID = 'android',
  WEB = 'web',
}

export enum PushNotificationTemplateEnum {
  NEW_POST = 'new-post',
  NEW_PRODUCT = 'new-product',
}

export enum PushNotificationTemplatePrefixKeyEnum {
  POST_TITLE = 'post-title',
  POST_DESCRIPTION = 'post-description',
  PRODUCT_TITLE = 'product-title',
  PRODUCT_DESCRIPTION = 'product-description',
}

export enum PushNotificationMessagesTabEnum {
  ALL = 'all',
  VIEWED = 'viewed',
  NOT_VIEWED = 'not-viewed',
}
