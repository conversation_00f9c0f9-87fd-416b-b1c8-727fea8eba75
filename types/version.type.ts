export interface Version {
  id: string
  /**
   * Use numbers and dots only (e.g., 1.0.0).
   */
  version: string
  name: string
  shortDescription: string
  content: {
    root: {
      type: string
      children: {
        type: string
        version: number
        [k: string]: unknown
      }[]
      direction: ('ltr' | 'rtl') | null
      format: 'left' | 'start' | 'center' | 'right' | 'end' | 'justify' | ''
      indent: number
      version: number
    }
    [k: string]: unknown
  }
  type?: ('app' | 'web') | null
  releasedAt?: string | null
  publishedAt?: string | null
  slug?: string | null
  slugLock?: boolean | null
  /**
   * If checked, the app will force the user to update to the latest version.
   */
  forceUpdate?: boolean | null
  /**
   * Minimum app version that can still run (e.g., x.x.x).
   */
  minVersion?: string | null
  updatedAt: string
  createdAt: string
  _status?: ('draft' | 'published') | null
}
