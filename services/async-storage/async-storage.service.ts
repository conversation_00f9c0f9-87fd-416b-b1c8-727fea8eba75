import AsyncStorage from '@react-native-async-storage/async-storage'

class AsyncStorageService {
  private static instance: AsyncStorageService

  private constructor() {}

  public static getInstance(): AsyncStorageService {
    if (!AsyncStorageService.instance) {
      AsyncStorageService.instance = new AsyncStorageService()
    }
    return AsyncStorageService.instance
  }

  async setItem(key: string, value: string): Promise<void> {
    try {
      await AsyncStorage.setItem(key, value)
    } catch (error) {
      console.error(`Error setting item ${key}:`, error)
      throw error
    }
  }

  async getItem(key: string): Promise<string | null> {
    try {
      return await AsyncStorage.getItem(key)
    } catch (error) {
      console.error(`Error getting item ${key}:`, error)
      return null
    }
  }

  async removeItem(key: string): Promise<void> {
    try {
      await AsyncStorage.removeItem(key)
    } catch (error) {
      console.error(`Error removing item ${key}:`, error)
      throw error
    }
  }

  async clear(): Promise<void> {
    try {
      await AsyncStorage.clear()
    } catch (error) {
      console.error('Error clearing AsyncStorage:', error)
      throw error
    }
  }
}

export const asyncStorageService = AsyncStorageService.getInstance()
export { AsyncStorageService }
