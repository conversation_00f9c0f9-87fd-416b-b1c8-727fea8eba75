import { Text } from '@/components/ui/Text/Text'
import React from 'react'
import { useTranslation } from 'react-i18next'
import { View } from 'react-native'
import { KeyboardAwareScrollView } from 'react-native-keyboard-controller'
import { SafeAreaView, useSafeAreaInsets } from 'react-native-safe-area-context'
import { LanguageSelector } from '../../components/LanguageSelector/LanguageSelector'
import { LoginPrompt } from '../../components/Register/LoginPrompt'
import { RegisterForm } from '../../components/Register/RegisterForm'
export const RegisterScreen = () => {
  const { t } = useTranslation()
  const insets = useSafeAreaInsets()
  return (
    <SafeAreaView className="flex-1 bg-white" edges={['bottom', 'left', 'right']}>
      <KeyboardAwareScrollView
        bottomOffset={insets.bottom + 16}
        showsVerticalScrollIndicator={false}
        className="flex-1 bg-white px-4"
      >
        <View className="flex-col gap-y-2 py-10">
          <Text size="heading7" variant="primary">
            {t('MES-406')}
          </Text>
          <Text size="body7" variant="subdued">
            {t('MES-647')}
          </Text>
        </View>
        <View className="flex-col  gap-y-3">
          <RegisterForm />
          <LoginPrompt />
          <LanguageSelector />
        </View>
      </KeyboardAwareScrollView>
    </SafeAreaView>
  )
}
