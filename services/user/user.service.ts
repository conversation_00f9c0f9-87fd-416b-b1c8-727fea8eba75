import { API_ENDPOINTS } from '@/constants/endpoint.constant'

import { httpService } from '@/services/http/http.service'
import {
  ChangePasswordRequest,
  ChangePasswordResponse,
  UpdateUserProfileRequest,
  UpdateUserProfileResponse,
} from '@/types/user.type'

// SERVER / CLIENT
class UserService {
  private static instance: UserService

  private constructor() {}

  public static getInstance(): UserService {
    if (!UserService.instance) {
      UserService.instance = new UserService()
    }
    return UserService.instance
  }

  /**
   * Update user profile using the HTTP service
   *
   * @param payload - The payload to update user profile.
   * @returns A promise resolving to UpdateUserProfileResponse or null in case of an error.
   */
  async updateUserProfile(payload: UpdateUserProfileRequest): Promise<UpdateUserProfileResponse> {
    const formData = new FormData()

    const { avatar, ...userData } = payload

    formData.append('_payload', JSON.stringify(userData))

    if (avatar && typeof avatar !== 'string' && 'uri' in avatar) {
      formData.append('file', {
        uri: avatar.uri,
        name: avatar.name || 'avatar.jpg',
        type: avatar.type || 'image/jpeg',
      } as unknown as Blob)
    }

    const response = await httpService.patch<UpdateUserProfileResponse>(
      `/${API_ENDPOINTS.users_api}/update-profile`,
      formData,
      {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      },
    )
    return response
  }

  /**
   * Change user password
   *
   * @param payload - The payload containing old and new passwords
   * @returns A promise resolving to a success response
   */
  async changePassword(payload: ChangePasswordRequest): Promise<ChangePasswordResponse> {
    const formData = new FormData()
    formData.append('_payload', JSON.stringify(payload))
    const response = await httpService.patch<ChangePasswordResponse>(
      `/${API_ENDPOINTS.users_api}/change-pw`,
      formData,
      {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      },
    )
    return response
  }
}

export const userService = UserService.getInstance()
