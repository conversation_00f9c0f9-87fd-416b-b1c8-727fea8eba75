'use client'
import { Faculty } from "@/types/faculty.type";
import React, { useCallback, useState } from 'react';
import { LayoutAnimation, Platform, StyleSheet, TouchableOpacity, UIManager, View } from 'react-native';

import ArrowDownIcon from '@/assets/icons/arrow-down-icon.svg';
import ArrowUpIcon from '@/assets/icons/arrow-down-primary.svg';
import { Text } from "@/components/ui/Text/Text";
import { LocaleEnum } from "@/enums/locale.enum";
import { useAppLanguage } from "@/hooks/common/useAppLanguage";
import { LocalizeField } from "@/types/global.type";
import { BodyPart } from "@/types/body-part.type";

if (Platform.OS === 'android' && UIManager.setLayoutAnimationEnabledExperimental) {
    UIManager.setLayoutAnimationEnabledExperimental(true);
}

type MedicalFacultyItemProps = {
    faculty: Faculty;
}

export const MedicalFacultyItem = ({ faculty }: MedicalFacultyItemProps) => {
    const [isExpanded, setIsExpanded] = useState(false);

    const { primaryLanguage, secondaryLanguage } = useAppLanguage()

    const toggleExpansion = useCallback(() => {
        LayoutAnimation.configureNext(LayoutAnimation.Presets.easeInEaseOut);
        setIsExpanded(prev => !prev);
    }, []);

    const name = (faculty?.name as unknown as LocalizeField<string>)

    return (
        <View style={[
            styles.container,
            isExpanded ? styles.containerActive : styles.containerDeActive

        ]}>
            <TouchableOpacity
                onPress={toggleExpansion}
                style={styles.header}
                activeOpacity={0.8}
            >
                <Text numberOfLines={1} size='body6' style={[
                    isExpanded ? { color: '#1157C8' } : {}
                ]}>
                    {name[primaryLanguage as LocaleEnum]} { }
                    ({name[secondaryLanguage as LocaleEnum]})
                </Text>

                {isExpanded ? <ArrowUpIcon
                    style={{
                        transform: [{ rotate: '-90deg' }]
                    }} className=" size-4" /> : <ArrowDownIcon className="size-4" />}
            </TouchableOpacity>

            {isExpanded && (
                <View style={styles.content}>
                    { faculty?.bodyParts?.map((bodyPart) => {
                           const { name, id } = bodyPart as BodyPart
                           const localizedBodyPartName = name as unknown as LocalizeField<string>

                           return (
                            <View key={id}> 
                                
                            </View>
                           )
                    })

                    }
                    <Text size="body6" variant="default">
                        {faculty.description}
                    </Text>


                </View>
            )}
        </View>
    );
}

const styles = StyleSheet.create({
    container: {
        borderWidth: 1,
        borderRadius: 8,
        overflow: 'hidden',
    },
    containerActive: {
        borderColor: '#1157C8',
        backgroundColor: '#F1F6FE'
    },
    containerDeActive: {
        borderColor: 'transparent',
        backgroundColor: '#F9F9FC',
    }
    ,
    header: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        padding: 16,
        textOverflow: 'ellipsis',

    },
    content: {
        padding: 15,
        paddingTop: 0,
    },
    contentText: {
        fontSize: 14,
        lineHeight: 20,
        marginBottom: 5,
    }
});