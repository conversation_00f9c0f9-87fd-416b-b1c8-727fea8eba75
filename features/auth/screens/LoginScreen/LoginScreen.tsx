import React from 'react'
import { View } from 'react-native'

import { Text } from '@/components/ui/Text/Text'
import { useTranslation } from 'react-i18next'
import { KeyboardAwareScrollView } from 'react-native-keyboard-controller'
import { SafeAreaView, useSafeAreaInsets } from 'react-native-safe-area-context'
import { LanguageSelector } from '../../components/LanguageSelector/LanguageSelector'
import { LoginForm } from '../../components/Login/LoginForm'
import { OAuthLogin } from '../../components/Login/OAuthLogin'
import { RegisterPrompt } from '../../components/Login/RegisterPrompt'
const isProd = process.env.EXPO_PUBLIC_APP_ENVIROMENT === 'production'

export const LoginScreen = () => {
  const { t } = useTranslation()
  const insets = useSafeAreaInsets()
  return (
    <SafeAreaView className="flex-1 bg-white" edges={['bottom', 'left', 'right']}>
      <KeyboardAwareScrollView
        bottomOffset={insets.bottom + 16}
        showsVerticalScrollIndicator={false}
        className="flex-1 bg-white px-4"
      >
        <View className="flex-col gap-y-2 py-10">
          <Text size="heading7" variant="primary">
            {t('MES-06')}
          </Text>
          <Text size="body7" variant="subdued">
            {t('MES-646')}
          </Text>
        </View>
        <View className="flex-1 flex-col gap-y-3">
          <LoginForm />
          {isProd && <OAuthLogin />}
          <RegisterPrompt />
          <LanguageSelector />
        </View>
      </KeyboardAwareScrollView>
    </SafeAreaView>
  )
}
