import { API_ENDPOINTS } from '@/constants/endpoint.constant'
import { PaginatedDocs } from '@/types/global.type'

import { Params } from '@/types/http.type'

import { AxiosRequestConfig } from 'axios'
import { httpService } from '../../../services/http/http.service'
import { Post, PostCategory } from '../types'

// SERVER / CLIENT
class PostService {
  private static instance: PostService

  private constructor() {}

  public static getInstance(): PostService {
    if (!PostService.instance) {
      PostService.instance = new PostService()
    }
    return PostService.instance
  }

  /**
   * Fetch posts using the HTTP service.
   *
   * @param params - Optional query parameters for filtering or paginating posts.
   * @param options - Optional fetch options such as headers, method, etc.
   * @returns A promise resolving to PaginatedDocs<Post> or null in case of an error.
   */
  public async getPosts({
    params = {},
    options = {},
  }: {
    params?: Params
    options?: AxiosRequestConfig
  } = {}): Promise<PaginatedDocs<Post> | null> {
    const data = await httpService.get<PaginatedDocs<Post>>(`/${API_ENDPOINTS.posts_api}`, {
      params,
      ...options,
    })
    return data
  }

  /**
   * Fetch post-categories using the HTTP service.
   *
   * @param params - Optional query parameters for filtering or paginating post-categories.
   * @param options - Optional fetch options such as headers, method, etc.
   * @returns A promise resolving to PaginatedDocs<PostCategory> or null in case of an error.
   */
  public async getPostCategories({
    params = {},
    options = {},
  }: {
    params?: Params
    options?: AxiosRequestConfig
  } = {}): Promise<PaginatedDocs<PostCategory> | null> {
    const data = await httpService.get<PaginatedDocs<PostCategory>>(
      `/${API_ENDPOINTS.post_categories_api}`,
      {
        params,
        ...options,
      },
    )
    return data
  }

  public async getPostDetailsBySlug({
    slug,
    params = {},
    options = {},
  }: {
    slug: string
    params?: Params
    options?: AxiosRequestConfig
  }): Promise<Post | null> {
    const data = await httpService.get<Post>(`/${API_ENDPOINTS.posts_api}/details/${slug}`, {
      params,
      ...options,
    })
    return data
  }
}
export const postService = PostService.getInstance()
