import { useAuthentication } from '@/hooks/auth/useAuthentication'
import { MutationOptions, useMutation } from '@tanstack/react-query'
import { unifiedNotificationService } from '../../services/unified-notification.service'
import { useInvalidateNotificationQuery } from '../common/useInvalidateNotificationQuery'
import { unifiedNotificationMutationKeys } from './queryKeys'

export const useMarkAllChatNotificationsAsViewed = ({
  options,
}: {
  options?: Omit<MutationOptions | undefined, 'mutationFn' | 'mutationKey'>
} = {}) => {
  const { user } = useAuthentication()
  const { handleResetAllNotificationQueries } = useInvalidateNotificationQuery()
  const {
    isError: isMarkAllChatNotificationsAsViewedError,
    isPending: isMarkAllChatNotificationsAsViewedPending,
    mutate: markAllChatNotificationsAsViewedMutation,

    ...rest
  } = useMutation({
    mutationKey: unifiedNotificationMutationKeys['mark-all-chat-notifications-as-viewed'].base(),
    mutationFn: async () => {
      // Don't mark all as viewed if no user is logged in
      if (!user) {
        console.log(
          '🔥 useMarkAllChatNotificationsAsViewed: No user logged in, skipping mark all as viewed',
        )
        return Promise.resolve()
      }

      return unifiedNotificationService.markAllChatNotificationAsViewed()
    },
    onSuccess: () => {
      handleResetAllNotificationQueries()
    },
    ...options,
  })

  return {
    isMarkAllChatNotificationsAsViewedError,
    isMarkAllChatNotificationsAsViewedPending,
    markAllChatNotificationsAsViewedMutation,
    ...rest,
  }
}
