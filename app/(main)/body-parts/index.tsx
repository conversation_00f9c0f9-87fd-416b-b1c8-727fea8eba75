import { BaseWebView } from '@/components/BaseWebView/BaseWebView'
import { StackScreenBase } from '@/components/StackBase/StackScreenBase'
import { withAuthentication } from '@/hoc/withAuthentication'
import { WEBVIEW_APP_ROUTES } from '@/routes/webviewRoutes'
import React from 'react'
import { useTranslation } from 'react-i18next'
function BodyPartsScreen() {
  const { t } = useTranslation()
  return (
    <>
      <StackScreenBase
        options={{
          headerTitle: t('MES-36'),
        }}
      />
      <BaseWebView
        source={{ uri: WEBVIEW_APP_ROUTES.MEDICAL_HANDBOOK?.children?.BODY_PARTS?.path }}
      />
    </>
  )
}
export default withAuthentication(BodyPartsScreen)
