import { API_ENDPOINTS } from '@/constants/endpoint.constant'
import { httpService } from '@/services/http/http.service'
import { Fact } from '@/types/fact.type'
import { PaginatedDocs } from '@/types/global.type'
import { Params } from '@/types/http.type'
import { AxiosRequestConfig } from 'axios'

class FactService {
  private static instance: FactService

  private constructor() {}

  public static getInstance(): FactService {
    if (!FactService.instance) {
      FactService.instance = new FactService()
    }
    return FactService.instance
  }

  /**
   * Fetch random facts using the HTTP service.
   *
   * @param params - Optional query parameters for filtering or paginating facts.
   * @param options - Optional fetch options such as headers, method, etc.
   * @returns A promise resolving to PaginatedDocs<Fact> or null in case of an error.
   */
  public async getRandomFacts({
    params = {},
    options = {},
  }: {
    params?: Params
    options?: AxiosRequestConfig
  } = {}): Promise<PaginatedDocs<Fact> | null> {
    const data = await httpService.get<PaginatedDocs<Fact>>(
      `/${API_ENDPOINTS.facts_api}/random-facts`,
      { params, ...options },
    )
    return data
  }
}

export const factService = FactService.getInstance()
