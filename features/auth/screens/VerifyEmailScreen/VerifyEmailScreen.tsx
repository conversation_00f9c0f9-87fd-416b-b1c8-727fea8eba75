import ArrowLeftIcon from '@/assets/auth/arrow-left-icon.svg'
import RefreshIcon from '@/assets/auth/refresh-icon.svg'
import { Text } from '@/components/ui/Text/Text'
import { useAppLanguage } from '@/hooks/common/useAppLanguage'
import { APP_ROUTES } from '@/routes/appRoutes'
import { authService } from '@/services/auth/auth.service'
import { primary, text } from '@/styles/_colors'
import { LinkProps, router, useLocalSearchParams } from 'expo-router'
import React, { useEffect, useState } from 'react'
import { useTranslation } from 'react-i18next'
import { ActivityIndicator, Image, SafeAreaView, TouchableOpacity, View } from 'react-native'
import Toast from 'react-native-toast-message'

export const VerifyEmailScreen = () => {
  const { primaryLanguage } = useAppLanguage()
  const { t } = useTranslation()
  const { email } = useLocalSearchParams()
  // 5 minutes countdown (300 seconds)
  const [timeLeft, setTimeLeft] = useState(300)
  const [canResend, setCanResend] = useState(true)
  const [isSending, setIsSending] = useState(false)
  // Countdown timer
  useEffect(() => {
    if (timeLeft > 0) {
      const timer = setTimeout(() => {
        setTimeLeft(timeLeft - 1)
      }, 1000)
      return () => clearTimeout(timer)
    } else {
      setCanResend(true)
    }
  }, [timeLeft])

  const formatTime = (seconds: number) => {
    const minutes = Math.floor(seconds / 60)
    const remainingSeconds = seconds % 60
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`
  }

  const handleResend = async () => {
    if (canResend && email) {
      try {
        setIsSending(true)
        const response = await authService.resendVerifyEmail({
          email: email as string,
          language: primaryLanguage,
        })
        if (response.success) {
          Toast.show({
            type: 'success',
            text1: t('MES-305'),
            text2: t('MES-186'),
          })
          setTimeLeft(300)
          setCanResend(false)
        } else {
          Toast.show({
            type: 'error',
            text1: t('MES-174'),
            text2: t(response.message as string),
          })
        }
      } catch (error: any) {
        Toast.show({
          type: 'error',
          text1: t('MES-174'),
          text2: error?.message,
        })
      } finally {
        setIsSending(false)
      }
    }
  }

  const handleGoBack = () => {
    router.replace({
      pathname: APP_ROUTES.HOME.path,
    } as LinkProps['href'])
  }

  return (
    <SafeAreaView className="flex-1 bg-white">
      {/* Header */}
      <View className=" h-[62px] flex-row items-center gap-3 p-4">
        <TouchableOpacity onPress={handleGoBack} className="h-6 w-6">
          <ArrowLeftIcon width={20} height={20} />
        </TouchableOpacity>
      </View>

      {/* Body */}
      <View className="flex-1 flex-col items-center gap-4 px-4 py-10">
        {/* Success Icon */}
        <View className="h-[152px] w-[152px] items-center justify-center rounded-full bg-custom-warning-100 p-3.5 shadow-lg">
          <Image
            source={require('@/assets/auth/email-confirmation.png')}
            className="h-[77px] w-[77px]"
            resizeMode="contain"
          />
        </View>

        {/* Title */}
        <Text variant="primary" size="heading7">
          {t('MES-205')}
        </Text>

        {/* Description */}
        <Text variant="subdued" size="body6">
          {t('MES-186')}
        </Text>

        {/* Timer and Resend Section */}
        <View className="flex-col items-center gap-3">
          <>
            {/* Resend when timer active */}
            <View className="mx-auto flex-row items-center justify-center gap-2">
              {canResend ? (
                <TouchableOpacity
                  className="flex-row items-center gap-2"
                  onPress={handleResend}
                  disabled={!canResend}
                >
                  <Text variant="primary" size="body6">
                    {t('MES-303')}
                  </Text>
                  {isSending ? (
                    <ActivityIndicator size="small" color={primary[500]} />
                  ) : (
                    <RefreshIcon
                      width={16}
                      height={16}
                      color={canResend ? primary[500] : text.subdued}
                    />
                  )}
                </TouchableOpacity>
              ) : (
                <View className="flex-row items-center justify-center gap-1">
                  <Text variant="subdued" size="body6">
                    {t('MES-308')}:{' '}
                  </Text>
                  <View className="w-[40px]">
                    <Text variant="error" size="body6">
                      {formatTime(timeLeft)}
                    </Text>
                  </View>
                </View>
              )}
            </View>
          </>
        </View>
      </View>
    </SafeAreaView>
  )
}
