import { PaginatedDocs } from '@/types/global.type'
import { Params } from '@/types/http.type'
import { InfiniteData, UseInfiniteQueryOptions, useInfiniteQuery } from '@tanstack/react-query'
import { AxiosRequestConfig } from 'axios'
import { commentService } from '../../services/comment.service'
import { Comment } from '../../types'
import { commentQueryKeys } from './queryKeys'

type NestedCommentsQueryConfig = Partial<
  Omit<
    UseInfiniteQueryOptions<PaginatedDocs<Comment>, Error, InfiniteData<PaginatedDocs<Comment>>>,
    'queryFn' | 'queryKey' | 'getNextPageParam' | 'initialPageParam'
  >
>

interface UseGetInfiniteNestedCommentsProps {
  parentId: string
  relationTo?: 'keywords' | 'posts' | 'products'
  params?: Params
  options?: AxiosRequestConfig
  key?: string | number
  config?: NestedCommentsQueryConfig
  overrideKey?: (string | Params)[]
}

export const useGetInfiniteNestedComments = ({
  parentId,
  relationTo,
  params = {},
  options = {},
  config = {},
  overrideKey,
}: UseGetInfiniteNestedCommentsProps) => {
  const {
    isError: isGetNestedCommentsError,
    isLoading: isGetNestedCommentsLoading,
    isFetching: isGetNestedCommentsFetching,
    isFetchingNextPage: isGetNestedCommentsFetchingNextPage,
    data: nestedComments,
    fetchNextPage,
    hasNextPage,
    ...rest
  } = useInfiniteQuery({
    queryKey: overrideKey
      ? overrideKey
      : relationTo
        ? [commentQueryKeys['nested-comments'].base(), relationTo, parentId, params]
        : [commentQueryKeys['nested-comments'].base(), parentId, params],
    queryFn: async ({ pageParam }) => {
      const result = await commentService.getNestedComments({
        parentId,
        params: {
          ...params,
          cursor: pageParam, // use cursor instead of page
        },
        options,
      })
      return result
    },
    getNextPageParam: (lastPage) => lastPage?.nextCursor || undefined,
    getPreviousPageParam: () => undefined,
    initialPageParam: undefined,
    ...config,
  })

  return {
    isGetNestedCommentsError,
    isGetNestedCommentsLoading,
    isGetNestedCommentsFetching,
    isGetNestedCommentsFetchingNextPage,
    nestedComments,
    fetchNextPage,
    hasNextPage,
    ...rest,
  }
}
