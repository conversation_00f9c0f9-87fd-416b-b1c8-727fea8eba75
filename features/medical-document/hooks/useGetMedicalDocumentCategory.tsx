import { medicalDocumentService } from '@/features/medical-document/services/medical-document.service'
import { Params } from '@/types/http.type'
import { UseQueryOptions, useQuery } from '@tanstack/react-query'
import { AxiosRequestConfig } from 'axios'
import { MedicalDocumentCategory } from '../types'
import { medicalDocumentsQueryKeys } from './queryKeys'

export const useGetMedicalDocumentCategory = ({
  id,
  params = {},
  options = {},
  useQueryOptions,
}: {
  id: string
  params?: Params
  options?: AxiosRequestConfig
  useQueryOptions?: Omit<UseQueryOptions<MedicalDocumentCategory | null>, 'queryKey' | 'queryFn'>
}) => {
  const {
    isError: isGetMedicalDocumentCategoryError,
    isPending: isGetMedicalDocumentCategoryLoading,
    data: medicalDocumentCategory,
    ...rest
  } = useQuery({
    queryKey: [medicalDocumentsQueryKeys['medical-document-category'].base(), id, params],
    queryFn: async () =>
      medicalDocumentService.getMedicalDocumentCategory({
        id,
        params: params,
        ...options,
      }),
    params: params,
    ...options,
    ...useQueryOptions,
  })
  return {
    isGetMedicalDocumentCategoryError,
    isGetMedicalDocumentCategoryLoading,
    medicalDocumentCategory,
    ...rest,
  }
}
