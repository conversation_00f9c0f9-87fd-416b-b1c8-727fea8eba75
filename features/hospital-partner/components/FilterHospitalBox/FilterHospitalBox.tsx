import { useSheetActions } from '@/contexts/SheetContext/SheetContext'
import { useCallback, useEffect, useRef, useState } from 'react'
import { useTranslation } from 'react-i18next'
import { View } from 'react-native'
import { useShallow } from 'zustand/react/shallow'

import { BaseFilterListSection } from '@/components/Filter/BaseFilterListSection/BaseFilterListSection'
import {
  BaseFilterSheetBox,
  type BaseFilterSheetBoxRef,
} from '@/components/Filter/BaseFilterSheetBox/BaseFilterSheetBox'
import { BottomSheetScrollView } from '@gorhom/bottom-sheet'

import { useGetHomeFaculties } from '@/features/home/<USER>/query/home-faculty/useGetHomeFaculties'
import { useAppLanguage } from '@/hooks/common/useAppLanguage'
import { SearchHospitalFilterType, useHospitalStore } from '../../stores'
import { FilterKeywordFooter } from './FilterHospitalBoxFooter'

interface FilterHospitalBoxProps {
  closeSheet?: () => void
  sharedFooterRef: React.RefObject<View | null>
  sharedSheetBoxRef?: React.RefObject<BaseFilterSheetBoxRef | null>
}

export const FilterHospitalBox = ({
  closeSheet,
  sharedFooterRef,
  sharedSheetBoxRef,
}: FilterHospitalBoxProps) => {
  const { t } = useTranslation()
  const [searchInputValue, setSearchInputValue] = useState('')
  const localSheetBoxRef = useRef<BaseFilterSheetBoxRef>(null)
  const sheetBoxRef = sharedSheetBoxRef || localSheetBoxRef
  const { primaryLanguage } = useAppLanguage()
  const { homeFaculties, isGetHomeFacultiesLoading } = useGetHomeFaculties({
    params: {
      locale: primaryLanguage,
      pagination: false,
      sort: ['-name'],
      limit: 100,
    },
    useQueryOptions: {
      staleTime: 0,
    },
  })

  const { toggleTempSearchHospitalFilter, initTempSearchFilters, tempFilters } = useHospitalStore(
    useShallow((state) => ({
      toggleTempSearchHospitalFilter: state.toggleTempSearchHospitalFilter,
      initTempSearchFilters: state.initTempSearchFilters,
      tempFilters: state.tempSearchFilters,
    })),
  )

  // Initialize temp search filters when component mounts
  useEffect(() => {
    initTempSearchFilters()
  }, [initTempSearchFilters])

  // Trigger manual footer measurement when filters change (no re-render!)
  useEffect(() => {
    // Small delay to allow footer DOM updates to complete
    const timer = setTimeout(() => {
      sheetBoxRef.current?.measureFooter()
    }, 100)

    return () => clearTimeout(timer)
  }, [tempFilters])

  return (
    <BaseFilterSheetBox
      ref={sheetBoxRef}
      title={t('MES-481')}
      onClose={closeSheet}
      footerRef={sharedFooterRef}
      enableFooterHeightMeasurement={true}
      footerHeightPadding={40}
      useBottomSheetScrollView={true}
    >
      <BottomSheetScrollView className="relative" showsVerticalScrollIndicator={false}>
        <View className="flex flex-col gap-y-3 px-4">
          <BaseFilterListSection
            withSearchInput={true}
            isLoading={isGetHomeFacultiesLoading}
            title={t('MES-564')}
            data={homeFaculties?.docs || []}
            onSelectFilter={(item) =>
              toggleTempSearchHospitalFilter(SearchHospitalFilterType.FACULTY_TYPE, {
                id: item.id,
                label: item.name,
                type: SearchHospitalFilterType.FACULTY_TYPE,
              })
            }
            activeFilters={tempFilters?.[SearchHospitalFilterType.FACULTY_TYPE]?.map(
              (filter) => filter.id,
            )}
            idKey="id"
            labelKey="name"
          />
        </View>
      </BottomSheetScrollView>
    </BaseFilterSheetBox>
  )
}

export const useOpenFilterHospitalBox = () => {
  const { openCustomSheet, closeSheet } = useSheetActions()

  const sharedFooterRef = useRef<View>(null)
  const sharedSheetBoxRef = useRef<BaseFilterSheetBoxRef>(null)

  const handleOpenFilterHospitalBox = useCallback(() => {
    openCustomSheet({
      children: ({ close }) => (
        <FilterHospitalBox
          closeSheet={close}
          sharedFooterRef={sharedFooterRef}
          sharedSheetBoxRef={sharedSheetBoxRef}
        />
      ),
      baseProps: {
        snapPoints: ['85%', '100%'],
        enableHandlePanningGesture: true,
        enableDynamicSizing: false,
        enableOverDrag: false,
      },
      options: {
        footerComponent: (props) => {
          return <FilterKeywordFooter ref={sharedFooterRef} closeSheet={closeSheet} {...props} />
        },
      },
    })
  }, [openCustomSheet])

  return { handleOpenFilterHospitalBox }
}
