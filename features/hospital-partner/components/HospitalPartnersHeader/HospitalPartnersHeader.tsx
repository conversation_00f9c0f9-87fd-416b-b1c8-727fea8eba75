import BackIcon from '@/assets/icons/back-screen-primary-icon.svg'
import { Text } from '@/components/ui/Text/Text'
import { useRouter } from 'expo-router'
import { useTranslation } from 'react-i18next'
import { TouchableOpacity, View } from 'react-native'
export default function HospitalPartnersHeader() {
  const { t } = useTranslation()
  const { back } = useRouter()
  return (
    <View className="h-[68px] flex-row items-center gap-x-2 px-4 pb-4">
      <TouchableOpacity onPress={() => back()}>
        <BackIcon />
      </TouchableOpacity>
      <View>
        <Text size="heading7" variant="primary">
          {t('MES-804')}
        </Text>
      </View>
    </View>
  )
}
