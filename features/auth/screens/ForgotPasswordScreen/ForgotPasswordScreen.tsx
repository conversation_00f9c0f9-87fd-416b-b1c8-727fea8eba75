import React from 'react'
import { KeyboardAvoidingView, Platform, ScrollView, View } from 'react-native'

import { Text } from '@/components/ui/Text/Text'
import { useTranslation } from 'react-i18next'
import { SafeAreaView } from 'react-native-safe-area-context'
import { ForgotPasswordForm } from '../../components/ForgotPasswordForm/ForgotPasswordForm'
import { LanguageSelector } from '../../components/LanguageSelector/LanguageSelector'
import { OAuthLogin } from '../../components/Login/OAuthLogin'
import { RegisterPrompt } from '../../components/Login/RegisterPrompt'
const isProd = process.env.EXPO_PUBLIC_APP_ENVIROMENT === 'production'
export const ForgotPasswordScreen = () => {
  const { t } = useTranslation()
  return (
    <SafeAreaView className="flex-1 bg-white">
      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        className="flex-1"
      >
        <ScrollView showsVerticalScrollIndicator={false} className="flex-1 bg-white px-4">
          <View className="mb-6 mt-8 flex-col  gap-y-2">
            <Text size="heading7" variant="primary">
              {t('MES-177')}
            </Text>

            <Text size="body7" variant="subdued">
              {t('MES-297')}
            </Text>
          </View>
          <View className="flex-1 flex-col gap-y-3">
            <ForgotPasswordForm />
            {isProd && <OAuthLogin />}
            <RegisterPrompt />
            <LanguageSelector />
          </View>
        </ScrollView>
      </KeyboardAvoidingView>
    </SafeAreaView>
  )
}
