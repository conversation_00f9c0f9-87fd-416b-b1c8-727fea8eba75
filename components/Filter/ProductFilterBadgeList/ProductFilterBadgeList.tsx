import { SelectedFilterBadge } from '@/features/product/components/FilterProductBox/SelectedFilterBadge'
import { ProductFilterItem, SearchProductFilterItem } from '@/features/product/stores/ProductStore'
import { BaseFilterBadgeList, BaseFilterItem } from '../BaseFilterBadgeList/BaseFilterBadgeList'

// Union type for both filter item types - they should extend BaseFilterItem
type FilterItem = (ProductFilterItem | SearchProductFilterItem) & BaseFilterItem

// Custom badge component that uses the existing SelectedFilterBadge
const ProductFilterBadge = ({
  filterItem,
  onClearFilter,
}: {
  filterItem: FilterItem
  onClearFilter?: (item: FilterItem) => void
}) => {
  return <SelectedFilterBadge filterItem={filterItem} onClearFilter={onClearFilter} />
}

interface ProductFilterBadgeListProps {
  activeFilters: FilterItem[]
  onClearFilter: (filterItem: FilterItem) => void
  maxDisplayCount?: number
}

export const ProductFilterBadgeList = ({
  activeFilters,
  onClearFilter,
  maxDisplayCount = 5,
}: ProductFilterBadgeListProps) => {
  if (!activeFilters.length) {
    return null
  }
  return (
    <BaseFilterBadgeList
      activeFilters={activeFilters}
      onClearFilter={onClearFilter}
      maxDisplayCount={maxDisplayCount}
      BadgeComponent={ProductFilterBadge}
    />
  )
}

// Export the type for use in other components
export type { FilterItem as ProductFilterUnion }
