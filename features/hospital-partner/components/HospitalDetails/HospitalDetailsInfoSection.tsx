import LocationIcon from '@/assets/icons/location-icon.svg'
import { Text } from '@/components/ui/Text/Text'
import { BLURHASH_CODE } from '@/constants/global.constant'
import { StyledExpoImage } from '@/libs/styled'
import { Media } from '@/types/media.type'
import { View } from 'react-native'
import { Hospital } from '../../types'
interface HospitalDetailsInfoSectionProps {
  hospital: Hospital
}
export const HospitalDetailsInfoSection = ({ hospital }: HospitalDetailsInfoSectionProps) => {
  const { address, name, japaneseName, bannerImage, logo, description } = hospital || {}
  const { url, thumbnailURL } = bannerImage as Media
  const bannerImageUrl = url || thumbnailURL

  const { url: logoUrl, thumbnailURL: logoThumbnailURL, width, height } = logo as Media
  const logoImageUrl = logoUrl || logoThumbnailURL

  return (
    <View className="px-4">
      {/*  Banner */}
      <View className="relative overflow-hidden rounded-xl">
        <View className="aspect-video  w-full  bg-neutral-50">
          {bannerImageUrl && (
            <StyledExpoImage
              source={{ uri: bannerImageUrl }}
              className="h-full w-full"
              transition={300}
              placeholder={BLURHASH_CODE}
              alt={name + 'banner'}
            />
          )}
        </View>
        {logoImageUrl && (
          <View className="absolute  right-[10px] top-[10px] rounded-lg bg-white p-2">
            <StyledExpoImage
              source={{ uri: logoImageUrl }}
              style={{
                height: 36,
                width: width && height ? (36 * width) / height : 180,
              }}
              transition={300}
              placeholder={BLURHASH_CODE}
              alt={name + 'logo'}
            />
          </View>
        )}
      </View>
      {logoImageUrl && <View></View>}
      {/* Information */}
      <View className="p-3">
        <Text size="body3" variant="default" className="mb-1">
          {name}
        </Text>
        <Text size="body9" variant="subdued" className="mb-3">
          {japaneseName}
        </Text>
        <View className="mb-3 flex flex-row items-center gap-3">
          <LocationIcon width={18} height={18} />
          <Text size="body7" variant="subdued">
            {address}
          </Text>
        </View>
        <Text size="body7" variant="default">
          {description}
        </Text>
      </View>
    </View>
  )
}
