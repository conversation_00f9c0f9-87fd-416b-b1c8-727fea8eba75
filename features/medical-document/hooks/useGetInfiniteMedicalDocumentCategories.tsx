import { Params } from '@/types/http.type'
import { InfiniteData, UseInfiniteQueryOptions, useInfiniteQuery } from '@tanstack/react-query'

import { PaginatedDocs } from '@/types/global.type'
import { AxiosRequestConfig } from 'axios'

import { medicalDocumentService } from '../services/medical-document.service'
import { MedicalDocumentCategory } from '../types'
import { medicalDocumentsQueryKeys } from './queryKeys'

export type MedicalDocumentCategoriesQueryConfig = Omit<
  UseInfiniteQueryOptions<
    PaginatedDocs<MedicalDocumentCategory>,
    Error,
    InfiniteData<PaginatedDocs<MedicalDocumentCategory>>,
    (string | Params)[],
    number
  >,
  'queryFn' | 'queryKey' | 'getNextPageParam' | 'initialPageParam'
>

interface UseGetInfiniteMedicalDocumentCategoriesProps {
  params?: Params
  options?: AxiosRequestConfig
  key?: string | number
  config?: MedicalDocumentCategoriesQueryConfig
  overrideKey?: (string | Params)[]
}

export const useGetInfiniteMedicalDocumentCategories = ({
  params = {},
  options = {},
  config = {},
  overrideKey,
}: UseGetInfiniteMedicalDocumentCategoriesProps = {}) => {
  const {
    isError: isGetMedicalDocumentCategoriesError,
    isFetching: isGetMedicalDocumentCategoriesLoading,
    data: medicalDocumentCategories,
    fetchNextPage,
    hasNextPage,
    ...rest
  } = useInfiniteQuery({
    queryKey: overrideKey
      ? overrideKey
      : [medicalDocumentsQueryKeys['medical-document-categories'].base(), params],
    queryFn: async ({ pageParam = 1 }) => {
      return medicalDocumentService.getMedicalDocumentCategories({
        params: {
          ...params,
          page: pageParam,
        },
        options,
      })
    },
    getNextPageParam: (lastPage) => lastPage?.nextPage,
    getPreviousPageParam: (lastPage) => lastPage?.prevPage,
    initialPageParam: 1,
    ...config,
  })

  return {
    isGetMedicalDocumentCategoriesError,
    isGetMedicalDocumentCategoriesLoading,
    medicalDocumentCategories,
    fetchNextPage,
    hasNextPage,
    ...rest,
  }
}
