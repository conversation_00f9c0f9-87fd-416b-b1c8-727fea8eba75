import { useEffect } from 'react'
import { View } from 'react-native'
import { FavoriteProduct } from '../../components/FavoriteProduct/FavoriteProduct'
import { useFavoriteProductStore } from '../../stores/FavoriteProductStore'

export const FavoriteProductScreen = () => {
  const { clearAllFavoriteSearchProductFiltersAndSearchText } = useFavoriteProductStore()

  useEffect(() => {
    return () => {
      clearAllFavoriteSearchProductFiltersAndSearchText()
    }
  }, [])

  return (
    <View className="flex flex-1 bg-white px-4 py-3">
      <FavoriteProduct></FavoriteProduct>
    </View>
  )
}
