import ArrowLeftIcon from '@/assets/icons/arrow-left-primary.svg'
import { router, Stack } from 'expo-router'
import React from 'react'
import { TouchableOpacity, View } from 'react-native'
type StackScreenProps = React.ComponentProps<typeof Stack.Screen>

export const StackScreenBase = (props: StackScreenProps) => {
  const { options, ...rest } = props
  return (
    <Stack.Screen
      options={{
        // headerBackTitle: t('MES-77'),
        headerBackButtonDisplayMode: 'minimal',
        headerShown: true,
        headerLeft: () => <BackButton />,
        ...options,
      }}
      {...rest}
    />
  )
}
// IOS Only
export const BackButton = () => {
  return (
    <View>
      <TouchableOpacity
        onPressIn={() => {
          if (router.canGoBack()) {
            router.back()
          } else {
            router.replace('/')
          }
        }}
        hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
      >
        <View className="bg-rp aspect-square p-3  pl-0">
          <ArrowLeftIcon width={18} height={18} />
        </View>
      </TouchableOpacity>
    </View>
  )
}
