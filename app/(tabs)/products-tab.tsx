import { ProductScreen } from '@/features/product/screens/ProductScreen/ProductScreen'
import { MainLayout } from '@/layouts/MainLayout/MainLayout'
import React from 'react'

export default function MedicineScreen() {
  return (
    // <BaseWebView
    //   source={{
    //     uri: WEBVIEW_APP_ROUTES.PRODUCTS?.children?.MEDICINES?.path,
    //   }}
    //   isTabScreen
    // />
    <MainLayout withHeader={false}>
      <ProductScreen />
    </MainLayout>
  )
}
