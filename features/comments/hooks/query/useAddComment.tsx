import { MutationOptions, useMutation } from '@tanstack/react-query'
import { useRef } from 'react'
import { commentService } from '../../services/comment.service'
import { AddCommentPayload } from '../../types'
import { commentMutationKeys } from './queryKeys'

type AddCommentVariables = { documentId: string; relationTo: string; payload: AddCommentPayload }

export const useAddComment = ({
  options,
}: {
  options?: Omit<MutationOptions | undefined, 'mutationFn' | 'mutationKey'>
} = {}) => {
  // Define an AbortController to cancel previous requests
  const abortControllerRef = useRef<AbortController | null>(null)

  const {
    isError: isAddCommentError,
    isPending: isAddCommentPending,
    mutate: addCommentMutation,
    isSuccess: isAddCommentSuccess,
    ...rest
  } = useMutation({
    mutationKey: commentMutationKeys['addComment'].base(),
    mutationFn: async ({ documentId, relationTo, payload }: AddCommentVariables) => {
      // Abort any ongoing request before initiating a new one
      if (abortControllerRef.current) {
        abortControllerRef.current.abort()
      }

      // Create a new AbortController for the new request
      const abortController = new AbortController()
      abortControllerRef.current = abortController

      // Pass the signal from AbortController to the service method
      return commentService.addComment(documentId, relationTo, payload, {
        signal: abortControllerRef.current.signal,
      })
    },
    ...options,
  })

  return {
    isAddCommentError,
    isAddCommentPending,
    addCommentMutation,
    isAddCommentSuccess,
    ...rest,
  }
}
