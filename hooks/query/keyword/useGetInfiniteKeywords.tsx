import { Params } from '@/types/http.type'
import { InfiniteData, UseInfiniteQueryOptions, useInfiniteQuery } from '@tanstack/react-query'

import { keywordService } from '@/services/handbook/keyword.service'
import { PaginatedDocs } from '@/types/global.type'
import { Keyword } from '@/types/keyword.type'
import { AxiosRequestConfig } from 'axios'
import { keywordQueryKeys } from './queryKeys'

export type KeywordQueryConfig = Omit<
  UseInfiniteQueryOptions<
    PaginatedDocs<Keyword>,
    Error,
    InfiniteData<PaginatedDocs<Keyword>>,
    (string | Params)[],
    number
  >,
  'queryFn' | 'queryKey' | 'getNextPageParam' | 'initialPageParam'
>

interface UseGetInfiniteKeywordsProps {
  params?: Params
  options?: AxiosRequestConfig
  key?: string | number
  config?: KeywordQueryConfig
  overrideKey?: (string | Params)[]
}

/**
 * Custom hook for fetching paginated keywords data
 * @param params - Query parameters for filtering and pagination
 * @param options - Request initialization options
 * @param key - Optional cache key
 * @param config - React Query configuration options
 * @param overrideKey - Override the query key
 * @returns Object containing keyword data and pagination controls
 */
export const useGetInfiniteKeywords = ({
  params = {},
  options = {},
  config = {},
  overrideKey,
}: UseGetInfiniteKeywordsProps = {}) => {
  const {
    isError: isGetKeywordsError,
    isFetching: isGetKeywordsLoading,
    data: keywords,
    fetchNextPage,
    hasNextPage,
    ...rest
  } = useInfiniteQuery({
    queryKey: overrideKey ? overrideKey : [keywordQueryKeys['keywords'].base(), params],
    queryFn: async ({ pageParam = 1 }) => {
      return keywordService.getKeywords({
        params: {
          ...params,
          page: pageParam,
        },
        options,
      })
    },
    getNextPageParam: (lastPage) => lastPage?.nextPage,
    getPreviousPageParam: (lastPage) => lastPage?.prevPage,
    initialPageParam: 1,
    ...config,
  })

  return {
    isGetKeywordsError,
    isGetKeywordsLoading,
    keywords,
    fetchNextPage,
    hasNextPage,
    ...rest,
  }
}
