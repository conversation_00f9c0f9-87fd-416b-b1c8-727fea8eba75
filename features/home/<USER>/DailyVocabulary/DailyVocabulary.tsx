import DictionaryBookV2Icon from '@/assets/icons/dictionary-book-v2-icon.svg'

import { Skeleton } from '@/components/ui/Skeleton/Skeleton'
import { Text } from '@/components/ui/Text/Text'
import { useGetRandomKeywords } from '@/hooks/query/keyword/useGetRandomKeywords'
import React from 'react'
import { useTranslation } from 'react-i18next'
import { FlatList, ScrollView, View } from 'react-native'
import KeyworCard from './KeyworCard'

export const DailyVocabulary = () => {
  const { t } = useTranslation()

  const { randomKeywords, isGetRandomKeywordsLoading } = useGetRandomKeywords({
    params: {
      locale: 'all',
      limit: 5,
      depth: 5,
      convertDescriptionHTML: true,
      select: {
        name: true,
        id: true,
        hiragana: true,
        audio: true,
        relatedImages: true,
        description: true,
      },
    },
    useQueryOptions: {
      staleTime: 5 * 60 * 1000,
    },
  })

  if (!isGetRandomKeywordsLoading && !randomKeywords?.docs.length) {
    return null
  }

  return (
    <View className="flex-col gap-5 px-4">
      <View className="flex-row items-center gap-3">
        <Text size="heading8" variant="primary">
          {t('MES-751')}
        </Text>
        <DictionaryBookV2Icon width={24} height={24} />
      </View>
      {isGetRandomKeywordsLoading ? (
        <DailyVocabularyLoading />
      ) : (
        <ScrollView
          horizontal
          showsHorizontalScrollIndicator={false}
          contentContainerStyle={{ paddingRight: 16, gap: 16 }}
          decelerationRate="fast"
          snapToInterval={300 + 16}
        >
          {randomKeywords?.docs.map((keyword) => (
            <KeyworCard key={keyword.id} keyword={keyword} />
          ))}
        </ScrollView>
      )}
    </View>
  )
}

const DailyVocabularyLoading = () => {
  return (
    <FlatList
      horizontal
      pagingEnabled
      data={new Array(4).fill(0)}
      keyExtractor={(_, index) => index.toString()}
      showsHorizontalScrollIndicator={false}
      decelerationRate="fast"
      snapToInterval={300 + 12}
      contentContainerStyle={{
        gap: 12,
      }}
      renderItem={({ index }) => (
        <Skeleton key={index} className="h-[120px] w-[300px] rounded-lg bg-gray-200" />
      )}
    />
  )
}
