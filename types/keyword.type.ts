import { LocaleEnum } from '@/enums/locale.enum'
import { Media } from './media.type'
import { User } from './user.type'

export interface Keyword {
  id: string
  name: string
  hiragana: string
  editable?: boolean | null
  isCustom?: boolean | null
  popupType?: ('day' | 'week' | 'month' | 'year' | 'other' | 'description' | 'fever') | null
  description?: {
    root: {
      type: string
      children: {
        type: string
        version: number
        [k: string]: unknown
      }[]
      direction: ('ltr' | 'rtl') | null
      format: 'left' | 'start' | 'center' | 'right' | 'end' | 'justify' | ''
      indent: number
      version: number
    }
    [k: string]: unknown
  } | null

  examples?:
    | {
        jaSentence: string
        translations?: Record<LocaleEnum, string | null>
        id?: string | null
      }[]
    | null
  unverified?: boolean | null
  /**
   * Select categories this keyword belongs to
   */
  categories?:
    | (
        | 'body_part'
        | 'medicine'
        | 'medicine_type'
        | 'allergy_type'
        | 'symptom_or_disease'
        | 'test_vaccine_treatment'
        | 'lifestyle'
        | 'medical_science'
        | 'other'
      )[]
    | null
  relatedKeywords?: (string | Keyword)[] | null
  relatedImages?: (string | Media)[] | null
  audio?:
    | {
        language?: ('vi' | 'ja') | null
        audio?: (string | null) | Media
        id?: string | null
      }[]
    | null
  searchCount?: number | null
  updatedAt: string
  createdAt: string
}

export interface CreateKeywordItem {
  name: {
    [key: string]: string
  }
  hiragana: string
}
export interface CreateMultipleKeywordsPayload {
  keywords: CreateKeywordItem[]
}
export interface CreateMultipleKeywordsResponse {
  doc: CreateKeywordItem[]
  newCount: number
  existingCount: number
  success: boolean
  message: string
}

export interface KeywordTextToSpeechPayload {
  keywordId: string
  language?: string
}

export interface KeywordTextToSpeechResponse {
  url: string
}

export interface KeywordV2 extends Keyword {
  isFavorite?: boolean
}

export interface CheckKeywordExistResponse {
  exist: boolean
}

export interface FavoriteKeyword {
  id: string
  name?: string | null
  user?: (string | null) | User
  keyword: string | Keyword
  updatedAt: string
  createdAt: string
}

export interface GetKeywordAudioResponse {
  url: string
}
