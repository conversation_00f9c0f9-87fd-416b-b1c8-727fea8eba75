import { API_ENDPOINTS } from '@/constants/endpoint.constant'
import { httpService } from '@/services/http/http.service'
import { Faculty } from '@/types/faculty.type'
import { PaginatedDocs } from '@/types/global.type'
import { Params } from '@/types/http.type'
import { AxiosRequestConfig } from 'axios'

class HomeFacultyService {
  private static instance: HomeFacultyService

  private constructor() {}

  public static getInstance(): HomeFacultyService {
    if (!HomeFacultyService.instance) {
      HomeFacultyService.instance = new HomeFacultyService()
    }
    return HomeFacultyService.instance
  }

  /**
   * Fetch home faculties using the HTTP service.
   *
   * @param params - Optional query parameters for filtering or paginating home faculties.
   * @param options - Optional fetch options such as headers, method, etc.
   * @returns A promise resolving to PaginatedDocs<Faculty> or null in case of an error.
   */
  public async getHomeFaculties({
    params = {},
    options = {},
  }: {
    params?: Params
    options?: AxiosRequestConfig
  } = {}): Promise<PaginatedDocs<Faculty> | null> {
    const data = await httpService.get<PaginatedDocs<Faculty>>(
      `/${API_ENDPOINTS.faculties_api}/home-faculties`,
      { params, ...options },
    )
    return data
  }
}

export const homeFacultyService = HomeFacultyService.getInstance()
