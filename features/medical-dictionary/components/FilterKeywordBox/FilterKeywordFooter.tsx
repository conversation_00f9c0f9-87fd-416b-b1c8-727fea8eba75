import { BaseFilterBadgeList } from '@/components/Filter/BaseFilterBadgeList/BaseFilterBadgeList'
import { BaseFilterFooterActions } from '@/components/Filter/BaseFilterFooterActions/BaseFilterFooterActions'
import { BottomSheetFooter, BottomSheetFooterProps } from '@gorhom/bottom-sheet'
import * as Haptics from 'expo-haptics'
import { forwardRef, memo, useMemo } from 'react'
import { useTranslation } from 'react-i18next'
import { View } from 'react-native'
import { useSafeAreaInsets } from 'react-native-safe-area-context'
import { useShallow } from 'zustand/react/shallow'
import { useMedicalDictionaryStore } from '../../stores/MedicalDictionaryStore'

interface FilterKeywordFooterProps extends BottomSheetFooterProps {
  closeSheet?: () => void
}

export const FilterKeywordFooter = memo(
  forwardRef<View, FilterKeywordFooterProps>(({ closeSheet, ...props }, ref) => {
    return (
      <BottomSheetFooter {...props}>
        <FilterKeywordFooterBox ref={ref} closeSheet={closeSheet} {...props} />
      </BottomSheetFooter>
    )
  }),
)

interface FilterKeywordFooterProps extends BottomSheetFooterProps {
  closeSheet?: () => void
  onLayout?: () => void
}

const FilterKeywordFooterBox = forwardRef<View, FilterKeywordFooterProps>(
  ({ closeSheet, onLayout }, ref) => {
    const { t } = useTranslation()
    const { tempFilters, applyTempFilters, clearAllTempFilters, toggleTempFilter } =
      useMedicalDictionaryStore(
        useShallow((state) => ({
          tempFilters: state.tempFilters,
          applyTempFilters: state.applyTempFilters,
          clearAllTempFilters: state.clearAllTempFilters,
          toggleTempFilter: state.toggleTempFilter,
        })),
      )

    const handleClearFilter = (filterItem: any) => {
      if (filterItem?.type) {
        toggleTempFilter(filterItem.type, filterItem)
      }
    }

    const handleApplyFilters = () => {
      applyTempFilters()
      closeSheet?.()
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light)
    }

    const handleClearAllTempFilters = () => {
      clearAllTempFilters()
    }

    const activeFilters = useMemo(() => {
      return Object.values(tempFilters).filter(Boolean).flat()
    }, [tempFilters])

    const insets = useSafeAreaInsets()
    return (
      <View
        ref={ref}
        className="relative flex flex-col gap-y-4 border-t border-gray-200 bg-white px-4 py-4"
        style={{
          paddingBottom: insets.bottom + 8,
        }}
        onLayout={onLayout}
      >
        {/* Selected Filter Badges using ProductFilterBadgeList */}
        <BaseFilterBadgeList
          activeFilters={activeFilters ?? []}
          onClearFilter={handleClearFilter}
          maxDisplayCount={5}
        />

        {/* Footer Actions using BaseFilterKeywordFooterActions */}
        <BaseFilterFooterActions
          onApply={handleApplyFilters}
          onReset={handleClearAllTempFilters}
          applyText={t('MES-281')}
          resetText={t('MES-105')}
        />
      </View>
    )
  },
)

FilterKeywordFooterBox.displayName = 'FilterKeywordFooter'
FilterKeywordFooter.displayName = 'FilterKeywordFooter'
