import CloseIconDanger from '@/assets/icons/close-icon-danger.svg'
import EmptyBoxIcon from '@/assets/icons/empty-box.svg'
import { Skeleton } from '@/components/ui/Skeleton/Skeleton'
import { Text } from '@/components/ui/Text/Text'
import { useAppLanguage } from '@/hooks/common/useAppLanguage'
import colors from '@/styles/_colors'
import React, { useCallback, useMemo, useState } from 'react'
import { useTranslation } from 'react-i18next'
import { ActivityIndicator, FlatList, RefreshControl, TouchableOpacity, View } from 'react-native'
import { useShallow } from 'zustand/react/shallow'
import { useGetInfiniteHospitalPartners } from '../../hooks/query/useGetInfinteHospitaPartners'
import {
  SearchHospitalFilterItem,
  SearchHospitalFilterType,
  useHospitalStore,
} from '../../stores/HospitalStore'
import { Hospital } from '../../types'
// import { SelectedFilterBadge } from '../FilterHospitalBox/SelectedFilterBadge'
import { BaseSelectedFilterBadge } from '@/components/Filter/BaseSelectedFilterBadge/BaseSelectedFilterBadge'
import { HospitalPartnersItem } from '../HospitalPartners/HospitalPartnersItem'

type ListItem =
  | { type: 'hospital'; hospital: Hospital }
  | { type: 'loading' }
  | { type: 'loading_skeleton' }

export const SearchHospitalList = () => {
  const { primaryLanguage } = useAppLanguage()
  const { t } = useTranslation()
  const [_, setHeaderHeight] = useState(0)
  const [refreshing, setRefreshing] = useState(false)
  const {
    searchTextValue,
    searchHospitalFilters,
    toggleSearchHospitalFilter,
    hasActiveFilters,
    clearAllSearchHospitalFilters,
  } = useHospitalStore(
    useShallow((state) => ({
      searchTextValue: state.searchHospitalFilters['search_text'],
      searchHospitalFilters: state.searchHospitalFilters,
      toggleSearchHospitalFilter: state.toggleSearchHospitalFilter,
      hasActiveFilters: state.hasActiveFilters,
      clearAllSearchHospitalFilters: state.clearAllSearchHospitalFilters,
    })),
  )

  const {
    hospitalPartners: filteredHospitals,
    isGetHospitalPartnersLoading,
    isGetHospitalPartnersError,
    fetchNextPage,
    hasNextPage,
    isFetchingNextPage: isGetHospitalPartnersFetchingNextPage,
    isRefetching,
    refetch,
  } = useGetInfiniteHospitalPartners({
    config: {
      staleTime: 5 * 60 * 1000,
      enabled: Boolean(hasActiveFilters || (searchTextValue && searchTextValue.length > 0)),
    },
    params: {
      locale: primaryLanguage,
      limit: 12,
      // faculties:
      //   searchHospitalFilters[SearchHospitalFilterType.FACULTY_TYPE]?.map((filter) => filter.id) ||
      //   undefined,
      where: {
        and: [
          {
            or: [
              {
                name: {
                  like: searchTextValue ? searchTextValue : undefined,
                },
              },
              {
                'faculties.name': {
                  like: searchTextValue ? searchTextValue : undefined,
                },
              },
              {
                japaneseName: {
                  like: searchTextValue ? searchTextValue : undefined,
                },
              },
            ],
          },
          {
            faculties: {
              in:
                searchHospitalFilters[SearchHospitalFilterType.FACULTY_TYPE]?.map(
                  (filter) => filter.id,
                ) || undefined,
            },
          },
        ],
      },
      select: {
        id: true,
        name: true,
        logo: true,
        japaneseName: true,
        address: true,
        bannerImage: true,
      },
    },
  })

  // Handle pull-to-refresh
  const handleRefresh = useCallback(async () => {
    setRefreshing(true)
    try {
      await refetch()
    } finally {
      setRefreshing(false)
    }
  }, [refetch])

  // Flatten all hospitals from all pages
  const allHospitals = useMemo(() => {
    if (!filteredHospitals?.pages) return []
    return filteredHospitals.pages.flatMap((page) => page?.docs || [])
  }, [filteredHospitals])

  const data = useMemo((): ListItem[] => {
    const items: ListItem[] = []

    // Show loading skeletons if initial loading or refreshing
    if (isGetHospitalPartnersLoading || isRefetching || refreshing) {
      items.push({ type: 'loading_skeleton' })
    } else {
      // Add each hospital as a separate item
      allHospitals.forEach((hospital) => {
        items.push({ type: 'hospital', hospital })
      })

      // Loading indicator for pagination
      if (isGetHospitalPartnersFetchingNextPage) {
        items.push({ type: 'loading' })
      }
    }

    return items
  }, [
    allHospitals,
    isGetHospitalPartnersFetchingNextPage,
    isGetHospitalPartnersLoading,
    isRefetching,
    refreshing,
  ])

  const handleLoadMore = useCallback(() => {
    if (hasNextPage && !isGetHospitalPartnersFetchingNextPage) {
      fetchNextPage()
    }
  }, [hasNextPage, isGetHospitalPartnersFetchingNextPage, fetchNextPage])

  const handleHeaderLayout = useCallback((event: any) => {
    const { height } = event.nativeEvent.layout
    setHeaderHeight(height)
  }, [])

  const totalHospitals = useMemo(() => {
    if (isGetHospitalPartnersLoading) {
      return 0
    }
    const total = filteredHospitals?.pages[0]?.totalDocs || 0

    return total > 99 ? '99+' : total
  }, [filteredHospitals, isGetHospitalPartnersLoading])

  const renderItem = useCallback(({ item }: { item: ListItem }) => {
    switch (item.type) {
      case 'hospital':
        return (
          <View className="mb-3 flex w-full">
            <HospitalPartnersItem hospital={item.hospital} />
          </View>
        )

      case 'loading':
        return (
          <View className="items-center py-2">
            <ActivityIndicator size="small" />
          </View>
        )

      case 'loading_skeleton':
        return (
          <View className="flex flex-col gap-y-4">
            {new Array(6).fill(0).map((_, rowIndex) => (
              <View key={rowIndex} className="flex flex-row gap-3">
                <View className="flex-1 flex-col gap-y-3">
                  <Skeleton className="h-48 w-full rounded-lg" />
                  <Skeleton className="h-6 w-[120px] rounded-lg" />
                  <Skeleton className="h-6 w-[240px] rounded-lg" />
                </View>
              </View>
            ))}
          </View>
        )

      default:
        return null
    }
  }, [])

  const renderHeaderComponent = useCallback(() => {
    const appliedFilters = Object.entries(searchHospitalFilters)
      .filter(
        ([key, value]) =>
          key !== SearchHospitalFilterType.SEARCH_TEXT &&
          value &&
          Array.isArray(value) &&
          value.length > 0,
      )
      .flatMap(([_, value]) => value as SearchHospitalFilterItem[])

    return (
      <View onLayout={handleHeaderLayout} className="mb-3 gap-y-3">
        {/* Hospitals header */}
        <View className="flex flex-row items-center justify-between">
          <Text size="body3" className="font-medium">
            {t('MES-71')}{' '}
            {!isGetHospitalPartnersLoading &&
              !isGetHospitalPartnersFetchingNextPage &&
              !isRefetching &&
              `(${totalHospitals})`}
          </Text>
        </View>

        {/* Active Filters - Only show real applied filters */}
        {appliedFilters.length > 0 && (
          <View className="flex flex-col gap-y-2">
            <Text size="body7" variant="subdued">
              {t('MES-706')} ({appliedFilters.length}):
            </Text>
            <View className="flex flex-row flex-wrap gap-2">
              {appliedFilters.map((filter, index) => (
                <BaseSelectedFilterBadge
                  key={`${filter.id}-${index}`}
                  filterItem={filter}
                  onClearFilter={(filterItem) => {
                    toggleSearchHospitalFilter(
                      filterItem.type as SearchHospitalFilterType,
                      filterItem as SearchHospitalFilterItem,
                    )
                  }}
                  labelKey={'label'}
                />
              ))}
              <TouchableOpacity
                onPress={() => clearAllSearchHospitalFilters()}
                className="flex flex-row items-center gap-x-1"
              >
                <Text size="body8" variant="error">
                  {t('MES-709')}
                </Text>
                <View className="-ml-1">
                  <CloseIconDanger width={20} height={20} />
                </View>
              </TouchableOpacity>
            </View>
          </View>
        )}
      </View>
    )
  }, [
    handleHeaderLayout,
    refreshing,
    t,
    totalHospitals,
    isGetHospitalPartnersLoading,
    searchHospitalFilters,
  ])

  const renderEmptyComponent = useCallback(() => {
    if (isGetHospitalPartnersError) {
      return (
        <View className="items-center  py-8">
          <Text size="body6" className="text-red-500">
            {t('MES-197')}
          </Text>
        </View>
      )
    }

    return (
      <View className="flex flex-col items-center justify-center gap-y-2  py-8">
        <EmptyBoxIcon />
        <Text size="body6" variant="default">
          {t('MES-811')}
        </Text>
      </View>
    )
  }, [isGetHospitalPartnersError])

  const keyExtractor = useCallback((item: ListItem, index: number) => {
    if (item.type === 'hospital') {
      return `hospital-${item.hospital.id}`
    }
    return `${item.type}-${index}`
  }, [])

  return (
    <View className="flex-1 px-4">
      <FlatList
        data={data}
        renderItem={renderItem}
        keyExtractor={keyExtractor}
        ListHeaderComponent={renderHeaderComponent}
        onEndReached={handleLoadMore}
        onEndReachedThreshold={0.3}
        ListEmptyComponent={renderEmptyComponent}
        showsVerticalScrollIndicator={false}
        removeClippedSubviews={false}
        maxToRenderPerBatch={10}
        windowSize={10}
        initialNumToRender={5}
        contentContainerStyle={{ paddingBottom: 16 }}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={handleRefresh}
            colors={[colors.primary['500']]}
            tintColor={colors.primary['500']}
            progressBackgroundColor="#FFFFFF"
          />
        }
      />
    </View>
  )
}
