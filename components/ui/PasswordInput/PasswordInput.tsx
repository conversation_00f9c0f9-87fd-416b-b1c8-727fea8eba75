import EyeSlashOpenIcon from '@/assets/icons/eye-slash-open.svg'
import EyeSlashIcon from '@/assets/icons/eye-slash.svg'
import { text } from '@/styles/_colors'
import { cn } from '@/utils/cn'

import React, { useState } from 'react'
import {
  TextInput as RNTextInput,
  TextInputProps as RNTextInputProps,
  TouchableOpacity,
  View,
} from 'react-native'
export interface TextInputProps extends RNTextInputProps {
  isError?: boolean
  wrapperClassName?: string
  isShowPassword?: boolean
}

export const PasswordInput = React.forwardRef<RNTextInput, TextInputProps>(
  (
    { className, wrapperClassName, isError, editable = true, isShowPassword = false, ...props },
    ref,
  ) => {
    const [showPassword, setShowPassword] = useState(isShowPassword)

    return (
      <View
        className={cn(
          ' flex min-h-11 w-full flex-row items-center gap-x-2 rounded-lg border border-custom-neutral-100 bg-white px-3',
          wrapperClassName,
          isError && 'border-custom-danger-600',
        )}
        style={{
          position: 'relative',
        }}
      >
        <RNTextInput
          ref={ref}
          className={cn('flex-1 py-2', className)}
          placeholderTextColor={text.subdued}
          editable={editable}
          secureTextEntry={!showPassword}
          {...props}
        />

        <TouchableOpacity onPress={() => setShowPassword(!showPassword)}>
          {showPassword ? (
            <EyeSlashOpenIcon width={24} height={24} />
          ) : (
            <EyeSlashIcon width={24} height={24} />
          )}
        </TouchableOpacity>
      </View>
    )
  },
)

PasswordInput.displayName = 'PasswordInput'
