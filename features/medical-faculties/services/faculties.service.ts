  import { API_ENDPOINTS } from '@/constants/endpoint.constant'
import { httpService } from '@/services/http/http.service'
import { Faculty } from '@/types/faculty.type'
import { PaginatedDocs } from '@/types/global.type'

import { Params } from '@/types/http.type'
import { AxiosRequestConfig } from 'axios'

// SERVER / CLIENT
class MedicalFacultyService {
  private static instance: MedicalFacultyService

  private constructor() {}

  public static getInstance(): MedicalFacultyService {
    if (!MedicalFacultyService.instance) {
      MedicalFacultyService.instance = new MedicalFacultyService()
    }
    return MedicalFacultyService.instance
  }

  async getMedicalFaculties({
    params = {},
    options = {},
  }: {
    params?: Params
    options?: AxiosRequestConfig
  } = {}): Promise<PaginatedDocs<Faculty>> {
    const data = await httpService.get<PaginatedDocs<Faculty>>(`/${API_ENDPOINTS.faculties_api}`, {
      params,
      ...options,
    })
    return data
  }
}

export const medicalFacultyService = MedicalFacultyService.getInstance()
