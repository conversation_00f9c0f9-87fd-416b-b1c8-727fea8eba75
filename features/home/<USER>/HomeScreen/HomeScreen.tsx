import React from 'react'
import { ScrollView, View } from 'react-native'
import { FeatureList } from '../../components/FeatureList/FeatureList'
import { HomeSearch } from '../../components/HomeSearch/HomeSearch'

import { useCurvedTabBarSafeSpacing } from '@/components/AppTabs/CurvedTabBar'
import { HomeFaculties } from '../../components/HomeFaculties/HomeFaculties'
import { HomeKeywordSearchHistory } from '../../components/HomeKeywordSearchHistory/HomeKeywordSearchHistory'
import { HomePosts } from '../../components/HomePosts/HomePosts'
import { HomeProducts } from '../../components/HomeProducts/HomeProducts'
import { TranslatorContact } from '../../components/TranslatorContact/TranslatorContact'

export default function HomeScreen() {
  const safeSpacing = useCurvedTabBarSafeSpacing()
  return (
    <ScrollView
      showsVerticalScrollIndicator={false}
      className="flex-1 bg-white"
      contentContainerStyle={{ paddingBottom: safeSpacing }}
    >
      <View className="flex-1 flex-col gap-5 pb-6">
        <HomeSearch />
        {/* <PartnerHospitalsSection /> */}
        <HomeKeywordSearchHistory />
        <TranslatorContact />
        {/* <DailyVocabulary /> */}
        <FeatureList />
        {/* <DailyFact /> */}
        <HomePosts />
        <HomeFaculties />
        <HomeProducts />

        {/* <HomeEmailSubscription /> */}
      </View>
    </ScrollView>
  )
}
