import { colors, getTextColor } from '@/styles/_colors'
import { typo } from '@/styles/_typography'
import { spacing } from '@/styles/_variables'
import React from 'react'
import { useWindowDimensions, View } from 'react-native'
import RenderHTML, { CustomBlockRenderer } from 'react-native-render-html'

// Mobile-optimized Tailwind prose-like styles using design tokens
export const TAGS_STYLES = {
  // Headings with mobile-friendly hierarchy and spacing
  h1: {
    ...typo.heading7, // 20px, semiBold - reduced from 36px
    color: getTextColor('default'),
    marginTop: spacing.lg, // 24px - reduced from 32px
    marginBottom: spacing.md, // 16px - reduced from 24px
    lineHeight: 28, // reduced from 44
  },
  h2: {
    ...typo.body2, // 18px, medium - reduced from 28px
    fontWeight: typo.heading8.fontWeight, // semiBold
    color: getTextColor('default'),
    marginTop: spacing.lg, // 24px - reduced from 32px
    marginBottom: spacing.sm, // 8px - reduced from 16px
    lineHeight: 26, // reduced from 36
  },
  h3: {
    ...typo.body4, // 16px, regular
    fontWeight: typo.body3.fontWeight, // semiBold
    color: getTextColor('default'),
    marginTop: spacing.md, // 16px - reduced from 24px
    marginBottom: spacing.sm, // 8px - reduced from 16px
    lineHeight: 24, // reduced from 28
  },
  h4: {
    ...typo.body6, // 14px, medium
    fontWeight: typo.body3.fontWeight, // semiBold
    color: getTextColor('default'),
    marginTop: spacing.md, // 16px - reduced from 24px
    marginBottom: spacing.xs, // 4px - reduced from 8px
    lineHeight: 20, // reduced from 24
  },
  h5: {
    ...typo.body7, // 14px, regular
    fontWeight: typo.body6.fontWeight, // medium
    color: getTextColor('default'),
    marginTop: spacing.sm, // 8px - reduced from 16px
    marginBottom: spacing.xs, // 4px - reduced from 8px
    lineHeight: 20, // reduced from 24
  },
  h6: {
    ...typo.body9, // 12px, regular
    fontWeight: typo.body6.fontWeight, // medium
    color: getTextColor('subdued'),
    marginTop: spacing.sm, // 8px - reduced from 16px
    marginBottom: spacing.xs, // 4px - reduced from 8px
    lineHeight: 16, // reduced from 20
    textTransform: 'uppercase',
    letterSpacing: 0.5,
  },

  // Body text optimized for mobile reading
  p: {
    ...typo.body7, // 14px, regular - reduced from 16px
    color: getTextColor('default'),
    marginBottom: spacing.sm, // 8px - reduced from 16px
    lineHeight: 16, // reduced from 28 for better mobile density
  },

  // Text formatting
  strong: {
    fontWeight: typo.body3.fontWeight, // semiBold
    color: getTextColor('default'),
  },
  b: {
    fontWeight: typo.body3.fontWeight, // semiBold
    color: getTextColor('default'),
  },
  em: {
    fontStyle: 'italic',
    color: getTextColor('default'),
  },
  i: {
    fontStyle: 'italic',
    color: getTextColor('default'),
  },
  small: {
    ...typo.body9, // 12px, regular
    color: getTextColor('subdued'),
    lineHeight: 18,
  },
  mark: {
    backgroundColor: colors.warning[200],
    color: getTextColor('default'),
    paddingHorizontal: spacing.xs / 2, // 2px
    borderRadius: 3,
  },

  // Links with hover-like styling
  a: {
    color: colors.primary[600],
    textDecorationLine: 'underline',
    fontWeight: typo.body5.fontWeight, // medium
  },

  // Lists with mobile-optimized spacing and indentation
  ul: {
    paddingLeft: spacing.md, // 16px - reduced from 24px
    marginBottom: spacing.sm, // 8px - reduced from 16px
    marginTop: spacing.xs, // 4px - reduced from 8px
  },
  ol: {
    paddingLeft: spacing.md, // 16px - reduced from 24px
    marginBottom: spacing.sm, // 8px - reduced from 16px
    marginTop: spacing.xs, // 4px - reduced from 8px
  },
  li: {
    ...typo.body7, // 14px, regular - reduced from 16px
    color: getTextColor('default'),
    marginBottom: spacing.xs, // 4px - reduced from 8px
    lineHeight: 22, // increased for better readability
    paddingLeft: spacing.xs, // 4px
  },

  // Blockquotes with mobile-friendly styling
  blockquote: {
    fontStyle: 'italic',
    fontSize: typo.body4.fontSize, // 16px - reduced from 18px
    lineHeight: 24, // reduced from 32
    color: getTextColor('subdued'),
    borderLeftWidth: 3, // reduced from 4
    borderLeftColor: colors.primary[200],
    paddingLeft: spacing.md, // 16px - reduced from 24px
    paddingVertical: spacing.sm, // 8px - reduced from 16px
    marginVertical: spacing.md, // 16px - reduced from 24px
    backgroundColor: colors.background.form,
    borderRadius: 6, // reduced from 8
    marginHorizontal: spacing.xs, // 4px - reduced from 8px
  },

  // Code elements
  code: {
    fontFamily: 'Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace',
    fontSize: typo.body7.fontSize, // 14px
    backgroundColor: colors.neutral[100],
    color: colors.danger[600],
    paddingHorizontal: spacing.xs, // 4px
    paddingVertical: spacing.xs / 2, // 2px
    borderRadius: 4,
    fontWeight: typo.body6.fontWeight, // medium
  },
  pre: {
    fontFamily: 'Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace',
    fontSize: typo.body7.fontSize, // 14px
    backgroundColor: colors.neutral[100],
    color: getTextColor('default'),
    padding: spacing.md, // 16px
    borderRadius: 8,
    marginVertical: spacing.md, // 16px
    overflow: 'scroll',
    lineHeight: 20,
  },

  // Tables
  table: {
    width: '100%',
    marginVertical: spacing.md, // 16px
    fontSize: typo.body7.fontSize, // 14px
    borderWidth: 1,
    borderColor: colors.divider.border,

    borderRightWidth: 0,

    borderBottomWidth: 0,
    overflow: 'hidden',
  },
  tbody: {},
  tr: {
    borderBottomWidth: 1,
    borderBottomColor: colors.divider.default,
  },
  th: {
    fontWeight: typo.body6.fontWeight, // medium
    textAlign: 'left',
    padding: spacing.sm, // 8px
    borderBottomWidth: 1,
    borderBottomColor: colors.divider.default,
    color: getTextColor('default'),
    borderRightWidth: 1,
    borderRightColor: colors.divider.default,
  },
  td: {
    padding: spacing.sm, // 8px
    borderRightWidth: 1,
    borderRightColor: colors.divider.default,
    color: getTextColor('default'),
    lineHeight: 20,
    // verticalAlign: 'top',
  },

  // Horizontal rule
  hr: {
    borderTopWidth: 1,
    borderTopColor: colors.divider.default,
    marginVertical: spacing.xl, // 32px
    borderBottomWidth: 0,
    borderLeftWidth: 0,
    borderRightWidth: 0,
  },

  // Images
  img: {
    borderRadius: 8,
    marginVertical: spacing.md, // 16px
  },

  // Figures and captions
  figure: {
    marginVertical: spacing.lg, // 24px
    textAlign: 'center',
  },
  figcaption: {
    ...typo.body9, // 12px, regular
    color: getTextColor('subdued'),
    textAlign: 'center',
    marginTop: spacing.sm, // 8px
    fontStyle: 'italic',
  },

  // Definition lists
  dl: {
    marginBottom: spacing.md, // 16px
  },
  dt: {
    fontWeight: typo.body3.fontWeight, // semiBold
    color: getTextColor('default'),
    marginBottom: spacing.xs, // 4px
    marginTop: spacing.sm, // 8px
  },
  dd: {
    marginLeft: spacing.lg, // 24px
    marginBottom: spacing.sm, // 8px
    color: getTextColor('default'),
  },
}

// Custom CSS classes styles for lexical editor tables and nested lists
export const CLASSES_STYLES = {
  'lexical-table-container': {
    marginVertical: spacing.md,
  },
  'lexical-table': {
    width: '100%',
  },
  'lexical-table-row': {
    flexDirection: 'row',
    borderBottomWidth: 1,
    borderBottomColor: colors.divider.default,
  },
  'lexical-table-cell': {
    flex: 1,
    padding: spacing.sm,
    borderRightWidth: 1,
    borderRightColor: colors.divider.default,
    justifyContent: 'center',
  },
  'lexical-table-cell-header-0': {
    fontWeight: typo.body6.fontWeight,
  },
  // Handle nested list items - remove bullets and adjust spacing
  nestedListItem: {
    marginLeft: -spacing.md, // Remove the default ul padding
    paddingLeft: spacing.md, // Add back some indentation
  },
}

// Custom renderer for list items to handle nested lists properly
const customListItemRenderer: CustomBlockRenderer = ({ TDefaultRenderer, ...props }) => {
  const { tnode } = props
  const isNestedListItem = tnode.classes?.includes('nestedListItem')

  if (isNestedListItem) {
    // For nested list items, render without bullet points
    return (
      <View style={{ marginLeft: spacing.md, marginBottom: spacing.xs }}>
        <TDefaultRenderer {...props} />
      </View>
    )
  }

  // For regular list items, use default rendering
  return <TDefaultRenderer {...props} />
}

interface CustomRenderHTMLProps {
  htmlContent: string
  baseStyle?: Record<string, any>
  customTagsStyles?: Record<string, any>
  className?: string
}

export const CustomRenderHTML = ({
  htmlContent,
  baseStyle = {},
  customTagsStyles = {},
}: CustomRenderHTMLProps) => {
  const { width } = useWindowDimensions()

  const mergedTagsStyles = {
    ...TAGS_STYLES,
    ...customTagsStyles,
  }

  const defaultBaseStyle = {
    color: getTextColor('default'),
    fontSize: typo.body7.fontSize, // 14px - reduced from 16px for mobile
    lineHeight: 22, // reduced from 28 for better mobile density
    fontFamily: 'Inter', // Using primary font family
  }

  return (
    <RenderHTML
      source={{ html: String(htmlContent) }}
      contentWidth={width}
      tagsStyles={mergedTagsStyles as unknown as Record<string, any>}
      classesStyles={CLASSES_STYLES as unknown as Record<string, any>}
      baseStyle={{
        ...defaultBaseStyle,
        ...baseStyle,
      }}
      systemFonts={['Inter']}
      defaultTextProps={{
        selectable: true,
      }}
      renderersProps={{
        img: {
          enableExperimentalPercentWidth: true,
        },
      }}
      renderers={{
        li: customListItemRenderer,
      }}
    />
  )
}
