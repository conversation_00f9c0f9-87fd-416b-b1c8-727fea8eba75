import { Text } from '@/components/ui/Text/Text'
import { APP_ROUTES, AppRoutesEnum } from '@/routes/appRoutes'
import { Faculty } from '@/types/faculty.type'
import * as Haptics from 'expo-haptics'
import { Link } from 'expo-router'
import { TouchableOpacity, View } from 'react-native'
import Svg, { Path } from 'react-native-svg'
import { ExaminationForm, Hospital } from '../../types'

interface HospitalExaminationFormItemProps {
  examinationForm: ExaminationForm
}

export const HospitalExaminationFormItem = ({
  examinationForm,
}: HospitalExaminationFormItemProps) => {
  const { id, name, faculty, hospital } = examinationForm || {}
  const facultyName = (faculty as Faculty)?.name
  const facultyId = (faculty as Faculty)?.id
  const hospitalId = (hospital as Hospital)?.id
  const handlePress = () => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light)
  }

  return (
    <Link
      href={{
        pathname:
          APP_ROUTES.HOSPITAL_PARTNERS.children?.[AppRoutesEnum.CREATE_HOSPITAL_EXAMINATION_FORM]
            ?.path || '',
        params: {
          examinationFormId: id,
          examinationFormName: name,
          facultyName: facultyName,
          facultyId: facultyId,
          hospitalId: hospitalId,
        },
      }}
      asChild
    >
      <TouchableOpacity
        className="flex min-h-[56px] flex-row items-center gap-x-3 rounded-lg bg-custom-background-hover p-4"
        onPress={handlePress}
      >
        {/* Icon */}
        <View className="flex-shrink-0">
          <Svg width={20} height={20} viewBox="0 0 20 20" fill="none">
            <Path
              d="M13.1658 1.8417C12.8241 1.50003 12.2324 1.73337 12.2324 2.20837V5.1167C12.2324 6.33337 13.2658 7.3417 14.5241 7.3417C15.3158 7.35003 16.4158 7.35003 17.3574 7.35003C17.8324 7.35003 18.0824 6.7917 17.7491 6.45837C16.5491 5.25003 14.3991 3.07503 13.1658 1.8417Z"
              fill="#1157C8"
            />
            <Path
              d="M17.084 8.49163H14.6757C12.7007 8.49163 11.0923 6.88329 11.0923 4.90829V2.49996C11.0923 2.04163 10.7173 1.66663 10.259 1.66663H6.72565C4.15898 1.66663 2.08398 3.33329 2.08398 6.30829V13.6916C2.08398 16.6666 4.15898 18.3333 6.72565 18.3333H13.2757C15.8423 18.3333 17.9173 16.6666 17.9173 13.6916V9.32496C17.9173 8.86663 17.5423 8.49163 17.084 8.49163ZM9.58398 14.7916H6.25065C5.90898 14.7916 5.62565 14.5083 5.62565 14.1666C5.62565 13.825 5.90898 13.5416 6.25065 13.5416H9.58398C9.92565 13.5416 10.209 13.825 10.209 14.1666C10.209 14.5083 9.92565 14.7916 9.58398 14.7916ZM11.2507 11.4583H6.25065C5.90898 11.4583 5.62565 11.175 5.62565 10.8333C5.62565 10.4916 5.90898 10.2083 6.25065 10.2083H11.2507C11.5923 10.2083 11.8757 10.4916 11.8757 10.8333C11.8757 11.175 11.5923 11.4583 11.2507 11.4583Z"
              fill="#1157C8"
            />
          </Svg>
        </View>

        {/* Content */}
        <View className="flex-1">
          <Text size="body6" variant="default" numberOfLines={2}>
            {name}
          </Text>
        </View>
      </TouchableOpacity>
    </Link>
  )
}
