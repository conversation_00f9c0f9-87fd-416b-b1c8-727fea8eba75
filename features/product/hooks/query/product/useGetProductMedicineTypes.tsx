import { productService } from '@/features/product/services/product.service'
import { ProductMedicineType } from '@/features/product/types'
import { PaginatedDocs } from '@/types/global.type'
import { Params } from '@/types/http.type'
import { UseQueryOptions, useQuery } from '@tanstack/react-query'
import { AxiosRequestConfig } from 'axios'
import { productQueryKeys } from './queryKeys'

export const useGetProductMedicineTypes = ({
  params = {},
  options = {},
  useQueryOptions,
}: {
  params?: Params
  options?: AxiosRequestConfig
  useQueryOptions?: Omit<
    UseQueryOptions<PaginatedDocs<ProductMedicineType> | null>,
    'queryKey' | 'queryFn'
  >
} = {}) => {
  const {
    isError: isGetProductMedicineTypesError,
    isPending: isGetProductMedicineTypesLoading,
    data: productMedicineTypes,
    ...rest
  } = useQuery({
    queryKey: [productQueryKeys['productMedicineTypes'].base(), params],
    queryFn: async () =>
      productService.getProductMedicineTypes({
        params: params,
        options: options,
      }),
    ...useQueryOptions,
  })
  return {
    isGetProductMedicineTypesError,
    isGetProductMedicineTypesLoading,
    productMedicineTypes,
    ...rest,
  }
}
