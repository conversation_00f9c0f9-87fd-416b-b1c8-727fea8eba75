import { StackScreenBase } from '@/components/StackBase/StackScreenBase'
import { CommentsScreen } from '@/features/comments/screens/CommentsScreen/CommentsScreen'
import { Comment } from '@/features/comments/types'
import { useLocalSearchParams } from 'expo-router'
import { useTranslation } from 'react-i18next'

export default function CommentsAppScreen() {
  const { id, relationTo, queryKeys, staleTime, gcTime, customTitle, totalCommentsQueryKeys } =
    useLocalSearchParams()
  const { t } = useTranslation()
  return (
    <>
      <StackScreenBase
        options={{
          headerTitle: (customTitle as string) || t('MES-886'),
          headerShown: true,
        }}
      />
      <CommentsScreen
        id={id as string}
        relationTo={relationTo as Comment['commentedOn']['relationTo']}
        queryKeys={queryKeys as string}
        staleTime={Boolean(staleTime) ? Number(staleTime) : undefined}
        gcTime={Boolean(gcTime) ? Number(gcTime) : undefined}
        totalCommentsQueryKeys={totalCommentsQueryKeys as string}
      />
    </>
  )
}
