export interface Params {
  [key: string]: any
}

export interface GenericOptions {
  url: string
  params?: Params
}

export interface ErrorResponse {
  status: string
  message: string
  messages?: string[]
}

// Comprehensive normalized error structure from HTTP service
export interface NormalizedError {
  message: string
  status: number
  rawError: any
  apiError: any
  messages: string[]
  code?: string | null
  data?: any
}
