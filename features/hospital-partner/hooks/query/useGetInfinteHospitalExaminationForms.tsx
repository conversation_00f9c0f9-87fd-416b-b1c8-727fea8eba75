import { Params } from '@/types/http.type'
import { InfiniteData, UseInfiniteQueryOptions, useInfiniteQuery } from '@tanstack/react-query'

import { PaginatedDocs } from '@/types/global.type'
import { AxiosRequestConfig } from 'axios'
import { hospitalPartnerService } from '../../services/hospital-partner.service'
import { ExaminationForm } from '../../types'
import { hospitalPartnersQueryKeys } from './queryKeys'

export type HospitalPartnersQueryConfig = Omit<
  UseInfiniteQueryOptions<
    PaginatedDocs<ExaminationForm>,
    Error,
    InfiniteData<PaginatedDocs<ExaminationForm>>,
    (string | Params)[],
    number
  >,
  'queryFn' | 'queryKey' | 'getNextPageParam' | 'initialPageParam'
>

interface UseGetInfiniteHospitalExaminationFormsProps {
  params?: Params
  options?: AxiosRequestConfig
  key?: string | number
  config?: HospitalPartnersQueryConfig
  overrideKey?: (string | Params)[]
}

export const useGetInfinteHospitalExaminationForms = ({
  params = {},
  options = {},
  config = {},
  overrideKey,
}: UseGetInfiniteHospitalExaminationFormsProps = {}) => {
  const {
    isError: isGetHospitalExaminationFormsError,
    isFetching: isGetHospitalExaminationFormsLoading,
    data: hospitalExaminationForms,
    fetchNextPage,
    hasNextPage,
    ...rest
  } = useInfiniteQuery({
    queryKey: overrideKey
      ? overrideKey
      : [hospitalPartnersQueryKeys['hospital-examination-forms'].base(), params],
    queryFn: async ({ pageParam = 1 }) => {
      return hospitalPartnerService.getHospitalExaminationForms({
        params: {
          ...params,
          page: pageParam,
        },
        options,
      })
    },
    getNextPageParam: (lastPage) => lastPage?.nextPage,
    getPreviousPageParam: (lastPage) => lastPage?.prevPage,
    initialPageParam: 1,
    ...config,
  })

  return {
    isGetHospitalExaminationFormsError,
    isGetHospitalExaminationFormsLoading,
    hospitalExaminationForms,
    fetchNextPage,
    hasNextPage,
    ...rest,
  }
}
