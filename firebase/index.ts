// Conditional Firebase imports for Expo Go compatibility
let firebaseAnalytics: any = null
let firebaseApp: any = null

try {
  if (process.env.EXPO_PUBLIC_APP_ENVIROMENT !== 'expo-go') {
    const { getAnalytics } = require('@react-native-firebase/analytics')
    const { getApp } = require('@react-native-firebase/app')
    firebaseApp = getApp()
    firebaseAnalytics = getAnalytics(firebaseApp)
  }
} catch (error) {
  console.log('Firebase Analytics not available in Expo Go')
}

export { firebaseAnalytics, firebaseApp }
