import { useAuthentication } from '@/hooks/auth/useAuthentication'
import { PaginatedDocs } from '@/types/global.type'
import { InfiniteData, MutationOptions, useMutation, useQueryClient } from '@tanstack/react-query'
import { PushNotificationMessagesTabEnum } from '../../enums/push-notification.enum'
import { unifiedNotificationService } from '../../services/unified-notification.service'
import { UserPushNotification } from '../../types'
import { useAddNotificationToCache } from '../common/useAddNotificationToCache'
import { unifiedNotificationMutationKeys, unifiedNotificationQueryKeys } from './queryKeys'

export const useMarkAllNotificationsAsViewed = ({
  options,
}: {
  options?: Omit<MutationOptions | undefined, 'mutationFn' | 'mutationKey'>
} = {}) => {
  const queryClient = useQueryClient()
  const { handleResetUnviewedCount } = useAddNotificationToCache()
  const { user } = useAuthentication()
  const {
    isError: isMarkAllNotificationsAsViewedError,
    isPending: isMarkAllNotificationsAsViewedPending,
    mutate: markAllNotificationsAsViewedMutation,

    ...rest
  } = useMutation({
    mutationKey: unifiedNotificationMutationKeys['mark-all-notifications-as-viewed'].base(),
    mutationFn: async () => {
      // Don't mark all as viewed if no user is logged in
      if (!user) {
        console.log(
          '🔥 useMarkAllNotificationsAsViewed: No user logged in, skipping mark all as viewed',
        )
        return Promise.resolve()
      }

      return unifiedNotificationService.markAllNotificationAsViewed()
    },
    onSuccess: () => {
      const now = new Date().toISOString()

      // Get all notifications from 'all' tab to move to 'viewed' tab
      const allTabQueryKey = ['user-push-notifications', PushNotificationMessagesTabEnum.ALL]
      const allTabData =
        queryClient.getQueryData<InfiniteData<PaginatedDocs<UserPushNotification>>>(allTabQueryKey)

      // Collect all unviewed notifications to add to the viewed tab
      const unviewedNotifications: UserPushNotification[] = []
      if (allTabData) {
        allTabData.pages.forEach((page) => {
          page.docs.forEach((noti) => {
            if (!noti.viewed) {
              unviewedNotifications.push({
                ...noti,
                viewed: true,
                viewedAt: now,
              })
            }
          })
        })
      }

      // Update all three tabs: 'all', 'viewed', 'not-viewed'
      const tabTypes = [
        PushNotificationMessagesTabEnum.ALL,
        PushNotificationMessagesTabEnum.VIEWED,
        PushNotificationMessagesTabEnum.NOT_VIEWED,
      ] as const

      tabTypes.forEach((tabType) => {
        const queryKey = [
          ...unifiedNotificationQueryKeys['user-push-notifications'].base(),
          tabType,
        ]

        queryClient.setQueryData<InfiniteData<PaginatedDocs<UserPushNotification>>>(
          queryKey,
          (oldData) => {
            if (!oldData) return oldData

            if (tabType === PushNotificationMessagesTabEnum.NOT_VIEWED) {
              // For 'not-viewed' tab: Remove all notifications
              return {
                ...oldData,
                pages: oldData.pages.map((page) => ({
                  ...page,
                  docs: [],
                  totalDocs: 0,
                })),
              }
            } else if (tabType === PushNotificationMessagesTabEnum.VIEWED) {
              // For 'viewed' tab: Remove existing duplicates, then add all unviewed notifications to the top
              const updatedPages = oldData.pages.map((page) => ({
                ...page,
                docs: page.docs.filter(
                  (noti) => !unviewedNotifications.some((un) => un.id === noti.id),
                ),
              }))

              // Add all newly viewed notifications to the first page
              if (unviewedNotifications.length > 0 && updatedPages.length > 0) {
                // Sort by viewedAt (most recent first)
                const sortedNewNotifications = [...unviewedNotifications].sort((a, b) => {
                  return new Date(b.viewedAt!).getTime() - new Date(a.viewedAt!).getTime()
                })

                updatedPages[0] = {
                  ...updatedPages[0],
                  docs: [...sortedNewNotifications, ...updatedPages[0].docs],
                  totalDocs: (updatedPages[0].totalDocs || 0) + unviewedNotifications.length,
                }
              }

              return {
                ...oldData,
                pages: updatedPages,
              }
            } else {
              // For 'all' tab: Update all notifications to viewed
              return {
                ...oldData,
                pages: oldData.pages.map((page) => ({
                  ...page,
                  docs: page.docs.map((noti) =>
                    noti.viewed ? noti : { ...noti, viewed: true, viewedAt: now },
                  ),
                })),
              }
            }
          },
        )
      })

      // Reset the unviewed notification count to 0
      handleResetUnviewedCount()
    },

    ...options,
  })

  return {
    isMarkAllNotificationsAsViewedError,
    isMarkAllNotificationsAsViewedPending,
    markAllNotificationsAsViewedMutation,
    ...rest,
  }
}
