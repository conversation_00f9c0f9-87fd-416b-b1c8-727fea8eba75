import { StackScreenBase } from '@/components/StackBase/StackScreenBase'
import { CommentDetailsScreen } from '@/features/comments/screens/CommentDetailsScreen/CommentDetailsScreen'
import { Comment } from '@/features/comments/types'
import { useLocalSearchParams } from 'expo-router'
import { useTranslation } from 'react-i18next'

export default function CommentsAppScreen() {
  const params = useLocalSearchParams()

  const {
    id,
    relationTo,
    queryKeys,
    staleTime,
    gcTime,
    customTitle,
    listLimit,
    nestedLimit,
    commentId,
    nestedCommentId,
    notiTitle,
    docSlug,
    documentId,
  } = params || {}

  const { t } = useTranslation()
  return (
    <>
      <StackScreenBase
        options={{
          headerTitle: (customTitle as string) || t('MES-845'),
          headerShown: true,
        }}
      />

      <CommentDetailsScreen
        id={id as string}
        relationTo={relationTo as Comment['commentedOn']['relationTo']}
        queryKeys={queryKeys as string}
        staleTime={Boolean(staleTime) ? Number(staleTime) : undefined}
        gcTime={Boolean(gcTime) ? Number(gcTime) : undefined}
        listLimit={Boolean(listLimit) ? Number(listLimit) : undefined}
        nestedLimit={Boolean(nestedLimit) ? Number(nestedLimit) : undefined}
        commentId={commentId as string}
        nestedCommentId={nestedCommentId as string}
        notiTitle={notiTitle as string}
        docSlug={docSlug as string}
        documentId={documentId as string}
      />
    </>
  )
}
