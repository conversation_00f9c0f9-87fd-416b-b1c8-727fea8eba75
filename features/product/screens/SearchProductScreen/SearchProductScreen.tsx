import { useEffect, useMemo } from 'react'

import {
  Keyboard,
  KeyboardAvoidingView,
  Platform,
  TouchableWithoutFeedback,
  View,
} from 'react-native'
import { useShallow } from 'zustand/react/shallow'
import { SearchProduct } from '../../components/SearchProduct/SearchProduct'
import { useProductStore } from '../../stores/ProductStore'
export const SearchProductScreen = () => {
  const { searchTextValue, hasActiveFilters, clearAllSearchProductFiltersAndSearchText } =
    useProductStore(
      useShallow((state) => ({
        searchTextValue: state.searchProductFilters['search_text'],
        hasActiveFilters: state.hasActiveFilters,
        clearAllSearchProductFiltersAndSearchText: state.clearAllSearchProductFiltersAndSearchText,
      })),
    )

  const shouldShowProductList = useMemo(() => {
    return searchTextValue || hasActiveFilters
  }, [searchTextValue, hasActiveFilters])

  const dismissKeyboard = () => {
    Keyboard.dismiss()
  }

  useEffect(() => {
    return () => {
      clearAllSearchProductFiltersAndSearchText()
    }
  }, [])

  return (
    <KeyboardAvoidingView
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      className="flex-1"
      enabled={true}
    >
      <TouchableWithoutFeedback
        onPress={dismissKeyboard}
        accessible={false}
        disabled={Boolean(shouldShowProductList)}
      >
        <View className="flex flex-1 bg-white px-4 py-3">
          <SearchProduct></SearchProduct>
        </View>
      </TouchableWithoutFeedback>
    </KeyboardAvoidingView>
  )
}
