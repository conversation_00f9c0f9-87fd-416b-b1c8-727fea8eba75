export const keywordQuery<PERSON>eys = {
  main: ['keywords'],
  keywords: {
    base: () => ['keywords'],
  },
  searchListKeywords: {
    base: () => ['searchListKeywords'],
  },
  randomKeywords: {
    base: () => ['random-keywords'],
  },
  'dictionary-keywords': {
    base: () => ['dictionary-keywords'],
  },
  'dictionary-keywords-by-category': {
    base: () => ['dictionary-keywords-by-category'],
  },
  'keyword-details': {
    base: () => ['keyword-details'],
  },
  'get-keyword-audio': {
    base: () => ['get-keyword-audio'],
  },
  'favorite-keywords-by-user': {
    base: () => ['favorite-keywords-by-user'],
  },
}
export const keywordMutationKeys = {
  'create-multiple-keywords': {
    base: () => ['create-multiple-keywords'],
  },

  'update-favorite-keyword': {
    base: () => ['update-favorite-keyword'],
  },
  'keyword-text-to-speech': {
    base: () => ['keyword-text-to-speech'],
  },
}
