import { Text } from '@/components/ui/Text/Text'
import colors from '@/styles/_colors'
import { cn } from '@/utils/cn'
import { useTranslation } from 'react-i18next'
import { TouchableOpacity, View } from 'react-native'
import Svg, { Path } from 'react-native-svg'
import {
  BASE_SORT_BY,
  FAVORITE_KEYWORDS_SORT_BY,
  KEYWORD_SEARCH_HISTORY_SORT_BY,
} from '../../enums'

interface SortButtonProps {
  sortBy: BASE_SORT_BY | FAVORITE_KEYWORDS_SORT_BY | KEYWORD_SEARCH_HISTORY_SORT_BY
  onPress: () => void
  className?: string
  showIcon?: boolean
}

export const SortButton = ({ sortBy, onPress, className, showIcon = false }: SortButtonProps) => {
  const { t } = useTranslation()

  return (
    <TouchableOpacity
      className={cn('flex flex-row items-center gap-x-2', className)}
      onPress={onPress}
    >
      {showIcon && (
        <Svg width={16} height={16} viewBox="0 0 16 16" fill="none">
          <Path
            d="M6.96586 4.47998L4.48584 2L2.00586 4.47998"
            stroke={sortBy === 'oldest' ? colors.primary['500'] : '#8B8C99'}
            strokeWidth={1.5}
            strokeLinecap="round"
            strokeLinejoin="round"
          />
          <Path
            d="M4.48633 14V2"
            stroke={sortBy === 'oldest' ? colors.primary['500'] : '#8B8C99'}
            strokeWidth={1.5}
            strokeLinecap="round"
            strokeLinejoin="round"
          />
          <Path
            d="M9.0332 11.5195L11.5132 13.9995L13.9932 11.5195"
            stroke={sortBy === 'newest' ? colors.primary['500'] : '#8B8C99'}
            strokeWidth={1.5}
            strokeLinecap="round"
            strokeLinejoin="round"
          />
          <Path
            d="M11.5117 2V14"
            stroke={sortBy === 'newest' ? colors.primary['500'] : '#8B8C99'}
            strokeWidth={1.5}
            strokeLinecap="round"
            strokeLinejoin="round"
          />
        </Svg>
      )}
      <View className={`flex flex-row items-center gap-x-2 ${!showIcon ? '' : 'ml-auto'}`}>
        {sortBy === 'newest' ? (
          <Text size="body6" variant="subdued">
            {t('MES-782')}
          </Text>
        ) : (
          <Text size="body6" variant="subdued">
            {t('MES-783')}
          </Text>
        )}
      </View>
    </TouchableOpacity>
  )
}
