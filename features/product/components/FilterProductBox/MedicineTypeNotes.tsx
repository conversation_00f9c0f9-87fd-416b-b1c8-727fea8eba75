import ArrowDownIcon from '@/assets/icons/arrow-down-icon.svg'
import InfoIcon from '@/assets/icons/info-circle-icon.svg'
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from '@/components/ui/Accordion/Accordion'
import { Text } from '@/components/ui/Text/Text'
import { StyledExpoImage } from '@/libs/styled'
import { Media } from '@/types/media.type'
import { cn } from '@/utils/cn'
import { useState } from 'react'
import { useTranslation } from 'react-i18next'
import { View } from 'react-native'
import { ProductMedicineType } from '../../types'
interface MedicineTypeNotesProps {
  medicineType: ProductMedicineType[]
  isLoading?: boolean
}
export const MedicineTypeNotes = ({ medicineType, isLoading = false }: MedicineTypeNotesProps) => {
  const { t } = useTranslation()
  const [activeAccordion, setActiveAccordion] = useState('1')
  return (
    <View className="mt-3">
      <Accordion
        variant="unfilled"
        defaultValue={['1'] as string[]}
        className="w-full rounded-lg bg-custom-informative-100"
        type="single"
        onValueChange={(value) => {
          setActiveAccordion(value[0])
        }}
      >
        <AccordionItem value="1" className={cn('w-full rounded-b-lg ')}>
          <AccordionTrigger className="w-full">
            <View className="w-full flex-row items-center justify-between">
              <View className="flex-row items-center gap-x-2">
                <InfoIcon width={18} height={18} />
                <Text size="body6" variant="primary">
                  {t('MES-719')}
                </Text>
              </View>
              <View className={cn('transition-all', activeAccordion === '1' ? 'rotate-180' : '')}>
                <ArrowDownIcon width={18} height={18} />
              </View>
            </View>
          </AccordionTrigger>
          <AccordionContent className="rounded-b-lg ">
            {isLoading ? (
              <></>
            ) : (
              <View className="flex flex-col gap-y-2">
                {medicineType.map((item) => {
                  const { id, title, icon, note } = item
                  const { url, thumbnailURL } = (icon as Media) || {}
                  const iconImage = url || thumbnailURL
                  return (
                    <View key={id} className="flex flex-col gap-[6px]">
                      <View className="flex-row items-center gap-x-1">
                        <View className="aspect-square h-[20px] w-[20px] overflow-hidden">
                          {iconImage && (
                            <StyledExpoImage
                              source={{ uri: iconImage }}
                              className="h-full w-full"
                              contentFit="cover"
                            />
                          )}
                        </View>
                        <View className="flex-1 flex-row items-center gap-x-2">
                          <Text size="body6">{title}</Text>
                        </View>
                      </View>
                      {note && <Text size="body7">{note}</Text>}
                    </View>
                  )
                })}
              </View>
            )}
          </AccordionContent>
        </AccordionItem>
      </Accordion>
    </View>
  )
}
