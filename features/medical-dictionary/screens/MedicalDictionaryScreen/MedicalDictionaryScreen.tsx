import { Text } from '@/components/ui/Text/Text'
import { useAuthentication } from '@/hooks/auth/useAuthentication'
import { keywordQueryKeys } from '@/hooks/query/keyword/queryKeys'
import { useQueryClient } from '@tanstack/react-query'
import React, { useCallback } from 'react'
import { useTranslation } from 'react-i18next'
import { View } from 'react-native'
import { DailyKeyword } from '../../components/DailyKeyword/DailyKeyword'
import { KeywordCategoriesSection } from '../../components/KeywordCategoriesSection/KeywordCategoriesSection'
import { MedicalDictionaryHeader } from '../../components/MedicalDictionaryHeader/MedicalDictionaryHeader'
import { MedicalDictionaryList } from '../../components/MedicalDictionaryList/MedicalDictionaryList'

import { MedicalDictionarySearchHistorySection } from '../../components/MedicalDictionarySearchHistory/MedicalDictionarySearchHistorySection'
import { useMedicalDictionaryList } from '../../hooks/common/useMedicalDictionaryList'

export default function MedicalDictionaryScreen() {
  const { t } = useTranslation()
  const { user } = useAuthentication()
  const { refreshing, data, isGetKeywordsError, handleRefresh, handleLoadMore } =
    useMedicalDictionaryList({
      extendedKeys: keywordQueryKeys['dictionary-keywords'].base(),
    })
  const queryClient = useQueryClient()

  const renderHeaderComponent = useCallback(() => {
    return (
      <View className="gap-y-4">
        <MedicalDictionaryHeader />
        <DailyKeyword isRefreshing={refreshing} />
        <MedicalDictionarySearchHistorySection isRefreshing={refreshing} />
        <KeywordCategoriesSection />

        {/* List Header */}
        <View className="mb-3">
          <Text size="body3" className="font-medium">
            {t('MES-565')}
          </Text>
        </View>
      </View>
    )
  }, [refreshing, t])

  return (
    <View className="flex-1 bg-white">
      <MedicalDictionaryList
        data={data}
        refreshing={refreshing}
        isGetKeywordsError={isGetKeywordsError}
        onRefresh={handleRefresh}
        onLoadMore={handleLoadMore}
        headerComponent={renderHeaderComponent()}
        showArchiveButton={Boolean(user)}
        onUpdateFavoriteKeywordSuccess={() => {
          queryClient.invalidateQueries({
            queryKey: keywordQueryKeys['dictionary-keywords-by-category'].base(),
            type: 'active',
            refetchType: 'active',
            exact: false,
          })
        }}
      />
    </View>
  )
}
