import { useMemo } from 'react'
import { View } from 'react-native'
import { useShallow } from 'zustand/react/shallow'
import { useProductStore } from '../../stores/ProductStore'
import { SearchProductHeader } from './SearchProductHeader'
import { SearchProductList } from './SearchProductList'
export const SearchProduct = () => {
  const { searchTextValue, hasActiveFilters } = useProductStore(
    useShallow((state) => ({
      searchTextValue: state.searchProductFilters['search_text'],
      hasActiveFilters: state.hasActiveFilters,
    })),
  )

  const shouldShowProductList = useMemo(() => {
    return searchTextValue || hasActiveFilters
  }, [searchTextValue, hasActiveFilters])

  return (
    <View className="flex  flex-col gap-y-3 ">
      <SearchProductHeader showTipsBox={!shouldShowProductList} />
      {shouldShowProductList && <SearchProductList />}
    </View>
  )
}
