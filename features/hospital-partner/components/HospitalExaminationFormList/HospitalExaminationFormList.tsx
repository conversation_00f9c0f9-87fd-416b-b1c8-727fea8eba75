import EmptyBoxIcon from '@/assets/icons/empty-box.svg'
import { SearchInput, SearchInputRef } from '@/components/ui/SearchInput/SearchInput'
import { Skeleton } from '@/components/ui/Skeleton/Skeleton'
import { Text } from '@/components/ui/Text/Text'
import { useAppLanguage } from '@/hooks/common/useAppLanguage'
import colors from '@/styles/_colors'
import { useLocalSearchParams } from 'expo-router'
import { useCallback, useMemo, useRef, useState } from 'react'
import { useTranslation } from 'react-i18next'
import { ActivityIndicator, FlatList, RefreshControl, View } from 'react-native'
import { useGetInfinteHospitalExaminationForms } from '../../hooks/query/useGetInfinteHospitalExaminationForms'
import { ExaminationForm } from '../../types'
import { HospitalExaminationFormItem } from './HospitalExaminationFormItem'

interface HospitalExaminationFormListProps {
  isShowSearchInput?: boolean
}

type ListItem =
  | { type: 'examination_form'; examinationForm: ExaminationForm }
  | { type: 'loading' }
  | { type: 'loading_skeleton' }

export const HospitalExaminationFormList = ({
  isShowSearchInput = true,
}: HospitalExaminationFormListProps) => {
  const { hospitalId, facultyId } = useLocalSearchParams()
  const { t } = useTranslation()
  const { primaryLanguage } = useAppLanguage()
  const [searchInputValue, setSearchInputValue] = useState('')
  const [searchQuery, setSearchQuery] = useState('')
  const [refreshing, setRefreshing] = useState(false)
  const searchInputRef = useRef<SearchInputRef>(null)

  const params = useMemo(() => {
    return {
      limit: 10,
      locale: primaryLanguage,
      where: {
        name: {
          like: searchQuery ? searchQuery.trim() : undefined,
        },
        faculty: {
          equals: facultyId as string,
        },
        hospital: {
          equals: hospitalId as string,
        },
      },
      sort: '-createdAt',
      select: {
        id: true,
        name: true,
        faculty: true,
        hospital: true,
      },
      populate: {
        faculties: {
          name: true,
        },
        hospitals: {
          id: true,
        },
      },
    }
  }, [primaryLanguage, searchQuery, facultyId, hospitalId])

  const {
    hospitalExaminationForms,
    isGetHospitalExaminationFormsLoading,
    isGetHospitalExaminationFormsError,
    isFetchingNextPage: isGetHospitalExaminationFormsFetchingNextPage,
    fetchNextPage,
    hasNextPage,
    refetch,
  } = useGetInfinteHospitalExaminationForms({
    params,
    config: {
      staleTime: 5 * 60 * 1000,
    },
  })

  // Flatten all examination forms from all pages
  const allExaminationForms = useMemo(() => {
    if (!hospitalExaminationForms?.pages) return []
    return hospitalExaminationForms.pages.flatMap((page) => page?.docs || [])
  }, [hospitalExaminationForms])

  // Handle pull-to-refresh
  const handleRefresh = useCallback(async () => {
    setRefreshing(true)
    try {
      await refetch()
    } finally {
      setRefreshing(false)
    }
  }, [refetch])

  // Handle load more
  const handleLoadMore = useCallback(() => {
    if (hasNextPage && !isGetHospitalExaminationFormsFetchingNextPage) {
      fetchNextPage()
    }
  }, [hasNextPage, isGetHospitalExaminationFormsFetchingNextPage, fetchNextPage])

  const handleSearchInputChange = (text: string) => {
    setSearchInputValue(text)
  }

  const handleClearSearchInput = () => {
    setSearchInputValue('')
    setSearchQuery('')
  }

  // Build data array with different ListItem types
  const data = useMemo((): ListItem[] => {
    const items: ListItem[] = []

    // Show loading skeletons during initial load or refresh (but not during pagination)
    const isShowingLoadingSkeleton =
      (isGetHospitalExaminationFormsLoading || refreshing) &&
      !isGetHospitalExaminationFormsFetchingNextPage

    if (isShowingLoadingSkeleton) {
      items.push({ type: 'loading_skeleton' })
    } else {
      // Add examination forms as individual items
      allExaminationForms.forEach((examinationForm) => {
        items.push({ type: 'examination_form', examinationForm })
      })

      // Loading indicator for pagination
      if (isGetHospitalExaminationFormsFetchingNextPage) {
        items.push({ type: 'loading' })
      }
    }

    return items
  }, [
    allExaminationForms,
    isGetHospitalExaminationFormsFetchingNextPage,
    isGetHospitalExaminationFormsLoading,
    refreshing,
  ])

  // Render item based on ListItem type
  const renderItem = useCallback(({ item }: { item: ListItem }) => {
    switch (item.type) {
      case 'examination_form':
        return <HospitalExaminationFormItem examinationForm={item.examinationForm} />

      case 'loading':
        return (
          <View className="items-center py-4">
            <ActivityIndicator size="small" />
          </View>
        )

      case 'loading_skeleton':
        return (
          <View className="flex flex-col gap-y-4">
            {new Array(6).fill(0).map((_, index) => (
              <Skeleton key={index} className="h-20 w-full rounded-lg" />
            ))}
          </View>
        )

      default:
        return null
    }
  }, [])

  const renderEmptyComponent = useCallback(() => {
    if (isGetHospitalExaminationFormsError) {
      return (
        <View className="items-center py-8">
          <Text size="body6" className="text-red-500">
            {t('MES-197')}
          </Text>
        </View>
      )
    }
    if (isGetHospitalExaminationFormsLoading || isGetHospitalExaminationFormsFetchingNextPage) {
      return null
    }

    return (
      <View className="items-center py-8">
        <EmptyBoxIcon />
        <Text size="body6" variant="default">
          {t('MES-813')}
        </Text>
      </View>
    )
  }, [
    isGetHospitalExaminationFormsError,
    isGetHospitalExaminationFormsLoading,
    isGetHospitalExaminationFormsFetchingNextPage,
  ])

  const renderSeparator = useCallback(() => {
    return <View className="h-2" />
  }, [])

  const keyExtractor = useCallback((item: ListItem, index: number) => {
    if (item.type === 'examination_form') {
      return `examination-form-${item.examinationForm.id}`
    }
    return `${item.type}-${index}`
  }, [])

  return (
    <View className="flex-1 flex-col gap-y-6 bg-white py-4">
      {/* Header */}
      {isShowSearchInput && (
        <View className="flex flex-col gap-y-3 px-4">
          <View className="flex flex-col gap-y-3 bg-white">
            <View className="flex flex-col gap-y-3">
              <View className="flex flex-row items-center gap-x-2">
                {/* Search Input */}
                <SearchInput
                  ref={searchInputRef}
                  placeholder={t('MES-808')}
                  value={searchInputValue}
                  onChangeText={handleSearchInputChange}
                  onClear={handleClearSearchInput}
                  onSubmitEditing={() => {
                    setSearchQuery(searchInputValue)
                    setSearchInputValue(searchInputValue)
                  }}
                />
              </View>
            </View>
          </View>
        </View>
      )}

      {/* List */}
      <View className="flex-1 px-4 pb-4">
        <FlatList
          showsVerticalScrollIndicator={false}
          data={data}
          renderItem={renderItem}
          keyExtractor={keyExtractor}
          contentContainerStyle={{
            paddingBottom: 16,
          }}
          ItemSeparatorComponent={renderSeparator}
          ListEmptyComponent={renderEmptyComponent}
          onEndReached={handleLoadMore}
          onEndReachedThreshold={0.1}
          refreshControl={
            <RefreshControl
              refreshing={refreshing}
              onRefresh={handleRefresh}
              colors={[colors.primary['500']]}
              tintColor={colors.primary['500']}
              progressBackgroundColor="#FFFFFF"
            />
          }
        />
      </View>
    </View>
  )
}
