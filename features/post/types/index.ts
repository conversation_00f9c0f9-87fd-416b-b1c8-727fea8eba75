import { Keyword } from '@/types/keyword.type'
import { Media } from '@/types/media.type'

import { User } from '@/types/user.type'

export interface Post {
  id: string
  title: string
  featured?: boolean | null
  language?: ('vi' | 'ja') | null
  heroImage?: (string | null) | Media
  content: {
    root: {
      type: string
      children: {
        type: string
        version: number
        [k: string]: unknown
      }[]
      direction: ('ltr' | 'rtl') | null
      format: 'left' | 'start' | 'center' | 'right' | 'end' | 'justify' | ''
      indent: number
      version: number
    }
    [k: string]: unknown
  }
  relatedKeywords?: (string | Keyword)[] | null
  relatedPosts?: (string | Post)[] | null
  meta?: {
    title?: string | null
    /**
     * Maximum upload file size: 12MB. Recommended file size for images is <500KB.
     */
    image?: (string | null) | Media
    description?: string | null
  }
  publishedAt?: string | null
  authors?: (string | User)[] | null
  populatedAuthors?:
    | {
        id?: string | null
        name?: string | null
      }[]
    | null
  categories?: (string | null) | PostCategory
  slug?: string | null
  slugLock?: boolean | null
  /**
   * This field is automatically populated from post title and post content editor
   */
  mergedText?: string | null
  /**
   * Dify document ID for this post
   */
  difyDocumentId?: string | null
  /**
   * Use this field to trigger Dify document sync (support bulk edit)
   */
  triggerDifyDocumentSync?: boolean | null
  updatedAt: string
  createdAt: string
  totalComments?: number | null
}
export interface PostCategory {
  id: string
  title: string
  updatedAt: string
  createdAt: string
}
