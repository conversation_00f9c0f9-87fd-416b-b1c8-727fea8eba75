/**
 * BackHandler Polyfill for React Native 0.79+
 *
 * This polyfill adds the deprecated removeEventListener method back to BackHandler
 * to support older libraries that haven't migrated to the new subscription API yet.
 */

import { BackHandler } from 'react-native'

// Store subscriptions mapped by listener functions
const subscriptions = new Map()

// Save the original addEventListener
const originalAddEventListener = BackHandler.addEventListener

// Override addEventListener to track subscriptions
BackHandler.addEventListener = function (eventName, handler) {
  const subscription = originalAddEventListener.call(BackHandler, eventName, handler)
  subscriptions.set(handler, subscription)
  return subscription
}

// Add the deprecated removeEventListener method back
BackHandler.removeEventListener = function (eventName, handler) {
  const subscription = subscriptions.get(handler)
  if (subscription) {
    subscription.remove()
    subscriptions.delete(handler)
  }
}

export default BackHandler
