import { usePathname } from 'expo-router'
import { useEffect } from 'react'
import { analyticsService } from '../services/analytics.service'

export function useScreenTracking() {
  const pathname = usePathname()

  useEffect(() => {
    if (!pathname) return

    const screenName = pathname === '/' ? 'Home' : pathname.replace('/', '')

    try {
      void analyticsService.trackScreenRoute(screenName)
    } catch (_) {}
  }, [pathname])
}
