import { Text } from '@/components/ui/Text/Text'
import { BLURHASH_CODE } from '@/constants/global.constant'
import { MedicineBuyButton, MedicineType, Product } from '@/features/product/types'
import { FavoriteProduct } from '@/features/user/types'
import { StyledExpoImage } from '@/libs/styled'
import { APP_ROUTES } from '@/routes/appRoutes'
import { Media } from '@/types/media.type'
import { cn } from '@/utils/cn'
import * as Haptics from 'expo-haptics'
import { Link, LinkProps } from 'expo-router'
import { openBrowserAsync } from 'expo-web-browser'
import { TouchableOpacity, View } from 'react-native'
import Svg, { Path } from 'react-native-svg'
import { useUpdateFavoriteProduct } from '../../hooks/query/useUpdateFavoriteProduct'

interface FavoriteProductItemProps {
  product: Product
  favoriteProductId: string
  onRemoveFromFavorites: (favoriteProductId: string) => void
  onAddToFavorites: (favoriteProduct: FavoriteProduct) => void
}

export const FavoriteProductItem = ({
  product,
  favoriteProductId,
  onRemoveFromFavorites,
  onAddToFavorites,
}: FavoriteProductItemProps) => {
  const { heroImage, stores, medicineType, description, isFavorite } = product
  const { updateFavoriteProductMutation, isUpdateFavoriteProductPending } =
    useUpdateFavoriteProduct()
  const imageMedia = heroImage as Media
  const imageURL = imageMedia?.thumbnailURL || imageMedia?.url

  const handleStorePress = async (url: string) => {
    await openBrowserAsync(url)
  }

  const handleFavoriteToggle = async () => {
    try {
      await updateFavoriteProductMutation(
        {
          id: product.id,
          type: isFavorite ? 'delete' : 'add',
        },
        {
          onSuccess: () => {
            onRemoveFromFavorites(favoriteProductId)
          },
        },
      )
    } catch (error) {
      // Handle error if needed
      console.error('Failed to update favorite:', error)
    }
  }

  return (
    <Link
      href={
        (APP_ROUTES.PRODUCTS.children?.PRODUCTS_DETAIL_V2.path +
          `/${product.slug}`) as LinkProps['href']
      }
      asChild
      disabled={isUpdateFavoriteProductPending}
    >
      <TouchableOpacity
        className={cn(
          'w-full flex-row gap-x-3 overflow-hidden rounded-lg bg-custom-background-hover px-3 py-2',
          isUpdateFavoriteProductPending && 'opacity-50',
        )}
        onPress={() => {
          Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light)
        }}
        disabled={isUpdateFavoriteProductPending}
      >
        {/* Product image */}
        <View className=" h-[80px] w-32 shrink-0 overflow-hidden rounded-lg bg-gray-100">
          {imageURL && (
            <StyledExpoImage
              source={imageURL}
              contentFit="cover"
              transition={300}
              placeholder={BLURHASH_CODE}
              className="h-full w-full"
            />
          )}
        </View>

        <View className="flex-1 flex-col gap-y-1">
          {/* Product title */}
          <View>
            <Text size="body8" numberOfLines={2}>
              {product.title}
            </Text>
          </View>

          {/* Product description */}
          {description && (
            <View>
              <Text size="body9" variant="subdued" numberOfLines={1}>
                {description}
              </Text>
            </View>
          )}

          {/* Store icons and medicine type icons */}
          <View className="mt-auto flex-row items-center justify-between gap-x-3">
            <View className="flex-row items-center justify-between">
              <View className="flex-row items-center gap-2">
                {stores?.slice(0, 3).map((store) => {
                  const medicineStore = store['medicine-store'] as MedicineBuyButton
                  const storeLink = store?.url
                  const { logo } = (medicineStore as MedicineBuyButton) || {}
                  const logoMedia = logo as Media
                  const logoURL = logoMedia?.thumbnailURL || logoMedia?.url

                  return (
                    <TouchableOpacity
                      key={store.id}
                      className="aspect-square h-5 w-5 overflow-hidden rounded"
                      onPress={(e) => {
                        e.stopPropagation()
                        storeLink && handleStorePress(storeLink)
                      }}
                    >
                      <StyledExpoImage
                        source={logoURL}
                        contentFit="contain"
                        transition={300}
                        placeholder={BLURHASH_CODE}
                        className="h-full w-full"
                      />
                    </TouchableOpacity>
                  )
                })}
              </View>

              <View className="flex-row items-center gap-1">
                {medicineType?.slice(0, 2).map((type) => {
                  const medicineTypeItem = type as MedicineType
                  const { icon } = (medicineTypeItem as MedicineType) || {}
                  const iconMedia = icon as Media
                  const iconURL = iconMedia?.thumbnailURL || iconMedia?.url

                  if (typeof type === 'string') return null
                  return (
                    <View key={type.id} className="aspect-square h-5 w-5 overflow-hidden rounded">
                      <StyledExpoImage
                        source={iconURL}
                        contentFit="contain"
                        transition={300}
                        placeholder={BLURHASH_CODE}
                        className="h-full w-full"
                      />
                    </View>
                  )
                })}
              </View>
            </View>

            <TouchableOpacity
              onPress={handleFavoriteToggle}
              disabled={isUpdateFavoriteProductPending}
            >
              <Svg
                width={20}
                height={20}
                viewBox="0 0 20 20"
                fill={isFavorite ? '#EF4444' : 'none'}
              >
                <Path
                  d="M10.516 17.3418C10.2327 17.4418 9.76602 17.4418 9.48268 17.3418C7.06602 16.5168 1.66602 13.0752 1.66602 7.24183C1.66602 4.66683 3.74102 2.5835 6.29935 2.5835C7.81602 2.5835 9.15768 3.31683 9.99935 4.45016C10.841 3.31683 12.191 2.5835 13.6993 2.5835C16.2577 2.5835 18.3327 4.66683 18.3327 7.24183C18.3327 13.0752 12.9327 16.5168 10.516 17.3418Z"
                  stroke="#EF4444"
                  strokeWidth={1.5}
                  strokeLinecap="round"
                  strokeLinejoin="round"
                />
              </Svg>
            </TouchableOpacity>
          </View>
        </View>
      </TouchableOpacity>
    </Link>
  )
}
