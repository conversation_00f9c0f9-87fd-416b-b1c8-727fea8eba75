import JapanFlagIcon from '@/assets/login/japan-flag.svg'
import VietnamFlagIcon from '@/assets/login/vietnam-flag.svg'
import ArrowLeftIcon from '@/assets/profile/arrow-left.svg'
import CheckIcon from '@/assets/profile/check-icon.svg'
import { CustomRadioIndicator, Radio, RadioGroup, RadioLabel } from '@/components/ui/Radio/Radio'
import { Text } from '@/components/ui/Text/Text'
import { LOCALE_MAP } from '@/constants/locale.constant'
import { LocaleEnum } from '@/enums/locale.enum'
import { useAppLanguage } from '@/hooks/common/useAppLanguage'
import { useLoadingScreen } from '@/hooks/common/useLoadingScreen'
import { useRouter } from 'expo-router'
import React, { useState } from 'react'
import { useTranslation } from 'react-i18next'
import { TouchableOpacity, View } from 'react-native'
import { ScrollView } from 'react-native-gesture-handler'
import { SafeAreaView } from 'react-native-safe-area-context'

// Language Option Data
interface LanguageOptionData {
  id: LocaleEnum
  label: string
  code: string
}

const languageOptions: LanguageOptionData[] = Object.entries(LOCALE_MAP).map(([key, value]) => ({
  id: key as LocaleEnum,
  label: value.label[key as LocaleEnum],
  code: value.code,
}))

// Custom Radio Indicator with filled circle

// Get flag based on language code
const getFlagIcon = (code: string) => {
  switch (code) {
    case 'vi':
      return VietnamFlagIcon
    case 'ja':
      return JapanFlagIcon
    default:
      return VietnamFlagIcon
  }
}

// Custom Radio Item Component
function LanguageRadioItem({
  option,
  isSelected,
}: {
  option: LanguageOptionData
  isSelected: boolean
}) {
  const FlagIcon = getFlagIcon(option.code)

  return (
    <Radio value={option.id} className="flex-row items-center justify-between gap-x-3 px-4 py-2">
      <CustomRadioIndicator isSelected={isSelected} />
      <RadioLabel className="flex-1">
        <Text size="body6" variant="default">
          {option.label}
        </Text>
      </RadioLabel>
      <View className="h-6 w-6 items-center justify-center">
        <FlagIcon width={24} height={24} />
      </View>
    </Radio>
  )
}

// Language Section Component
interface LanguageSectionProps {
  title: string
  options: LanguageOptionData[]
  selectedValue: string
  onValueChange: (value: string) => void
}

function LanguageSection({ title, options, selectedValue, onValueChange }: LanguageSectionProps) {
  return (
    <View className="gap-3">
      <Text size="body6" variant="default">
        {title}
      </Text>
      <View>
        <RadioGroup value={selectedValue} onChange={onValueChange}>
          {options.map((option) => (
            <LanguageRadioItem
              key={option.id}
              option={option}
              isSelected={selectedValue === option.id}
            />
          ))}
        </RadioGroup>
      </View>
    </View>
  )
}

// Language Header Component
interface LanguageHeaderProps {
  onBack: () => void
  onSave: () => void
}

function LanguageHeader({ onBack, onSave }: LanguageHeaderProps) {
  const { t } = useTranslation()
  return (
    <View className="flex-row items-center justify-between border-b border-neutral-200 bg-white px-4 py-3">
      <TouchableOpacity className="h-6 w-6 items-center justify-center" onPress={onBack}>
        <ArrowLeftIcon width={16} height={16} />
      </TouchableOpacity>

      <Text variant="primary" size="body3">
        {t('MES-42')}
      </Text>

      <TouchableOpacity
        className="h-6 w-6 items-center justify-center"
        onPress={onSave}
        hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
      >
        <CheckIcon width={16} height={16} />
      </TouchableOpacity>
    </View>
  )
}

export const SwitchLanguageScreen = () => {
  const router = useRouter()
  const { primaryLanguage, secondaryLanguage, setPrimaryAndSecondary } = useAppLanguage()
  const [selectedPrimary, setSelectedPrimary] = useState<string>(primaryLanguage)
  const [selectedSecondary, setSelectedSecondary] = useState<string>(secondaryLanguage)
  const { showLoading, hideLoading } = useLoadingScreen()
  // Get the opposite language
  const getOppositeLanguage = (language: string): string => {
    return language === LocaleEnum.VI ? LocaleEnum.JA : LocaleEnum.VI
  }

  // Handle primary language change
  const handlePrimaryChange = (newPrimary: string) => {
    setSelectedPrimary(newPrimary)
    // If secondary is the same as new primary, change secondary to opposite
    if (selectedSecondary === newPrimary) {
      const oppositeLanguage = getOppositeLanguage(newPrimary)

      setSelectedSecondary(oppositeLanguage)
    }
  }

  // Handle secondary language change
  const handleSecondaryChange = (newSecondary: string) => {
    setSelectedSecondary(newSecondary)
    // If primary is the same as new secondary, change primary to opposite
    if (selectedPrimary === newSecondary) {
      const oppositeLanguage = getOppositeLanguage(newSecondary)

      setSelectedPrimary(oppositeLanguage)
    }
  }

  const handleSave = async () => {
    showLoading()
    await setPrimaryAndSecondary(selectedPrimary)
    hideLoading()
    router.back()
  }

  const handleBack = () => {
    router.back()
  }

  return (
    <SafeAreaView className="flex-1 bg-white">
      <LanguageHeader onBack={handleBack} onSave={handleSave} />

      <ScrollView className="flex-1" showsVerticalScrollIndicator={false}>
        <View className="gap-4 p-4">
          <LanguageSection
            title="Ngôn ngữ chính"
            options={languageOptions}
            selectedValue={selectedPrimary}
            onValueChange={handlePrimaryChange}
          />

          <LanguageSection
            title="Ngôn ngữ phụ"
            options={languageOptions}
            selectedValue={selectedSecondary}
            onValueChange={handleSecondaryChange}
          />
        </View>
      </ScrollView>
    </SafeAreaView>
  )
}
