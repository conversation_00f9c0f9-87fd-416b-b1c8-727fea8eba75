import FileImageIcon from '@/assets/icons/file-image-icon.svg'
import PdfIcon from '@/assets/icons/pdf-icon.svg'
import { Text } from '@/components/ui/Text/Text'

import { Media } from '@/types/media.type'
import dayjs from 'dayjs'
import * as Haptics from 'expo-haptics'
import { openBrowserAsync } from 'expo-web-browser'
import { useState } from 'react'
import { Modal, StyleSheet, TouchableOpacity, View } from 'react-native'
import Gallery from 'react-native-awesome-gallery'
import { MedicalDocumentFileTypeEnum } from '../../enums'
import { MedicalDocument } from '../../types'
interface MedicalDocumentItemProps {
  medicalDocument: MedicalDocument
}

export const MedicalDocumentItem = ({ medicalDocument }: MedicalDocumentItemProps) => {
  const [imageVisible, setImageVisible] = useState(false)
  const [pdfVisible, setPdfVisible] = useState(false)

  const handlePress = (medicalDocument: MedicalDocument) => {
    const fileType = medicalDocument.fileType
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light)

    if (fileType === MedicalDocumentFileTypeEnum.PDF) {
      if (pdfUrl) {
        openBrowserAsync(pdfUrl)
      }
    } else if (fileType === MedicalDocumentFileTypeEnum.IMAGE) {
      const galleryData = getGalleryData()
      if (galleryData.length > 0) {
        setImageVisible(true)
      }
    }
  }

  const closeImageModal = () => {
    setImageVisible(false)
  }

  const renderFileIcon = () => {
    if (medicalDocument.fileType === MedicalDocumentFileTypeEnum.PDF) {
      return <PdfIcon width={36} height={36} />
    } else {
      return <FileImageIcon width={36} height={36} />
    }
  }

  // Prepare gallery data for images
  const getGalleryData = () => {
    if (medicalDocument.fileType !== MedicalDocumentFileTypeEnum.IMAGE) {
      return []
    }

    const file = medicalDocument.file
    if (typeof file === 'string') {
      return []
    } else if (file && typeof file === 'object') {
      const media = file as Media
      const imageUrl = media.url || media.thumbnailURL || ''
      return imageUrl ? [imageUrl] : []
    }
    return []
  }

  // Get PDF URL for WebView
  const getPdfUrl = () => {
    const file = medicalDocument.file
    if (typeof file === 'string') {
      return null
    } else if (file && typeof file === 'object') {
      const media = file as Media
      return media.url || ''
    }
    return null
  }

  const galleryData = getGalleryData()
  const pdfUrl = getPdfUrl()

  return (
    <View>
      <TouchableOpacity
        onPress={() => handlePress(medicalDocument)}
        className="flex min-h-16 flex-row items-center gap-x-3 rounded-lg bg-custom-background-form p-3"
      >
        {/* File Icon */}
        <View className="flex-shrink-0">{renderFileIcon()}</View>

        {/* Document Info */}
        <View className="flex-1 flex-col gap-y-1">
          <Text size="body6" variant="default" numberOfLines={2}>
            {medicalDocument.name}
          </Text>
          <Text size="body9" variant="subdued">
            {dayjs(medicalDocument.createdAt).format('YYYY/MM/DD')}
          </Text>
        </View>
      </TouchableOpacity>

      {/* Modal for Image Gallery */}
      <Modal
        visible={imageVisible}
        transparent={false}
        animationType="slide"
        onRequestClose={closeImageModal}
      >
        <View style={styles.modalContainer}>
          {/* Close Button */}
          <TouchableOpacity style={styles.closeButton} onPress={closeImageModal}>
            <Text style={styles.closeButtonText}>✕</Text>
          </TouchableOpacity>

          {medicalDocument.fileType === MedicalDocumentFileTypeEnum.IMAGE &&
            galleryData.length > 0 && (
              <Gallery
                data={galleryData as string[]}
                onSwipeToClose={closeImageModal}
                loop={false}
                doubleTapEnabled={true}
              />
            )}
        </View>
      </Modal>
    </View>
  )
}

const styles = StyleSheet.create({
  modalContainer: {
    flex: 1,
    backgroundColor: 'white',
  },
  closeButton: {
    position: 'absolute',
    top: 50,
    right: 20,
    zIndex: 10,
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
    borderRadius: 20,
    width: 40,
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
  },
  closeButtonText: {
    color: 'white',
    fontSize: 18,
    fontWeight: 'bold',
  },
  webview: {
    flex: 1,
    marginTop: 50, // Account for close button
  },
})
