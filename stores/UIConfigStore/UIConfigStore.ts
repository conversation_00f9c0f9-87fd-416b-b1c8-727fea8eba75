import { asyncStorageService } from '@/services/async-storage/async-storage.service'
import { create } from 'zustand'
import { createJSONStorage, persist } from 'zustand/middleware'

interface UIConfigState {
  showFurigana: boolean
  setShowFurigana: (value: boolean) => void
}

const useUIConfigStore = create<UIConfigState>()(
  persist(
    (set) => ({
      showFurigana: true,
      setShowFurigana: (value) => set({ showFurigana: value }),
    }),
    {
      name: 'ui-config-store',
      storage: createJSONStorage(() => ({
        setItem: async (name, value) => {
          await asyncStorageService.setItem(name, value)
        },
        getItem: async (name) => {
          const value = await asyncStorageService.getItem(name)
          return value || null
        },
        removeItem: async (name) => {
          await asyncStorageService.removeItem(name)
        },
      })),
      partialize: (state) => ({
        showFurigana: state.showFurigana,
      }),
    },
  ),
)

export { useUIConfigStore }
