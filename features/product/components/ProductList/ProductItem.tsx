import { Text } from '@/components/ui/Text/Text'
import { BLURHASH_CODE } from '@/constants/global.constant'
import { MedicineBuyButton, MedicineType, Product } from '@/features/product/types'
import { StyledExpoImage } from '@/libs/styled'
import { APP_ROUTES } from '@/routes/appRoutes'
import { Media } from '@/types/media.type'
import * as Haptics from 'expo-haptics'
import { Link, LinkProps } from 'expo-router'
import { openBrowserAsync } from 'expo-web-browser'
import { TouchableOpacity, View } from 'react-native'

interface ProductItemProps {
  product: Product
}

export const ProductItem = ({ product }: ProductItemProps) => {
  const { heroImage, stores, medicineType } = product
  const imageMedia = heroImage as Media
  const imageURL = imageMedia?.thumbnailURL || imageMedia?.url

  const handleStorePress = async (url: string) => {
    await openBrowserAsync(url)
  }

  return (
    <Link
      href={
        (APP_ROUTES.PRODUCTS.children?.PRODUCTS_DETAIL_V2.path +
          `/${product.slug}`) as LinkProps['href']
      }
      asChild
    >
      <TouchableOpacity
        className="h-full flex-1 flex-col gap-y-2 overflow-hidden rounded-lg bg-custom-background-hover p-3 shadow-sm"
        onPress={() => {
          Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light)
        }}
      >
        {/* Store icons  */}
        <View className="h-5 flex-row items-center justify-between gap-1 overflow-hidden">
          <View className="flex-row items-center gap-1">
            {stores?.slice(0, 3).map((store) => {
              const medicineStore = store['medicine-store'] as MedicineBuyButton
              const storeLink = store?.url
              const { logo } = (medicineStore as MedicineBuyButton) || {}
              const logoMedia = logo as Media
              const logoURL = logoMedia?.thumbnailURL || logoMedia?.url

              return (
                <TouchableOpacity
                  key={store.id}
                  className="aspect-square h-[18px] w-[18px] overflow-hidden  "
                  onPress={() => storeLink && handleStorePress(storeLink)}
                >
                  <StyledExpoImage
                    source={logoURL}
                    contentFit="contain"
                    transition={300}
                    placeholder={BLURHASH_CODE}
                    className="h-full w-full"
                  />
                </TouchableOpacity>
              )
            })}
          </View>
          <View className="flex-row items-center gap-1">
            {medicineType?.map((type) => {
              const medicineType = type as MedicineType

              const { icon } = (medicineType as MedicineType) || {}
              const iconMedia = icon as Media
              const iconURL = iconMedia?.thumbnailURL || iconMedia?.url
              if (typeof type === 'string') return null
              return (
                <View key={type.id} className="aspect-square h-[18px] w-[18px] overflow-hidden  ">
                  <StyledExpoImage
                    source={iconURL}
                    contentFit="contain"
                    transition={300}
                    placeholder={BLURHASH_CODE}
                    className="h-full w-full"
                  />
                </View>
              )
            })}
          </View>
        </View>

        {/* Product image */}
        <View className="aspect-square w-full overflow-hidden rounded-lg bg-gray-100">
          {imageURL ? (
            <StyledExpoImage
              source={imageURL}
              contentFit="cover"
              transition={300}
              placeholder={BLURHASH_CODE}
              className="h-full w-full"
            />
          ) : (
            <View className="h-full w-full items-center justify-center bg-gray-200">
              <Text size="body6" className="text-gray-500">
                No Image
              </Text>
            </View>
          )}
        </View>

        {/* Product title */}
        <View>
          <Text size="body8" className="line-clamp-2" numberOfLines={2}>
            {product.title}
          </Text>
        </View>
      </TouchableOpacity>
    </Link>
  )
}
