import HealthCareIcon from '@/assets/icons/health-care-icon.svg'
import MedicalDoumentIcon from '@/assets/icons/medical-document-icon.svg'
import { Text } from '@/components/ui/Text/Text'
import { useAppLanguage } from '@/hooks/common/useAppLanguage'
import { Faculty } from '@/types/faculty.type'
import { Media } from '@/types/media.type'

import { Button } from '@/components/ui/Button/Button'
import { Skeleton } from '@/components/ui/Skeleton/Skeleton'
import { BLURHASH_CODE } from '@/constants/global.constant'
import { useAuthentication } from '@/hooks/auth/useAuthentication'
import { StyledExpoImage } from '@/libs/styled'
import { APP_ROUTES } from '@/routes/appRoutes'
import * as Haptics from 'expo-haptics'
import { Link, LinkProps } from 'expo-router'
import React, { useCallback, useMemo } from 'react'
import { useTranslation } from 'react-i18next'
import { FlatList, View } from 'react-native'
import { useGetHomeFaculties } from '../../hooks/query/home-faculty/useGetHomeFaculties'

export const HomeFaculties = () => {
  const { status } = useAuthentication()

  const { t } = useTranslation()
  const { primaryLanguage } = useAppLanguage()
  const { homeFaculties, isGetHomeFacultiesLoading } = useGetHomeFaculties({
    params: {
      locale: primaryLanguage,
      limit: 50,
      sort: ['-hasQuestions', 'name'],
      depth: 5,
    },
  })

  const groupedFaculties = useMemo(() => {
    const groups: Faculty[][] = []
    if (!homeFaculties?.docs) return []
    for (let i = 0; i < homeFaculties?.docs.length; i += 4) {
      groups.push(homeFaculties?.docs.slice(i, i + 4))
    }
    return groups
  }, [homeFaculties])

  const renderItem = useCallback(
    ({ item }: { item: Faculty[] }) => (
      <View className="flex-1">
        <View className="flex-col gap-3">
          {item.map((faculty) => {
            const iconMedia = faculty?.icon as Media

            const iconUrl = iconMedia?.url || iconMedia?.thumbnailURL || ''

            return (
              <Link
                href={
                  {
                    pathname:
                      status === 'success'
                        ? APP_ROUTES.MEDICAL_HANDBOOK_FACULTIES.path + '/[id]'
                        : APP_ROUTES.MEDICAL_HANDBOOK_FACULTIES.path + '/[id]',
                    params: { id: faculty.id },
                  } as LinkProps['href']
                }
                key={faculty.id}
                asChild
              >
                <Button
                  className="h-[50px] w-[232px] flex-row items-center justify-start gap-3 rounded-lg bg-primary-50 px-3 py-2 "
                  onPress={() => {
                    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light)
                  }}
                >
                  {iconUrl ? (
                    <StyledExpoImage
                      source={iconUrl}
                      contentFit="cover"
                      transition={1000}
                      className="h-8 w-8"
                      placeholder={BLURHASH_CODE}
                    />
                  ) : (
                    <HealthCareIcon className="h-8 w-8" />
                  )}
                  <Text
                    size="body8"
                    variant="default"
                    className="line-clamp-1 whitespace-pre"
                    numberOfLines={1}
                  >
                    {faculty.name}
                  </Text>
                </Button>
              </Link>
            )
          })}
        </View>
      </View>
    ),
    [],
  )

  if (!isGetHomeFacultiesLoading && !groupedFaculties.length) {
    return null
  }

  return (
    <View className="flex-1 px-4">
      <View className="mb-5 flex-row items-center gap-3">
        <Text size="heading8" variant="primary">
          {t('MES-556')}
        </Text>
        <MedicalDoumentIcon className="h-6 w-6" />
      </View>

      {isGetHomeFacultiesLoading ? (
        <HomeFacultiesLoading />
      ) : (
        <FlatList
          horizontal
          pagingEnabled
          data={groupedFaculties}
          showsHorizontalScrollIndicator={false}
          decelerationRate={'fast'}
          snapToInterval={232 + 12}
          contentContainerStyle={{
            gap: 12,
          }}
          renderItem={renderItem}
        />
      )}
    </View>
  )
}

const HomeFacultiesLoading = () => {
  return (
    <FlatList
      horizontal
      pagingEnabled
      data={Array.from({ length: 4 }, () => new Array(4).fill(0))}
      showsHorizontalScrollIndicator={false}
      decelerationRate={'fast'}
      snapToInterval={232 + 12}
      contentContainerStyle={{
        gap: 12,
      }}
      renderItem={({ item }) => (
        <View className="flex-1">
          <View className="flex-col gap-3">
            {item.map((_, index) => {
              return <Skeleton key={index} className="h-[50px] w-[232px] rounded-lg bg-gray-200" />
            })}
          </View>
        </View>
      )}
    />
  )
}
