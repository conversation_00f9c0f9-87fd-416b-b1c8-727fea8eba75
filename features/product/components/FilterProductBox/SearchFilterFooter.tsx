import {
  BaseFilterFooter,
  type BaseFilterItem,
} from '@/components/Filter/BaseFilterFooter/BaseFilterFooter'
import { BottomSheetFooter, BottomSheetFooterProps } from '@gorhom/bottom-sheet'
import * as Haptics from 'expo-haptics'
import { forwardRef, useMemo } from 'react'
import { useTranslation } from 'react-i18next'
import { View } from 'react-native'
import { useShallow } from 'zustand/react/shallow'
import {
  SearchProductFilterItem,
  SearchProductFilterType,
  useProductStore,
} from '../../stores/ProductStore'

interface SearchFilterFooterProps extends BottomSheetFooterProps {
  closeSheet?: () => void
}

export const SearchFilterFooter = forwardRef<View, SearchFilterFooterProps>(
  ({ closeSheet, ...props }, ref) => {
    return (
      <BottomSheetFooter {...props}>
        <SearchFilterFooterContent ref={ref} closeSheet={closeSheet} {...props} />
      </BottomSheetFooter>
    )
  },
)

interface SearchFilterFooterContentProps extends BottomSheetFooterProps {
  closeSheet?: () => void
}

const SearchFilterFooterContent = forwardRef<View, SearchFilterFooterContentProps>(
  ({ closeSheet }, ref) => {
    const { t } = useTranslation()
    const {
      tempSearchFilters,
      applyTempSearchFilters,
      clearAllTempSearchFilters,
      toggleTempSearchFilter,
    } = useProductStore(
      useShallow((state) => ({
        tempSearchFilters: state.tempSearchFilters,
        applyTempSearchFilters: state.applyTempSearchFilters,
        clearAllTempSearchFilters: state.clearAllTempSearchFilters,
        toggleTempSearchFilter: state.toggleTempSearchFilter,
      })),
    )

    const handleClearFilter = (filterItem: BaseFilterItem) => {
      const searchFilterItem = filterItem as SearchProductFilterItem

      // Handle different filter types
      if (searchFilterItem.type === SearchProductFilterType.PRODUCT_TYPE) {
        const currentFilters = tempSearchFilters[SearchProductFilterType.PRODUCT_TYPE] || []
        const isSelected = currentFilters.some((f) => f.id === searchFilterItem.id)

        if (isSelected) {
          // Remove the product type filter
          toggleTempSearchFilter(SearchProductFilterType.PRODUCT_TYPE, searchFilterItem)
        }
      } else if (searchFilterItem.type === SearchProductFilterType.CATEGORY) {
        const currentFilters = tempSearchFilters[SearchProductFilterType.CATEGORY] || []
        const isSelected = currentFilters.some((f) => f.id === searchFilterItem.id)

        if (isSelected) {
          toggleTempSearchFilter(SearchProductFilterType.CATEGORY, searchFilterItem)
        }
      } else if (
        searchFilterItem.type === SearchProductFilterType.AGE_GROUP ||
        searchFilterItem.type === SearchProductFilterType.MEDICINE_TYPE
      ) {
        const currentFilters = tempSearchFilters[searchFilterItem.type] || []
        const isSelected = currentFilters.some((f) => f.id === searchFilterItem.id)

        if (isSelected) {
          toggleTempSearchFilter(searchFilterItem.type, searchFilterItem)
        }
      }
    }

    const handleApplyFilters = () => {
      // Apply temporary filters to actual search filters
      applyTempSearchFilters()
      closeSheet?.()
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light)
    }

    const handleResetFilters = () => {
      clearAllTempSearchFilters()
    }

    const activeFilters = useMemo(() => {
      const filters: SearchProductFilterItem[] = []

      // Add age group filters
      if (tempSearchFilters[SearchProductFilterType.AGE_GROUP]) {
        filters.push(...tempSearchFilters[SearchProductFilterType.AGE_GROUP]!)
      }

      // Add medicine type filters
      if (tempSearchFilters[SearchProductFilterType.MEDICINE_TYPE]) {
        filters.push(...tempSearchFilters[SearchProductFilterType.MEDICINE_TYPE]!)
      }

      // Add product type filters
      if (tempSearchFilters[SearchProductFilterType.PRODUCT_TYPE]) {
        filters.push(...tempSearchFilters[SearchProductFilterType.PRODUCT_TYPE]!)
      }

      // Add category filters
      if (tempSearchFilters[SearchProductFilterType.CATEGORY]) {
        filters.push(...tempSearchFilters[SearchProductFilterType.CATEGORY]!)
      }

      return filters
    }, [tempSearchFilters])

    return (
      <BaseFilterFooter
        ref={ref}
        activeFilters={activeFilters}
        onClearFilter={handleClearFilter}
        maxDisplayCount={5}
        onApply={handleApplyFilters}
        onReset={handleResetFilters}
        applyText={t('MES-281')}
        resetText={t('MES-105')}
        selectedFiltersLabel={t('MES-706')}
        showLessText={t('MES-493')}
      />
    )
  },
)
