import { Params } from '@/types/http.type'
import { InfiniteData, UseInfiniteQueryOptions, useInfiniteQuery } from '@tanstack/react-query'

import { PaginatedDocs } from '@/types/global.type'
import { AxiosRequestConfig } from 'axios'
import { unifiedNotificationService } from '../../services/unified-notification.service'
import { UserPushNotification } from '../../types'
import { unifiedNotificationQueryKeys } from './queryKeys'
export type UserPushNotificationsQueryConfig = Omit<
  UseInfiniteQueryOptions<
    PaginatedDocs<UserPushNotification>,
    Error,
    InfiniteData<PaginatedDocs<UserPushNotification>>,
    (string | Params)[],
    string | undefined
  >,
  'queryFn' | 'queryKey' | 'getNextPageParam' | 'initialPageParam'
>

interface UseGetInfiniteUserPushNotificationsProps {
  params?: Params
  options?: AxiosRequestConfig
  key?: string | number
  config?: UserPushNotificationsQueryConfig
  overrideKey?: (string | Params)[]
}

export const useGetInfiniteUserPushNotifications = ({
  params = {},
  options = {},
  config = {},
  overrideKey,
}: UseGetInfiniteUserPushNotificationsProps = {}) => {
  const {
    isError: isGetUserPushNotificationsError,
    isFetching: isGetUserPushNotificationsLoading,
    data: userPushNotifications,
    fetchNextPage,
    hasNextPage,
    ...rest
  } = useInfiniteQuery({
    queryKey: overrideKey
      ? overrideKey
      : [unifiedNotificationQueryKeys['user-push-notifications'].base(), params],
    queryFn: async ({ pageParam }) => {
      return unifiedNotificationService.getUserPushNotifications({
        params: {
          ...params,
          cursor: pageParam, // use cursor instead of page
        },
        options,
      })
    },
    getNextPageParam: (lastPage) => lastPage?.nextCursor || undefined,
    getPreviousPageParam: () => undefined,
    initialPageParam: undefined,
    ...config,
  })

  return {
    isGetUserPushNotificationsError,
    isGetUserPushNotificationsLoading,
    userPushNotifications,
    fetchNextPage,
    hasNextPage,
    ...rest,
  }
}
