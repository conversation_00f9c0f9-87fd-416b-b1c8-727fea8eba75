import { Keyword } from './keyword.type'
import { Summarize } from './summarize.type'

export interface Question {
  id: string
  name: string
  question: string
  key?: string | null
  note?: string | null
  keywords?: (string | Keyword)[] | null
  category: 'patient' | 'doctor'
  isMultipleChoices?: boolean | null
  canSkipQuestion?: boolean | null
  summarize?: (string | null) | Summarize
  updatedAt: string
  createdAt: string
}
