import { MutationOptions, useMutation } from '@tanstack/react-query'
import { useRef } from 'react'
import { keywordSearchHistoryService } from '../../services/keyword-search-history.service'
import { keywordSearchHistoryMutationKeys } from './queryKeys'

type UpdateKeywordSearchHistoryVariables = { id: string; type: 'add' | 'delete' }

export const useUpdateKeywordSearchHistory = ({
  options,
}: {
  options?: Omit<MutationOptions | undefined, 'mutationFn' | 'mutationKey'>
} = {}) => {
  // Define an AbortController to cancel previous requests
  const abortControllerRef = useRef<AbortController | null>(null)

  const {
    isError: isUpdateKeywordSearchHistoryError,
    isPending: isUpdateKeywordSearchHistoryPending,
    mutate: updateKeywordSearchHistoryMutation,
    ...rest
  } = useMutation({
    mutationKey: keywordSearchHistoryMutationKeys['update-keyword-search-history'].base(),
    mutationFn: async ({ id, type }: UpdateKeywordSearchHistoryVariables) => {
      // Abort any ongoing request before initiating a new one
      if (abortControllerRef.current) {
        abortControllerRef.current.abort()
      }

      // Create a new AbortController for the new request
      const abortController = new AbortController()
      abortControllerRef.current = abortController

      // Pass the signal from AbortController to the service method
      if (type === 'add') {
        return keywordSearchHistoryService.updateKeywordSearchHistory(id, {
          signal: abortControllerRef.current.signal,
        })
      } else if (type === 'delete') {
        return keywordSearchHistoryService.deleteKeywordSearchHistory(id, {
          signal: abortControllerRef.current.signal,
        })
      }

      return Promise.resolve()
    },
    ...options,
  })

  return {
    isUpdateKeywordSearchHistoryError,
    isUpdateKeywordSearchHistoryPending,
    updateKeywordSearchHistoryMutation,
    ...rest,
  }
}
