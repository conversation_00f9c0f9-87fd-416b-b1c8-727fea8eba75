import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/FormField/FormField'
import { CustomRadioIndicator, Radio, RadioGroup, RadioLabel } from '@/components/ui/Radio/Radio'
import { Text } from '@/components/ui/Text/Text'
import { TextInput } from '@/components/ui/TextInput/TextInput'
import { zodResolver } from '@hookform/resolvers/zod'
import { useForm } from 'react-hook-form'
import { useTranslation } from 'react-i18next'
import {
  KeyboardAvoidingView,
  LayoutChangeEvent,
  Platform,
  ScrollView,
  TouchableOpacity,
  View,
} from 'react-native'

import { useAppLanguage } from '@/hooks/common/useAppLanguage'
import { APP_ROUTES } from '@/routes/appRoutes'
import { cn } from '@/utils/cn'
import { LinkProps, useRouter } from 'expo-router'
import { useState } from 'react'
import { SafeAreaView, useSafeAreaInsets } from 'react-native-safe-area-context'
import Toast from 'react-native-toast-message'
import { z } from 'zod'
import { COMMENT_REPORT_REASON_OPTIONS } from '../../constants'
import { CommentReportReasonsEnum } from '../../enums'
import { useReportComment } from '../../hooks/query/useReportComment'
const MAX_LENGTH_DESCRIPTION = 256
export const ReportCommentScreen = ({ commentId }: { commentId: string }) => {
  const { t } = useTranslation()
  const { primaryLanguage } = useAppLanguage()
  const router = useRouter()
  const { reportCommentMutation, isReportCommentPending } = useReportComment()

  const formSchema = z
    .object({
      reason: z.string().min(1, { message: t('MES-926') }),
      description: z
        .string()
        .refine((val) => !val || val.trim().length <= MAX_LENGTH_DESCRIPTION, {
          message: t('MES-890', { length: MAX_LENGTH_DESCRIPTION }),
        })
        .optional(),
    })
    .refine(
      (data) =>
        data.reason !== CommentReportReasonsEnum.OTHER ||
        (data.description && data.description.trim().length > 0),
      {
        path: ['description'],
        message: t('MES-930') || 'Vui lòng nhập mô tả chi tiết',
      },
    )

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      reason: '',
      description: '',
    },
    mode: 'onTouched',
  })

  const handleSubmit = (values: z.infer<typeof formSchema>) => {
    const { reason, description } = values
    reportCommentMutation(
      {
        commentId,
        payload: {
          reason: reason as CommentReportReasonsEnum,
          description: reason === CommentReportReasonsEnum.OTHER ? description?.trim() : null,
        },
        options: {
          params: {
            locale: primaryLanguage,
          },
        },
      },
      {
        onSuccess: () => {
          Toast.show({
            type: 'success',
            text1: t('MES-927') || 'Báo cáo thành công',
          })
          if (router.canGoBack()) {
            router.back()
          } else {
            router.replace({
              pathname: APP_ROUTES.HOME.path,
            } as LinkProps['href'])
          }
        },
        onError: (error: any) => {
          console.log('error', error)
          Toast.show({
            type: 'error',
            text1: t(error?.message) || t('MES-197') || 'Có lỗi xảy ra, vui lòng thử lại',
          })

          if (router.canGoBack()) {
            router.back()
          } else {
            router.replace({
              pathname: APP_ROUTES.HOME.path,
            } as LinkProps['href'])
          }
        },
      },
    )
  }

  const selectedReason = form.watch('reason')
  const isOtherSelected = selectedReason === CommentReportReasonsEnum.OTHER

  const [buttonHeight, setButtonHeight] = useState(0)
  const handleButtonLayout = (event: LayoutChangeEvent) => {
    setButtonHeight(event.nativeEvent.layout.height)
  }
  const insets = useSafeAreaInsets()

  return (
    <SafeAreaView className="flex-1 bg-white" edges={['left', 'right']}>
      <KeyboardAvoidingView
        behavior={'padding'}
        className="flex-1"
        keyboardVerticalOffset={buttonHeight + insets.bottom}
      >
        <Form {...form}>
          <ScrollView
            className="flex flex-1 flex-col"
            automaticallyAdjustContentInsets
            showsVerticalScrollIndicator={false}
          >
            <View className="flex flex-1 flex-col gap-y-2">
              <View className="flex flex-1 flex-col gap-y-4">
                <View className="px-4 pt-4">
                  <Text size="body3">{t('MES-926')}</Text>
                </View>

                <View>
                  <FormField
                    control={form.control}
                    name="reason"
                    render={({ field }) => (
                      <FormItem className="flex flex-col gap-1.5 px-4">
                        <FormControl>
                          <RadioGroup
                            value={field.value}
                            onChange={field.onChange}
                            className="flex flex-col"
                            isDisabled={isReportCommentPending}
                          >
                            {Object.values(COMMENT_REPORT_REASON_OPTIONS).map((option) => {
                              return (
                                <Radio
                                  key={option.value}
                                  value={option.value}
                                  isDisabled={isReportCommentPending}
                                  className={cn(
                                    'flex-row items-center justify-between  px-4 py-3',
                                    selectedReason === option.value && 'rounded-lg bg-primary-50',
                                  )}
                                >
                                  <CustomRadioIndicator isSelected={field.value === option.value} />
                                  <RadioLabel className="ml-2 flex-1">
                                    <Text size="body6">{t(option.translationKey)}</Text>
                                  </RadioLabel>
                                </Radio>
                              )
                            })}
                          </RadioGroup>
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  {isOtherSelected && (
                    <View className="px-4 py-3">
                      <FormField
                        control={form.control}
                        name="description"
                        render={({ field }) => (
                          <FormItem className="flex flex-col gap-1.5">
                            <FormLabel required>{t('MES-929')}</FormLabel>
                            <FormControl>
                              <TextInput
                                placeholder={t('MES-930')}
                                onChangeText={field.onChange}
                                onBlur={field.onBlur}
                                value={field.value}
                                multiline
                                editable={!isReportCommentPending}
                                inputAccessoryViewID={`reportCommentInputAccessory_${commentId}`}
                                style={{ maxHeight: 120, minHeight: 40 }}
                              />
                            </FormControl>
                            <FormMessage />
                            <View className={cn('mt-2 ')}>
                              <Text
                                size="body7"
                                variant={
                                  field.value?.trim().length &&
                                  field.value?.trim().length > MAX_LENGTH_DESCRIPTION
                                    ? 'error'
                                    : 'subdued'
                                }
                              >
                                {field.value?.trim().length || 0}/{MAX_LENGTH_DESCRIPTION}
                              </Text>
                            </View>
                          </FormItem>
                        )}
                      />
                    </View>
                  )}
                </View>
              </View>
            </View>
          </ScrollView>
          <View
            className="px-4 py-4"
            style={{
              borderTopLeftRadius: 16,
              borderTopRightRadius: 16,
              boxShadow: '0px 4px 22px 0px #00000026',
              paddingBottom: Platform.OS === 'android' ? insets.bottom + 16 : 16,
            }}
            onLayout={handleButtonLayout}
          >
            <TouchableOpacity
              className={cn(
                'items-center justify-center rounded-lg bg-primary-500 px-4 py-3',
                isReportCommentPending && 'opacity-50',
              )}
              onPress={async () => {
                form.handleSubmit(handleSubmit)()
              }}
              disabled={isReportCommentPending}
            >
              <Text size="body6" variant="white">
                {isReportCommentPending
                  ? t('MES-855') || 'Đang gửi...'
                  : t('MES-932') || 'Gửi báo cáo'}
              </Text>
            </TouchableOpacity>
          </View>
        </Form>
      </KeyboardAvoidingView>
    </SafeAreaView>
  )
}
