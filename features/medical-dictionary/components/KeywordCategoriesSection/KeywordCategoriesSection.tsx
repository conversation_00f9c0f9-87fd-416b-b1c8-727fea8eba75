import { Text } from '@/components/ui/Text/Text'
import { KEYWORD_CATEGORY_OPTIONS } from '@/constants/keyword.constant'
import { APP_ROUTES } from '@/routes/appRoutes'
import { cn } from '@/utils/cn'
import * as Haptics from 'expo-haptics'
import { Link } from 'expo-router'
import React, { useMemo, useRef, useState } from 'react'
import { useTranslation } from 'react-i18next'
import { TouchableOpacity, View } from 'react-native'
import Svg, { Path } from 'react-native-svg'

interface KeywordCategoriesSectionProps {
  activeCategory?: string
  onPressCategory?: (category: string) => void
  isShowTitle?: boolean
  mode?: 'collapse' | 'full'
  enableRepositioning?: boolean
  defaultItemsToShow?: number
}

export const KeywordCategoriesSection = ({
  activeCategory,
  onPressCategory,
  isShowTitle = true,
  mode = 'full',
  enableRepositioning = false,
  defaultItemsToShow = 4,
}: KeywordCategoriesSectionProps) => {
  const { t } = useTranslation()
  const [isExpanded, setIsExpanded] = useState(false)
  const hasRepositionedRef = useRef(false)
  const initialActiveCategoryRef = useRef<string | undefined>(activeCategory)

  // Reposition active category to first position (only once on initial load)
  const repositionedOptions = useMemo(() => {
    const options = Object.values(KEYWORD_CATEGORY_OPTIONS)

    if (!enableRepositioning || !initialActiveCategoryRef.current) {
      return options
    }

    // If we've already repositioned, don't do it again
    if (hasRepositionedRef.current) {
      return options
    }

    const activeIndex = options.findIndex(
      (option) => option.value === initialActiveCategoryRef.current,
    )
    if (activeIndex === -1 || activeIndex === 0) {
      // Mark as repositioned even if no actual repositioning needed
      hasRepositionedRef.current = true
      return options
    }

    // Move active category to first position
    const reordered = [...options]
    const [activeOption] = reordered.splice(activeIndex, 1)
    reordered.unshift(activeOption)

    // Mark as repositioned after successful repositioning
    hasRepositionedRef.current = true

    return reordered
  }, [enableRepositioning])

  // Determine which options to show based on mode and expansion state
  const visibleOptions = useMemo(() => {
    if (mode === 'full') {
      return repositionedOptions
    }

    // In collapse mode, show first 4 items by default, or all if expanded
    return isExpanded ? repositionedOptions : repositionedOptions.slice(0, defaultItemsToShow)
  }, [repositionedOptions, mode, isExpanded])

  // Group items into pairs
  const groupedOptions: Array<
    Array<(typeof KEYWORD_CATEGORY_OPTIONS)[keyof typeof KEYWORD_CATEGORY_OPTIONS] | null>
  > = []

  for (let i = 0; i < visibleOptions.length; i += 2) {
    const row = visibleOptions.slice(i, i + 2) as Array<(typeof visibleOptions)[0] | null>
    // Add placeholder if row has only one item (odd total count)
    if (row.length === 1) {
      row.push(null)
    }
    groupedOptions.push(row)
  }

  const handleToggleExpansion = () => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light)
    setIsExpanded(!isExpanded)
  }

  return (
    <View className="flex flex-col gap-y-3">
      {isShowTitle && (
        <Text size="body3" variant="default">
          {t('MES-759')}
        </Text>
      )}
      <View className="flex flex-col gap-2">
        {groupedOptions.map((row, rowIndex) => (
          <View key={rowIndex} className="flex flex-row gap-2">
            {row.map((option, optionIndex) => {
              if (!option)
                return (
                  <View
                    key={`blank-${rowIndex}-${optionIndex}`}
                    className={cn(
                      ' max-w-1/2 flex-1 basis-1/2 flex-row items-center gap-x-3 overflow-hidden rounded-[6px] px-3 py-2',
                    )}
                  />
                )
              return onPressCategory ? (
                // Local state change (for categories screen)
                <TouchableOpacity
                  key={option.value}
                  onPress={() => {
                    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light)
                    onPressCategory?.(option.value)
                  }}
                  className={cn(
                    ' max-w-1/2 flex-1 basis-1/2 flex-row items-center gap-x-3 overflow-hidden rounded-[6px] px-3 py-2',
                    activeCategory === option.value
                      ? 'border border-primary-500 bg-primary-50'
                      : 'border border-transparent bg-[#F8F8FC] ',
                  )}
                  style={{
                    height: 60,
                  }}
                >
                  <option.icon className="size-[30px] shrink-0" width={30} height={30} />
                  <View className="flex-1">
                    <Text
                      size="body6"
                      variant={activeCategory === option.value ? 'primary' : 'default'}
                      numberOfLines={2}
                      className="line-clamp-2"
                    >
                      {t(option.translationKey)}
                    </Text>
                  </View>
                </TouchableOpacity>
              ) : (
                // Navigation (for main medical dictionary screen)
                <Link
                  key={option.value}
                  href={{
                    pathname:
                      APP_ROUTES.MEDICAL_DICTIONARY.children?.['MEDICAL_DICTIONARY_CATEGORIES']
                        ?.path,
                    params: {
                      category: option.value,
                    },
                  }}
                  asChild
                  className={cn(
                    ' max-w-1/2 flex-1 basis-1/2 flex-row items-center gap-x-3 overflow-hidden rounded-[6px] px-3 py-2',
                    activeCategory === option.value
                      ? 'border border-primary-500 bg-primary-50'
                      : 'border border-transparent bg-[#F8F8FC] ',
                  )}
                  style={{
                    height: 60,
                  }}
                  onPress={() => Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light)}
                >
                  <TouchableOpacity>
                    <option.icon className="size-[30px] shrink-0" width={30} height={30} />
                    <View className="flex-1">
                      <Text
                        size="body6"
                        variant={activeCategory === option.value ? 'primary' : 'default'}
                        numberOfLines={2}
                        className="line-clamp-2"
                      >
                        {t(option.translationKey)}
                      </Text>
                    </View>
                  </TouchableOpacity>
                </Link>
              )
            })}
          </View>
        ))}

        {/* Show/Hide Button for collapse mode */}
        {mode === 'collapse' && repositionedOptions.length > defaultItemsToShow && (
          <TouchableOpacity
            onPress={handleToggleExpansion}
            className="mx-auto  mt-2 flex-row items-center justify-center self-start px-3 py-2"
            style={{ height: 40 }}
          >
            <Text size="body6" variant="primary">
              {!isExpanded
                ? t('MES-746', { number: repositionedOptions.length })
                : t('MES-745', { number: repositionedOptions.length })}
            </Text>
            <View
              className={cn('transition-transform duration-300')}
              style={{
                transform: [{ rotate: isExpanded ? '0deg' : '180deg' }],
              }}
            >
              <Svg width={16} height={16} viewBox="0 0 16 16" fill="none">
                <Path
                  d="M12.3996 9.69452L8.77739 6.0723C8.34961 5.64452 7.64961 5.64452 7.22183 6.0723L3.59961 9.69452"
                  stroke="#1157C8"
                  strokeWidth={1.5}
                  strokeMiterlimit={10}
                  strokeLinecap="round"
                  strokeLinejoin="round"
                />
              </Svg>
            </View>
          </TouchableOpacity>
        )}
      </View>
    </View>
  )
}
