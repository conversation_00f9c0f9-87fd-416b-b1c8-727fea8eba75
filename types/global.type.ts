import { LocaleEnum } from '@/enums/locale.enum'
import { SvgProps } from 'react-native-svg'

export type PaginatedDocs<T = any> = {
  docs: T[]
  hasNextPage: boolean
  hasPrevPage: boolean
  limit: number
  nextPage?: null | number | undefined
  // Optional cursor-based pagination fields (for APIs that use cursors)
  nextCursor?: string | null
  prevCursor?: string | null
  cursor?: string | null
  page?: number
  pagingCounter: number
  prevPage?: null | number | undefined
  totalDocs: number
  totalPages: number
}
export type LocalizeField<T = unknown> = {
  [K in LocaleEnum]: T
}

export interface SVGIconProps extends SvgProps {}
export interface ReactNativeFile {
  uri: string
  name: string
  type: string
}
