import FilterIcon from '@/assets/icons/filter-icon.svg'
import { SearchInput, SearchInputRef } from '@/components/ui/SearchInput/SearchInput'
import { useRef, useState } from 'react'
import { useTranslation } from 'react-i18next'
import { Keyboard, TouchableOpacity, View } from 'react-native'
import { useShallow } from 'zustand/react/shallow'
import { useFavoriteProductStore } from '../../stores/FavoriteProductStore'
import { useOpenFilterSearchBox } from './FilterBox/FilterSearchBox'

export const FavoriteProductHeader = () => {
  const { t } = useTranslation()
  const { hasActiveFilters, setFavoriteSearchTextValue } = useFavoriteProductStore(
    useShallow((state) => ({
      hasActiveFilters: state.hasActiveFilters,
      setFavoriteSearchTextValue: state.setFavoriteSearchTextValue,
    })),
  )
  const [searchInputValue, setSearchInputValue] = useState('')
  const searchInputRef = useRef<SearchInputRef>(null)
  const { handleOpenFilterSearchBox } = useOpenFilterSearchBox()

  const handleSearchInputChange = (text: string) => {
    setSearchInputValue(text)
  }

  const handleClearSearchInput = () => {
    setSearchInputValue('')
    setFavoriteSearchTextValue('')
  }

  return (
    <View className="flex flex-col gap-y-2">
      <View className="flex flex-row items-center gap-x-2">
        <SearchInput
          ref={searchInputRef}
          placeholder={t('MES-66')}
          value={searchInputValue}
          onChangeText={handleSearchInputChange}
          onClear={handleClearSearchInput}
          onSubmitEditing={() => {
            setFavoriteSearchTextValue(searchInputValue)
          }}
        />
        <TouchableOpacity
          hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
          onPress={() => {
            Keyboard.dismiss()
            handleOpenFilterSearchBox()
          }}
          className="relative flex h-full flex-row items-center gap-x-2 rounded-lg border border-custom-divider-border p-3"
        >
          <FilterIcon width={18} height={18} />
          {hasActiveFilters && (
            <View className="absolute -right-1 -top-1 size-4 rounded-full bg-custom-danger-600/80" />
          )}
        </TouchableOpacity>
      </View>
    </View>
  )
}
