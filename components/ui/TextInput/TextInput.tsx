import { text } from '@/styles/_colors'
import { cn } from '@/utils/cn'

import React from 'react'
import { TextInput as RNTextInput, TextInputProps as RNTextInputProps, View } from 'react-native'

export interface TextInputProps extends RNTextInputProps {
  isError?: boolean
  wrapperClassName?: string
}

export const TextInput = React.forwardRef<RNTextInput, TextInputProps>(
  ({ className, wrapperClassName, isError, editable = true, multiline, style, ...props }, ref) => {
    return (
      <View
        className={cn(
          'flex w-full flex-row rounded-lg border border-custom-neutral-100 bg-white px-3 ',
          multiline ? 'min-h-11 items-center' : 'h-11 items-center',
          isError && 'border-custom-danger-600',
          !editable && 'opacity-50',
          wrapperClassName,
        )}
      >
        <RNTextInput
          ref={ref}
          className={cn('flex-1 py-2 ', className)}
          style={[
            // multiline ? { minHeight: 40, maxHeight: 96 } : { height: 40 },
            ...(Array.isArray(style) ? style : [style]),
          ]}
          placeholderTextColor={text.subdued}
          editable={editable}
          multiline={multiline}
          scrollEnabled={multiline}
          // textAlignVertical={multiline ? 'top' : 'center'}

          {...props}
        />
      </View>
    )
  },
)

TextInput.displayName = 'TextInput'
