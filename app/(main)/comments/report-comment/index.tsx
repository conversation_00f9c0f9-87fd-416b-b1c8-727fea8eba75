import { ReportCommentScreen } from '@/features/comments/screens/ReportCommentScreen/ReportCommentScreen'
import { useLocalSearchParams } from 'expo-router'

export default function ReportCommentAppScreen() {
  const { commentId } = useLocalSearchParams()
  return (
    <>
      {/* <StackScreenBase
        options={{
          headerTitle: t('MES-920'),
          headerShown: true,
        }}
      /> */}
      <ReportCommentScreen commentId={commentId as string} />
    </>
  )
}
