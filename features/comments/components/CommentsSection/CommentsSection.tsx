import ArrowDownIcon from '@/assets/icons/arrow-down-primary.svg'
import { Skeleton } from '@/components/ui/Skeleton/Skeleton'
import { Text } from '@/components/ui/Text/Text'
import { useAuthentication } from '@/hooks/auth/useAuthentication'
import { APP_ROUTES } from '@/routes/appRoutes'
import { Params } from '@/types/http.type'
import { User } from '@/types/user.type'
import { useFocusEffect } from '@react-navigation/native'
import { Link, LinkProps } from 'expo-router'
import React, { useCallback, useEffect } from 'react'
import { useTranslation } from 'react-i18next'
import { TouchableOpacity, View } from 'react-native'
import { useCommentManagement } from '../../hooks/common/useCommentManagement'
import { CommentQueryParams } from '../../services/commentCacheManager.service'
import { Comment } from '../../types'
import { CommentEntryBox } from '../CommentEntryBox/CommentEntryBox'
import { BaseCommentInputRef } from '../CommentInput'
import { CommentInput } from '../CommentInput/CommentInput'
import { CommentItem } from '../CommentItem/CommentItem'
interface CommentsSectionProps {
  id: string
  relationTo: Comment['commentedOn']['relationTo']
  customMainParams?: CommentQueryParams
  customNestedParams?: CommentQueryParams
  isRefreshing?: boolean
  customTitle?: string
  commentInputRef?: React.RefObject<BaseCommentInputRef>
  handleAddCommentPress?: () => void
  handleReplyPress?: (comment: Comment) => void
  handleCommentSubmit?: (content: string) => void
  handleCommentCancel?: () => void
  handleInputContentChange?: (content: string) => void
  handleCancelReply?: () => void
  handleEditComment?: (commentId: string, content: string) => void
  handleStartEdit?: (commentId: string) => void
  handleCancelEdit?: () => void
  handleDeleteComment?: (commentId: string) => void
  handleRetryAddComment?: (content: string, parentId?: string) => void
  isAddCommentPending?: boolean
  isEditCommentPending?: boolean
  editingCommentId?: string | null
  replyingTo?: Comment | null
  currentInputContent?: string
  allComments?: Comment[]
  totalComments?: number
  isGetCommentsLoading?: boolean
  isGetCommentsFetching?: boolean
  cacheManager?: any
  mainParams?: Params
  nestedParams?: Params
}

export const CommentsSection = ({
  id,
  relationTo,
  customMainParams,
  customNestedParams,
  isRefreshing,
  customTitle,
  commentInputRef: externalCommentInputRef,
  handleAddCommentPress: externalHandleAddCommentPress,
  handleReplyPress: externalHandleReplyPress,
  handleCommentSubmit: externalHandleCommentSubmit,
  handleCommentCancel: externalHandleCommentCancel,
  handleInputContentChange: externalHandleInputContentChange,
  handleCancelReply: externalHandleCancelReply,
  handleEditComment: externalHandleEditComment,
  handleStartEdit: externalHandleStartEdit,
  handleCancelEdit: externalHandleCancelEdit,
  handleDeleteComment: externalHandleDeleteComment,
  handleRetryAddComment: externalHandleRetryAddComment,
  isAddCommentPending: externalIsAddCommentPending,
  isEditCommentPending: externalIsEditCommentPending,
  editingCommentId: externalEditingCommentId,
  replyingTo: externalReplyingTo,
  // currentInputContent: externalCurrentInputContent,
  allComments: externalAllComments,
  totalComments: externalTotalComments,
  isGetCommentsLoading: externalIsGetCommentsLoading,
  isGetCommentsFetching: externalIsGetCommentsFetching,
  cacheManager: externalCacheManager,
  mainParams: externalMainParams,
  nestedParams: externalNestedParams,
}: CommentsSectionProps) => {
  const { user, status } = useAuthentication()
  const { t } = useTranslation()
  // Only use local comment management if no external handlers are provided

  const {
    allComments: localAllComments,
    totalComments: localTotalComments,
    isGetCommentsLoading: localIsGetCommentsLoading,
    isGetCommentsFetching: localIsGetCommentsFetching,
    replyingTo: localReplyingTo,
    commentInputRef,
    isAddCommentPending: localIsAddCommentPending,
    handleAddCommentPress: localHandleAddCommentPress,
    handleReplyPress: localHandleReplyPress,
    handleCommentSubmit: localHandleCommentSubmit,
    handleCommentCancel: localHandleCommentCancel,
    handleInputContentChange: localHandleInputContentChange,
    handleCancelReply: localHandleCancelReply,
    handleEditComment: localHandleEditComment,
    handleStartEdit: localHandleStartEdit,
    handleCancelEdit: localHandleCancelEdit,
    handleDeleteComment: localHandleDeleteComment,
    handleRetryAddComment: localHandleRetryAddComment,
    isEditCommentPending: localIsEditCommentPending,
    editingCommentId: localEditingCommentId,
    invalidateQueries,
    queryKeys,
    cacheManager: localCacheManager,
    mainParams: localMainParams,
    nestedParams: localNestedParams,
    totalCommentsQueryKeys,
    // currentInputContent: localCurrentInputContent,
    showCommentInput: localShowCommentInput,
  } = useCommentManagement({
    id,
    relationTo,
    customMainParams,
    customNestedParams,
    externalCommentInputRef,
  })

  // Use external data/handlers if provided, otherwise use local ones
  const handleAddCommentPress = externalHandleAddCommentPress ?? localHandleAddCommentPress
  const handleReplyPress = externalHandleReplyPress ?? localHandleReplyPress
  const handleCommentSubmit = externalHandleCommentSubmit ?? localHandleCommentSubmit
  const handleCommentCancel = externalHandleCommentCancel ?? localHandleCommentCancel
  const handleInputContentChange = externalHandleInputContentChange ?? localHandleInputContentChange
  const handleCancelReply = externalHandleCancelReply ?? localHandleCancelReply
  const handleEditComment = externalHandleEditComment ?? localHandleEditComment
  const handleStartEdit = externalHandleStartEdit ?? localHandleStartEdit
  const handleCancelEdit = externalHandleCancelEdit ?? localHandleCancelEdit
  const handleDeleteComment = externalHandleDeleteComment ?? localHandleDeleteComment
  const handleRetryAddComment = externalHandleRetryAddComment ?? localHandleRetryAddComment
  const isAddCommentPending = externalIsAddCommentPending ?? localIsAddCommentPending
  const isEditCommentPending = externalIsEditCommentPending ?? localIsEditCommentPending
  const editingCommentId = externalEditingCommentId ?? localEditingCommentId
  const replyingTo = externalReplyingTo ?? localReplyingTo

  const showCommentInput = localShowCommentInput

  // Use external data if provided, otherwise use local data
  const allComments = externalAllComments ?? localAllComments
  const totalComments = externalTotalComments ?? localTotalComments
  const isGetCommentsLoading = externalIsGetCommentsLoading ?? localIsGetCommentsLoading
  const isGetCommentsFetching = externalIsGetCommentsFetching ?? localIsGetCommentsFetching
  const cacheManager = externalCacheManager ?? localCacheManager
  const mainParams = externalMainParams ?? localMainParams
  const nestedParams = externalNestedParams ?? localNestedParams

  useEffect(() => {
    if (isRefreshing) {
      invalidateQueries()
    }
  }, [isRefreshing, invalidateQueries])

  useFocusEffect(
    useCallback(() => {
      commentInputRef.current?.blur()
    }, []),
  )
  if (isGetCommentsLoading || isGetCommentsFetching) {
    return <CommentsSectionLoading />
  }
  return (
    <>
      <View className="overflow-hidden ">
        <View className="flex flex-col gap-y-3 overflow-hidden px-4">
          <Text size="body3">
            {t('MES-845')} ({totalComments})
          </Text>
          {user && status === 'success' && (
            <CommentEntryBox onPress={handleAddCommentPress}></CommentEntryBox>
          )}

          <View className="flex flex-col gap-y-2">
            <View className="flex flex-col gap-y-3">
              {allComments?.map((comment, index) => {
                if (typeof comment === 'string' || !comment?.id) return null

                const isLastComment = index === allComments.length - 1

                return (
                  <View key={comment.id + index}>
                    <CommentItem
                      comment={comment}
                      relationTo={relationTo}
                      onReply={handleReplyPress}
                      isLastComment={isLastComment}
                      onEditComment={handleEditComment}
                      onStartEdit={handleStartEdit}
                      onCancelEdit={handleCancelEdit}
                      onDeleteComment={handleDeleteComment}
                      onRetryAddComment={handleRetryAddComment}
                      isEditCommentPending={isEditCommentPending}
                      editingCommentId={editingCommentId}
                      cacheManager={cacheManager}
                      mainParams={mainParams}
                      nestedParams={nestedParams}
                    />
                  </View>
                )
              })}
            </View>

            {totalComments > 0 && allComments.length > 0 ? (
              <Link
                href={
                  {
                    pathname: APP_ROUTES.COMMENTS.path,
                    params: {
                      id: id as string,
                      relationTo: 'keywords',
                      queryKeys: JSON.stringify(queryKeys),
                      customTitle: customTitle || '',
                      totalCommentsQueryKeys: JSON.stringify(totalCommentsQueryKeys),
                    },
                  } as LinkProps['href']
                }
                asChild
                className="mx-auto flex flex-row items-center justify-center gap-x-2 self-start"
              >
                <TouchableOpacity>
                  <Text size="body6" variant="primary">
                    {t('MES-850')}
                  </Text>
                  <ArrowDownIcon />
                </TouchableOpacity>
              </Link>
            ) : (
              <Text size="body6" variant="subdued">
                {t('MES-938')}
              </Text>
            )}
          </View>
        </View>
      </View>
      {/* Unified input - shows only when adding/replying, hides on blur */}
      {(showCommentInput || replyingTo) && !editingCommentId && (
        <CommentInput
          ref={commentInputRef}
          onSubmit={handleCommentSubmit}
          onCancel={handleCommentCancel}
          isLoading={isAddCommentPending}
          placeholder={t('MES-887')}
          replyingTo={replyingTo ? (replyingTo?.author as User)?.name || 'Hico User' : undefined}
          instanceId="section"
          onContentChange={handleInputContentChange}
          onCancelReply={handleCancelReply}
          onBlur={handleCommentCancel}
        />
      )}
    </>
  )
}

const CommentsSectionLoading = () => {
  return (
    <View className="flex flex-col gap-y-3 px-4">
      {new Array(3).fill(0).map((_, index) => (
        <View key={index} className="flex flex-row gap-x-3">
          <Skeleton className="h-12 w-12 rounded-full" />
          <View className="flex flex-1 flex-col gap-y-2">
            <Skeleton className="h-8 w-[120px]" />
            <Skeleton className="h-12 w-full" />
          </View>
        </View>
      ))}
    </View>
  )
}
