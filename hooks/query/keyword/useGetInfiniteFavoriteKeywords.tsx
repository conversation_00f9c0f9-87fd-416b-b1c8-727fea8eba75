import { Params } from '@/types/http.type'
import { InfiniteData, UseInfiniteQueryOptions, useInfiniteQuery } from '@tanstack/react-query'

import { keywordService } from '@/services/handbook/keyword.service'
import { PaginatedDocs } from '@/types/global.type'
import { FavoriteKeyword } from '@/types/keyword.type'
import { AxiosRequestConfig } from 'axios'
import { keywordQueryKeys } from './queryKeys'

export type FavoriteKeywordsQueryConfig = Omit<
  UseInfiniteQueryOptions<
    PaginatedDocs<FavoriteKeyword>,
    Error,
    InfiniteData<PaginatedDocs<FavoriteKeyword>>,
    (string | Params)[],
    number
  >,
  'queryFn' | 'queryKey' | 'getNextPageParam' | 'initialPageParam'
>

interface UseGetInfiniteFavoriteKeywordsProps {
  params?: Params
  options?: AxiosRequestConfig
  key?: string | number
  config?: FavoriteKeywordsQueryConfig
  overrideKey?: (string | Params)[]
}

export const useGetInfiniteFavoriteKeywords = ({
  params = {},
  options = {},
  config = {},
  overrideKey,
}: UseGetInfiniteFavoriteKeywordsProps = {}) => {
  const {
    isError: isGetFavoriteKeywordsError,
    isFetching: isGetFavoriteKeywordsLoading,
    data: favoriteKeywords,
    fetchNextPage,
    hasNextPage,
    ...rest
  } = useInfiniteQuery({
    queryKey: overrideKey
      ? overrideKey
      : [keywordQueryKeys['favorite-keywords-by-user'].base(), params],
    queryFn: async ({ pageParam = 1 }) => {
      return keywordService.getFavoriteKeywordsByUser({
        params: {
          ...params,
          page: pageParam,
        },
        options,
      })
    },
    getNextPageParam: (lastPage) => lastPage?.nextPage,
    getPreviousPageParam: (lastPage) => lastPage?.prevPage,
    initialPageParam: 1,
    ...config,
  })

  return {
    isGetFavoriteKeywordsError,
    isGetFavoriteKeywordsLoading,
    favoriteKeywords,
    fetchNextPage,
    hasNextPage,
    ...rest,
  }
}
