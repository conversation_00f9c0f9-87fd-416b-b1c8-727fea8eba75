import DoubleBookmarkIcon from '@/assets/icons/double-bookmark-icon.svg'
import SearchInputIcon from '@/assets/icons/search-input-icon.svg'
import { Text } from '@/components/ui/Text/Text'
import { useAuthentication } from '@/hooks/auth/useAuthentication'
import { APP_ROUTES, AppRoutesEnum } from '@/routes/appRoutes'
import { LinearGradient } from 'expo-linear-gradient'
import { Link } from 'expo-router'
import { useTranslation } from 'react-i18next'
import { TouchableOpacity, View } from 'react-native'
export const MedicalDictionaryHeader = () => {
  const { t } = useTranslation()
  const { user, status } = useAuthentication()
  return (
    <View className="flex flex-col gap-y-4 pt-5">
      <View className="flex flex-row items-center justify-between gap-x-2">
        <Text size="heading7" variant="primary">
          {t('MES-756')}
        </Text>

        {user && status === 'success' && (
          <Link
            href={
              APP_ROUTES.MEDICAL_DICTIONARY?.children?.[
                AppRoutesEnum.MEDICAL_DICTIONARY_FAVORITE_KEYWORDS
              ]?.path
            }
            asChild
          >
            <TouchableOpacity className="h-9">
              <LinearGradient
                colors={['#5BAEF3', '#1764E0']}
                start={{ x: 0, y: 1 }}
                end={{ x: 0, y: 0 }}
                style={{
                  borderRadius: 99,
                  flex: 1,

                  paddingHorizontal: 12,
                  paddingVertical: 8,
                  flexDirection: 'row',
                  alignItems: 'center',
                  gap: 4,
                }}
              >
                <DoubleBookmarkIcon width={18} height={18} />
                <Text size="body8" variant="white">
                  {t('MES-773')}
                </Text>
              </LinearGradient>
            </TouchableOpacity>
          </Link>
        )}
      </View>
      {/* Search Bar */}
      <Link
        href={
          APP_ROUTES.MEDICAL_DICTIONARY?.children?.[AppRoutesEnum.MEDICAL_DICTIONARY_SEARCH]?.path
        }
        asChild
      >
        <TouchableOpacity className="flex-row items-center gap-2 overflow-hidden rounded-lg border border-custom-divider bg-white p-3">
          <SearchInputIcon width={18} height={18} />

          <View className="flex-1 overflow-hidden">
            <Text className="line-clamp-1" size="field1" variant="subdued">
              {t('MES-757')}
            </Text>
          </View>
        </TouchableOpacity>
      </Link>
    </View>
  )
}
