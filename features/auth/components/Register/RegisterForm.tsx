import { Button } from '@/components/ui/Button/Button'
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/FormField/FormField'
import { PasswordInput } from '@/components/ui/PasswordInput/PasswordInput'
import { Text } from '@/components/ui/Text/Text'
import { TextInput } from '@/components/ui/TextInput/TextInput'
import { useAppLanguage } from '@/hooks/common/useAppLanguage'
import { useLoadingScreen } from '@/hooks/common/useLoadingScreen'
import { APP_ROUTES } from '@/routes/appRoutes'
import { authService } from '@/services/auth/auth.service'
import { zodResolver } from '@hookform/resolvers/zod'
import { LinkProps, router } from 'expo-router'
import React, { useEffect } from 'react'
import { useForm } from 'react-hook-form'
import { useTranslation } from 'react-i18next'
import { View } from 'react-native'
import Toast from 'react-native-toast-message'
import { z } from 'zod'
export const RegisterForm = () => {
  const { t } = useTranslation()
  const { primaryLanguage } = useAppLanguage()
  const { showLoading, hideLoading, isLoading } = useLoadingScreen()
  const formSchema = z
    .object({
      email: z.email({ message: t('MES-225') }).min(1, { message: t('MES-523') }),
      name: z.string().min(1, { message: t('MES-523') }),
      password: z.string().min(8, {
        message: t('MES-178'),
      }),
      confirmPassword: z.string().min(1, {
        message: t('MES-179'),
      }),
    })
    .refine((data) => data.password === data.confirmPassword, {
      message: t('MES-117'),
      path: ['confirmPassword'],
    })

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      email: '',
      name: '',
      password: '',
      confirmPassword: '',
    },
    mode: 'onTouched',
  })

  const handleRegister = async (values: z.infer<typeof formSchema>) => {
    const { email, password, name } = values || {}

    showLoading()
    try {
      await authService.signup({ email, password, name })
      Toast.show({
        type: 'success',
        text1: t('MES-185'),
        text2: t('MES-186'),
      })
      router.push({
        pathname: APP_ROUTES.VERIFY_EMAIL.path,
        params: {
          email,
        },
      } as LinkProps['href'])
    } catch (error: any) {
      Toast.show({
        type: 'error',
        text1: t('MES-203'),
        text2: error?.message,
      })
      // console.log(error)
    } finally {
      hideLoading()
    }
  }

  useEffect(() => {
    //  Re-trigger to update error message when language changed
    if (!form.formState.isValid && (form.formState.isDirty || form.formState.isSubmitted)) {
      form.trigger()
    }
  }, [primaryLanguage])

  return (
    <Form {...form}>
      <View className="flex w-full flex-col gap-y-4">
        <FormField
          control={form.control}
          name="name"
          render={({ field }) => (
            <FormItem className="w-full">
              <View className="flex w-full flex-col gap-y-1.5">
                <FormLabel required>{t('MES-91')}</FormLabel>
                <FormControl>
                  <View className="relative w-full">
                    <TextInput
                      placeholder={t('MES-648')}
                      autoCapitalize="none"
                      keyboardType="default"
                      onChangeText={field.onChange}
                      onBlur={field.onBlur}
                      value={field.value}

                      // isError={!!form.formState.errors.password}
                    />
                  </View>
                </FormControl>
                <FormMessage />
              </View>
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="email"
          render={({ field }) => (
            <FormItem className="w-full">
              <View className="flex w-full flex-col gap-y-1.5">
                <FormLabel required>Email</FormLabel>
                <FormControl>
                  <View className="relative w-full">
                    <TextInput
                      placeholder={t('MES-120')}
                      autoCapitalize="none"
                      keyboardType="email-address"
                      onChangeText={field.onChange}
                      onBlur={field.onBlur}
                      value={field.value}

                      // isError={!!form.formState.errors.password}
                    />
                  </View>
                </FormControl>
                <FormMessage />
              </View>
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="password"
          render={({ field }) => (
            <FormItem className="w-full">
              <View className="flex w-full flex-col gap-y-1.5">
                <FormLabel required>{t('MES-03')}</FormLabel>
                <FormControl>
                  <View className="relative w-full">
                    <PasswordInput
                      className="flex-1"
                      placeholder={t('MES-161')}
                      onChangeText={field.onChange}
                      onBlur={field.onBlur}
                      value={field.value}
                    />
                  </View>
                </FormControl>
                <FormMessage />
              </View>
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="confirmPassword"
          render={({ field }) => (
            <FormItem className="w-full">
              <View className="flex w-full flex-col gap-y-1.5">
                <FormLabel required>{t('MES-119')}</FormLabel>
                <FormControl>
                  <View className="relative w-full">
                    <PasswordInput
                      className="flex-1"
                      placeholder={t('MES-116')}
                      onChangeText={field.onChange}
                      onBlur={field.onBlur}
                      value={field.value}
                    />
                  </View>
                </FormControl>
                <FormMessage />
              </View>
            </FormItem>
          )}
        />

        <Button
          className={`mt-3 min-h-12 w-full flex-row items-center justify-center gap-2 rounded-lg px-6 py-3 `}
          onPress={form.handleSubmit(handleRegister)}
          disabled={form.formState.isSubmitting || isLoading}
        >
          <Text size="body6" variant={'white'}>
            {t('MES-09')}
          </Text>
        </Button>
      </View>
    </Form>
  )
}
