import { AudioIcon } from '@/components/Icons/AudioIcon'
import { Text } from '@/components/ui/Text/Text'
import { LocaleEnum } from '@/enums/locale.enum'
import { useKeywordAudio } from '@/hooks/common/useKeywordAudio'
import { APP_ROUTES, AppRoutesEnum } from '@/routes/appRoutes'
import { primary } from '@/styles/_colors'
import { LocalizeField } from '@/types/global.type'
import { Keyword } from '@/types/keyword.type'
import * as Haptics from 'expo-haptics'
import { Link } from 'expo-router'
import React from 'react'
import { ActivityIndicator, TouchableOpacity, View } from 'react-native'

interface KeyworCardProps {
  keyword: Keyword
}

export default function KeyworCard({ keyword }: KeyworCardProps) {
  const { name, hiragana } = keyword
  const { handlePlayAudio, isAudioLoading, isPlaying } = useKeywordAudio({ keyword })

  const localizedName = name as unknown as LocalizeField<string>

  return (
    <Link
      href={{
        pathname:
          APP_ROUTES.MEDICAL_DICTIONARY?.children?.[
            AppRoutesEnum.MEDICAL_DICTIONARY_KEYWORD_DETAILS
          ]?.path,
        params: {
          id: keyword.id,
        },
      }}
      asChild
      onPress={() => {
        Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light)
      }}
    >
      <TouchableOpacity className="w-[300px] overflow-hidden rounded-[8px] bg-[#EDF5FF] px-3 py-4">
        <View className="w-full flex-row items-start justify-between gap-2">
          <View className="flex-1 flex-col gap-2">
            <View className="flex-row items-center justify-between gap-3">
              <Text size="body2" className="line-clamp-2 " numberOfLines={2}>
                {localizedName[LocaleEnum.JA]}
              </Text>
            </View>
            {hiragana && (
              <Text size="body7" variant="subdued" className="line-clamp-2" numberOfLines={2}>
                /{hiragana}/
              </Text>
            )}
            <Text size="body7" variant="default" className="line-clamp-2" numberOfLines={2}>
              {localizedName[LocaleEnum.VI]}
            </Text>
          </View>
          <View className="mt-1 shrink-0">
            {isAudioLoading ? (
              <ActivityIndicator size="small" color={primary[500]} />
            ) : (
              <TouchableOpacity
                className=" h-5 w-5  items-center justify-center p-0"
                onPress={(e) => {
                  e.stopPropagation()
                  Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light)
                  handlePlayAudio()
                }}
              >
                <AudioIcon width={20} height={20} isPlaying={isPlaying} />
              </TouchableOpacity>
            )}
          </View>
        </View>
      </TouchableOpacity>
    </Link>
  )
}
