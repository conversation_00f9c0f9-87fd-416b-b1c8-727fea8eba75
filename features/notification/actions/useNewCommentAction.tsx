import { useAppLanguage } from '@/hooks/common/useAppLanguage'
import { APP_ROUTES, AppRoutesEnum } from '@/routes/appRoutes'
import { LinkProps, useRouter } from 'expo-router'
import { useTranslation } from 'react-i18next'
import { Platform } from 'react-native'
import { NewCommentNotificationBaseData } from '../types'
import { parseDocName } from '../utils/parseDocName'

export const useNewCommentAction = () => {
  const { primaryLanguage } = useAppLanguage()
  const { t } = useTranslation()
  const router = useRouter()
  const handleRouting = (params: Record<string, string>) => {
    const path = APP_ROUTES.COMMENTS.children?.[AppRoutesEnum.COMMENTS_DETAILS].path
    if (Platform.OS === 'ios') {
      setTimeout(() => {
        router.push({
          pathname: path,
          params: params,
        } as LinkProps['href'])
      }, 1)
    } else {
      setImmediate(() => {
        router.push({
          pathname: path,
          params: params,
        } as LinkProps['href'])
      })
    }
  }
  const handleOpenNewCommentNoti = (data: NewCommentNotificationBaseData) => {
    const { relationTo, docSlug, documentId, commentId, docName, commentAuthorName } = data

    const parsedDocName = parseDocName(docName, primaryLanguage)
    const documentName = `"${parsedDocName}"`
    const getTitlte = () => {
      switch (relationTo) {
        case 'posts':
          return t('MES-940', { username: commentAuthorName, name: documentName })
        case 'products':
          return t('MES-941', { username: commentAuthorName, name: documentName })
        case 'keywords':
          return t('MES-942', { username: commentAuthorName, name: documentName })
        default:
          return ''
      }
    }
    // Handle routing for all supported relation types
    if (
      (relationTo === 'posts' || relationTo === 'products' || relationTo === 'keywords') &&
      commentId
    ) {
      handleRouting({
        nestedCommentId: commentId || '',
        commentId: commentId || '',
        relationTo,
        listLimit: '1',
        nestedLimit: '5',
        docSlug: docSlug || '',
        documentId: documentId || '',
        id: documentId || '',
        notiTitle: getTitlte(),
      })
    }
  }
  return {
    handleOpenNewCommentNoti,
  }
}
