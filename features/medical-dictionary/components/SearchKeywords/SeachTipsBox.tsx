import LightBulb from '@/assets/icons/light-bulb-v2-icon.svg'
import { Text } from '@/components/ui/Text/Text'
import { useTranslation } from 'react-i18next'
import { View } from 'react-native'
export const SeachTipsBox = () => {
  const { t } = useTranslation()
  return (
    <View className="flex flex-row items-start gap-x-2 rounded-[6px] bg-primary-50 p-3">
      <LightBulb></LightBulb>
      <View className="flex flex-1 flex-col gap-y-1 overflow-hidden">
        <Text size="body6" variant="primary">
          {t('MES-712')}
        </Text>
        <Text size="body9">{t('MES-769')}</Text>
      </View>
    </View>
  )
}
