import {
  BaseFilterSheetBox,
  type BaseFilterSheetBoxRef,
} from '@/components/Filter/BaseFilterSheetBox/BaseFilterSheetBox'
import { useSheetActions } from '@/contexts/SheetContext/SheetContext'
import { useAppLanguage } from '@/hooks/common/useAppLanguage'
import { BottomSheetScrollView } from '@gorhom/bottom-sheet'
import { useCallback, useEffect, useMemo, useRef } from 'react'
import { useTranslation } from 'react-i18next'
import { View } from 'react-native'
import { useShallow } from 'zustand/react/shallow'
import { ProductV2TypeEnum } from '../../enums'
import { useGetProductAgeGroups } from '../../hooks/query/product/useGetProductAgeGroups'
import { useGetProductMedicineTypes } from '../../hooks/query/product/useGetProductMedicineTypes'
import { ProductFilterType, useProductStore } from '../../stores/ProductStore'
import { ProductAgeGroup, ProductMedicineType } from '../../types'
import { FilterFooter } from './FilterFooter'

import { BaseFilterListSection } from '@/components/Filter/BaseFilterListSection/BaseFilterListSection'
import { MedicineTypeNotes } from './MedicineTypeNotes'

interface FilterProductBoxProps {
  productType: ProductV2TypeEnum
  closeSheet?: () => void
  sharedFooterRef: React.RefObject<View | null>
  sharedSheetBoxRef?: React.RefObject<BaseFilterSheetBoxRef | null>
}

export const FilterProductBox = ({
  productType,
  closeSheet,
  sharedFooterRef,
  sharedSheetBoxRef,
}: FilterProductBoxProps) => {
  const { primaryLanguage } = useAppLanguage()
  const { t } = useTranslation()
  const localSheetBoxRef = useRef<BaseFilterSheetBoxRef>(null)
  const sheetBoxRef = sharedSheetBoxRef || localSheetBoxRef

  const { productAgeGroups, isGetProductAgeGroupsLoading } = useGetProductAgeGroups({
    params: { locale: primaryLanguage, pagination: false },
    useQueryOptions: {
      staleTime: 5 * 60 * 1000,
    },
  })
  const { productMedicineTypes, isGetProductMedicineTypesLoading } = useGetProductMedicineTypes({
    params: { locale: primaryLanguage, pagination: false },
    useQueryOptions: {
      staleTime: 5 * 60 * 1000,
    },
  })

  const { toggleTempFilter, initTempFilters } = useProductStore(
    useShallow((state) => ({
      toggleTempFilter: state.toggleTempFilter,
      initTempFilters: state.initTempFilters,
    })),
  )

  // Initialize temp filters with current filters
  useEffect(() => {
    initTempFilters(productType)
  }, [productType, initTempFilters])

  const activeAgeGroupIds = useProductStore(
    useShallow(
      (state) =>
        state.productTabState[productType].tempFilters[ProductFilterType.AGE_GROUP]?.map(
          (filter) => filter.id,
        ) || [],
    ),
  )

  const activeMedicineTypeIds = useProductStore(
    useShallow(
      (state) =>
        state.productTabState[productType].tempFilters[ProductFilterType.MEDICINE_TYPE]?.map(
          (filter) => filter.id,
        ) || [],
    ),
  )

  const handleToggleAgeGroup = useCallback(
    (ageGroup: ProductAgeGroup) => {
      toggleTempFilter(productType, ProductFilterType.AGE_GROUP, {
        id: ageGroup.id,
        label: ageGroup.title,
        type: ProductFilterType.AGE_GROUP,
      })
    },
    [toggleTempFilter, productType],
  )

  const handleToggleMedicineType = useCallback(
    (medicineType: ProductMedicineType) => {
      toggleTempFilter(productType, ProductFilterType.MEDICINE_TYPE, {
        id: medicineType.id,
        label: medicineType.title,
        type: ProductFilterType.MEDICINE_TYPE,
      })
    },
    [toggleTempFilter, productType],
  )

  const ageGroupData = useMemo(() => productAgeGroups?.docs || [], [productAgeGroups?.docs])
  const medicineTypeData = useMemo(
    () => productMedicineTypes?.docs || [],
    [productMedicineTypes?.docs],
  )

  // Track active filters count to trigger footer height re-measurement
  const activeFiltersCount = activeAgeGroupIds.length + activeMedicineTypeIds.length

  // Trigger manual footer measurement when filters change (no re-render!)
  useEffect(() => {
    // Small delay to allow footer DOM updates to complete
    const timer = setTimeout(() => {
      sheetBoxRef.current?.measureFooter()
    }, 100)

    return () => clearTimeout(timer)
  }, [activeFiltersCount])

  return (
    <BaseFilterSheetBox
      ref={sheetBoxRef}
      title={t('MES-481')}
      onClose={closeSheet}
      footerRef={sharedFooterRef}
      enableFooterHeightMeasurement={true}
      footerHeightPadding={20}
    >
      <BottomSheetScrollView className="relative" showsVerticalScrollIndicator={false}>
        <View className="flex flex-col gap-y-3 px-4">
          <BaseFilterListSection
            title={t('MES-707')}
            data={ageGroupData}
            onSelectFilter={handleToggleAgeGroup}
            activeFilters={activeAgeGroupIds}
            idKey="id"
            labelKey="title"
            isLoading={isGetProductAgeGroupsLoading}
          />
          {productType === ProductV2TypeEnum.MEDICINE && (
            <BaseFilterListSection
              title={t('MES-708')}
              data={medicineTypeData}
              onSelectFilter={handleToggleMedicineType}
              activeFilters={activeMedicineTypeIds}
              idKey="id"
              labelKey="title"
              isLoading={isGetProductMedicineTypesLoading}
            >
              <MedicineTypeNotes medicineType={medicineTypeData} />
            </BaseFilterListSection>
          )}
        </View>
      </BottomSheetScrollView>
    </BaseFilterSheetBox>
  )
}

export const useOpenFilterProductBox = (productType: ProductV2TypeEnum) => {
  const { openCustomSheet, closeSheet } = useSheetActions()

  const sharedFooterRef = useRef<View>(null)
  const sharedSheetBoxRef = useRef<BaseFilterSheetBoxRef>(null)

  const handleOpenFilterProductBox = useCallback(() => {
    openCustomSheet({
      children: ({ close }) => (
        <FilterProductBox
          productType={productType}
          closeSheet={close}
          sharedFooterRef={sharedFooterRef}
          sharedSheetBoxRef={sharedSheetBoxRef}
        />
      ),
      baseProps: {
        snapPoints: ['85%', '100%'],
        enableHandlePanningGesture: true,
        enableDynamicSizing: false,
        enableOverDrag: false,
      },
      options: {
        footerComponent: (props) => {
          return (
            <FilterFooter
              ref={sharedFooterRef}
              productType={productType}
              closeSheet={closeSheet}
              {...props}
            />
          )
        },
      },
    })
  }, [openCustomSheet, productType])

  return { handleOpenFilterProductBox }
}
