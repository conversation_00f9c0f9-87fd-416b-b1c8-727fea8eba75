import { Hospital } from '@/features/hospital-partner/types'
import { Keyword } from '@/types/keyword.type'
import { Media } from '@/types/media.type'
import { MedicalDocumentFileTypeEnum } from '../enums'

export interface MedicalDocument {
  id: string
  name: string
  categories: MedicalDocumentCategory[]
  /**
   * Select the file type that corresponds to the uploaded document.
   */
  fileType: MedicalDocumentFileTypeEnum
  file: string | Media
  tags?: (string | Keyword)[] | null
  updatedAt: string
  createdAt: string
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "medical-document-categories".
 */
export interface MedicalDocumentCategory {
  id: string
  name: string
  description?: string | null
  icon?: (string | null) | Media
  updatedAt: string
  createdAt: string
  color?: string | null
  hospital?: Hospital | null
}
