import { Text } from '@/components/ui/Text/Text'
import { useAuthentication } from '@/hooks/auth/useAuthentication'
import { keywordQueryKeys } from '@/hooks/query/keyword/queryKeys'
import { useQueryClient } from '@tanstack/react-query'
import { useLocalSearchParams } from 'expo-router'
import React, { useCallback, useEffect, useRef, useState } from 'react'
import { useTranslation } from 'react-i18next'
import { View } from 'react-native'
import { KeywordCategoriesSection } from '../../components/KeywordCategoriesSection/KeywordCategoriesSection'
import { MedicalDictionaryList } from '../../components/MedicalDictionaryList/MedicalDictionaryList'
import { useMedicalDictionaryList } from '../../hooks/common/useMedicalDictionaryList'

export default function MedicalDictionaryCategoriesScreen() {
  const { category } = useLocalSearchParams()
  const [activeCategory, setActiveCategory] = useState(category as string)
  const { t } = useTranslation()
  const { user } = useAuthentication()
  const { refreshing, data, isGetKeywordsError, handleRefresh, handleLoadMore } =
    useMedicalDictionaryList({
      activeCategory: [activeCategory],
      extendedKeys: [activeCategory, ...keywordQueryKeys['dictionary-keywords-by-category'].base()],
      config: {
        staleTime: 0,
        refetchOnMount: 'always',
        gcTime: 0,
      },
    })

  const handlePressCategory = useCallback((category: string) => {
    setActiveCategory(category)
  }, [])
  const queryClient = useQueryClient()
  // Invalidate queries list in MedicalDictionaryScreen
  const shouldInvalidateQueries = useRef(false)
  useEffect(() => {
    return () => {
      if (shouldInvalidateQueries.current) {
        queryClient.invalidateQueries({
          queryKey: keywordQueryKeys['dictionary-keywords'].base(),
          type: 'active',
          refetchType: 'active',
          exact: false,
        })
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [])
  const renderHeaderComponent = useCallback(() => {
    return (
      <View className="gap-y-4">
        <KeywordCategoriesSection
          activeCategory={activeCategory}
          onPressCategory={handlePressCategory}
          isShowTitle={false}
          mode="collapse"
          enableRepositioning={true}
          defaultItemsToShow={4}
        />

        {/* List Header */}
        <View className="mb-3">
          <Text size="body3" className="font-medium">
            {t('MES-565')}
          </Text>
        </View>
      </View>
    )
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [activeCategory, handlePressCategory, t, refreshing])

  return (
    <MedicalDictionaryList
      data={data}
      refreshing={refreshing}
      isGetKeywordsError={isGetKeywordsError}
      onRefresh={handleRefresh}
      onLoadMore={handleLoadMore}
      headerComponent={renderHeaderComponent()}
      containerStyle="flex-1 bg-white p-4"
      showArchiveButton={Boolean(user)}
      onUpdateFavoriteKeywordSuccess={() => {
        shouldInvalidateQueries.current = true
      }}
    />
  )
}
