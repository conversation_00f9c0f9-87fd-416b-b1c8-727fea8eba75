import { LocaleEnum } from '@/enums/locale.enum'

/**
 * Parse docName which can be:
 * 1. A simple string: "Headache"
 * 2. A JSON string: "{\"vi\":\"Đau đầu\",\"ja\":\"頭が痛い\"}"
 * 3. An object: {"vi":"Đau đầu","ja":"頭が痛い"}
 * 4. undefined/null
 */
export const parseDocName = (
  docName: string | Record<string, string> | undefined | null,
  language: LocaleEnum | string,
): string => {
  if (!docName) {
    return ''
  }

  // If it's already an object, use it directly
  if (typeof docName === 'object') {
    return docName[language] || Object.values(docName)[0] || ''
  }

  // If it's a string, check if it's a JSON string
  if (typeof docName === 'string') {
    // Try to parse as JSON first
    try {
      const parsed = JSON.parse(docName)
      if (typeof parsed === 'object' && parsed !== null) {
        return parsed[language] || Object.values(parsed)[0] || ''
      }
    } catch {
      // If JSON parsing fails, treat it as a regular string
    }

    // Return the string as-is if it's not JSON
    return docName
  }

  return ''
}
