import DefaultAvatarIcon from '@/assets/icons/default-avatar-icon.svg'
import EditIcon from '@/assets/profile/edit-icon.svg'
import { Text } from '@/components/ui/Text/Text'
import { BLURHASH_CODE } from '@/constants/global.constant'
import { useAuthentication } from '@/hooks/auth/useAuthentication'
import { StyledExpoImage } from '@/libs/styled'
import { APP_ROUTES } from '@/routes/appRoutes'
import { Media } from '@/types/media.type'
import dayjs from 'dayjs'
import { Link, LinkProps, useRouter } from 'expo-router'
import React from 'react'
import { useTranslation } from 'react-i18next'
import { TouchableOpacity, View } from 'react-native'

export const SummaryInfo = () => {
  const router = useRouter()
  const { user } = useAuthentication()
  const avatarMedia = user?.avatar as Media
  const avatar = avatarMedia?.thumbnailURL || avatarMedia?.url || user?.oauthAvatar || ''
  const { t } = useTranslation()

  return (
    <View className="flex flex-col items-center justify-center gap-y-3 rounded-xl bg-primary-50 p-4">
      <>
        {avatar ? (
          <View className="h-20 w-20 overflow-hidden rounded-full">
            <StyledExpoImage
              source={{ uri: avatar }}
              contentFit="cover"
              className="h-full w-full"
              transition={1000}
              placeholder={BLURHASH_CODE}
            />
          </View>
        ) : (
          <DefaultAvatarIcon width={80} height={80} />
        )}
      </>
      <View className="flex flex-col items-center justify-center gap-y-3">
        <Text size="heading7" className="text-center" numberOfLines={3}>
          {user?.name}
        </Text>
        <Link href={APP_ROUTES.EDIT_PROFILE.path as LinkProps['href']} asChild>
          <TouchableOpacity className="flex flex-row items-center gap-x-2 rounded-[99px] border border-custom-informative-700 bg-white px-3 py-2">
            <EditIcon width={16} height={16} />
            <Text size="body6" className="text-custom-informative-700">
              {t('MES-650')}
            </Text>
          </TouchableOpacity>
        </Link>
        <Text size="body9" variant="subdued">
          {t('MES-217')}: {dayjs(user?.lastLogin).format('YYYY/MM/DD HH:mm')}
        </Text>
      </View>
    </View>
  )
}
