import { LocaleEnum } from '@/enums/locale.enum'
import { Platform } from 'react-native'
import { create } from 'zustand'
import {
  PushNotificationMessagesPlatformGroupsEnum,
  PushNotificationMessagesTypeEnum,
} from '../enums/push-notification.enum'
import { unifiedNotificationService } from '../services/unified-notification.service'

export enum NotificationFilterType {
  TYPE = 'type',
}

export interface NotificationFilterItem {
  id: string
  label: string
  type: NotificationFilterType
  value: PushNotificationMessagesTypeEnum
}

export interface NotificationFilter {
  [NotificationFilterType.TYPE]: NotificationFilterItem[] | null
}

export interface UnifiedNotificationState {
  // FCM Push Notifications
  fcmToken: string | null
  fcmIsRegistered: boolean
  fcmIsLoading: boolean
  fcmError: string | null
  permissionGranted: boolean

  // Push Notification Token Management
  pushToken: string | null
  pushTokenIsValid: boolean
  pushTokenIsRegistered: boolean
  pushTokenIsLoading: boolean
  pushTokenError: string | null
  lastTokenValidation: number | null

  // Local Notifications
  localNotificationsEnabled: boolean
  localNotificationsLoading: boolean
  localNotificationsError: string | null

  // General notification settings
  authNotifiable: boolean
  language: LocaleEnum

  // User attachment tracking
  userAttached: boolean

  // Notification filters
  filters: NotificationFilter
  tempFilters: NotificationFilter
  hasActiveFilters: boolean
}

// Notification actions
export interface UnifiedNotificationActions {
  // FCM Actions
  setFcmToken: (token: string | null) => void
  setFcmIsRegistered: (isRegistered: boolean) => void
  setFcmIsLoading: (isLoading: boolean) => void
  setFcmError: (error: string | null) => void
  setFcmPermissionGranted: (granted: boolean) => void

  // Push Token Actions
  setPushToken: (token: string | null) => void
  setPushTokenIsValid: (isValid: boolean) => void
  setPushTokenIsRegistered: (isRegistered: boolean) => void
  setPushTokenIsLoading: (isLoading: boolean) => void
  setPushTokenError: (error: string | null) => void
  setLastTokenValidation: (timestamp: number | null) => void

  // Local Notification Actions
  setLocalNotificationsEnabled: (enabled: boolean) => void
  setLocalNotificationsLoading: (isLoading: boolean) => void
  setLocalNotificationsError: (error: string | null) => void

  // General Actions
  setAuthNotifiable: (authNotifiable: boolean) => void
  setLanguage: (language: LocaleEnum) => void
  setUserAttached: (userAttached: boolean) => void

  // Filter Actions
  toggleFilter: (filterType: NotificationFilterType, filter: NotificationFilterItem) => void
  clearFilters: (filterType?: NotificationFilterType) => void
  clearAllFilters: () => void
  // Temporary filter actions
  toggleTempFilter: (filterType: NotificationFilterType, filter: NotificationFilterItem) => void
  applyTempFilters: () => void
  resetTempFilters: () => void
  initTempFilters: () => void
  clearAllTempFilters: () => void

  // Service Actions
  initializeNotifications: (language: LocaleEnum) => Promise<void>
  updateAuthNotifiableStatus: (authNotifiable: boolean) => Promise<void>
  updateNotificationLanguage: (language: LocaleEnum) => Promise<void>
  attachUserToToken: () => Promise<void>
  validateToken: () => Promise<void>
  createToken: () => Promise<void>
  recreateToken: () => Promise<void>
  resetUserAttachment: () => void
  reset: () => void
}

// Helper function to check if filters are active
const hasActiveFilters = (filters: NotificationFilter): boolean => {
  return Object.values(filters).some((filterArray) => filterArray && filterArray.length > 0)
}

const initialState = {
  // FCM Push Notifications
  fcmToken: null as string | null,
  fcmIsRegistered: false,
  fcmIsLoading: false,
  fcmError: null as string | null,
  permissionGranted: false,

  // Push Notification Token Management
  pushToken: null as string | null,
  pushTokenIsValid: false,
  pushTokenIsRegistered: false,
  pushTokenIsLoading: false,
  pushTokenError: null as string | null,
  lastTokenValidation: null as number | null,

  // Local Notifications
  localNotificationsEnabled: false,
  localNotificationsLoading: false,
  localNotificationsError: null as string | null,

  // General notification settings
  authNotifiable: false,
  language: LocaleEnum.VI,

  // User attachment tracking
  userAttached: false,

  // Notification filters
  filters: {
    [NotificationFilterType.TYPE]: null,
  } as NotificationFilter,
  tempFilters: {
    [NotificationFilterType.TYPE]: null,
  } as NotificationFilter,
  hasActiveFilters: false,
}

export const useUnifiedNotificationStore = create<
  UnifiedNotificationState & UnifiedNotificationActions
>((set, get) => ({
  ...initialState,

  // FCM Actions
  setFcmToken: (token: string | null) => set({ fcmToken: token }),
  setFcmIsRegistered: (isRegistered: boolean) => set({ fcmIsRegistered: isRegistered }),
  setFcmIsLoading: (isLoading: boolean) => set({ fcmIsLoading: isLoading }),
  setFcmError: (error: string | null) => set({ fcmError: error }),
  setFcmPermissionGranted: (granted: boolean) => set({ permissionGranted: granted }),

  // Push Token Actions
  setPushToken: (token: string | null) => set({ pushToken: token }),
  setPushTokenIsValid: (isValid: boolean) => set({ pushTokenIsValid: isValid }),
  setPushTokenIsRegistered: (isRegistered: boolean) => set({ pushTokenIsRegistered: isRegistered }),
  setPushTokenIsLoading: (isLoading: boolean) => set({ pushTokenIsLoading: isLoading }),
  setPushTokenError: (error: string | null) => set({ pushTokenError: error }),
  setLastTokenValidation: (timestamp: number | null) => set({ lastTokenValidation: timestamp }),

  // Local Notification Actions
  setLocalNotificationsEnabled: (enabled: boolean) => set({ localNotificationsEnabled: enabled }),
  setLocalNotificationsLoading: (isLoading: boolean) =>
    set({ localNotificationsLoading: isLoading }),
  setLocalNotificationsError: (error: string | null) => set({ localNotificationsError: error }),

  // General Actions
  setAuthNotifiable: (authNotifiable: boolean) => set({ authNotifiable }),
  setLanguage: (language: LocaleEnum) => set({ language }),
  setUserAttached: (userAttached: boolean) => set({ userAttached }),

  // Filter Actions
  toggleFilter: (filterType: NotificationFilterType, filter: NotificationFilterItem) => {
    set((state) => {
      const currentFilters = state.filters[filterType] || []
      const isSelected = currentFilters.some((f) => f.id === filter.id)

      const newFilters = {
        ...state.filters,
        [filterType]: isSelected
          ? currentFilters.filter((f) => f.id !== filter.id)
          : [...currentFilters, filter],
      }

      return {
        filters: newFilters,
        hasActiveFilters: hasActiveFilters(newFilters),
      }
    })
  },
  clearFilters: (filterType?: NotificationFilterType) => {
    set((state) => {
      const newFilters = filterType
        ? {
            ...state.filters,
            [filterType]: null,
          }
        : {
            [NotificationFilterType.TYPE]: null,
          }

      return {
        filters: newFilters,
        hasActiveFilters: hasActiveFilters(newFilters),
      }
    })
  },
  clearAllFilters: () => {
    set({
      filters: {
        [NotificationFilterType.TYPE]: null,
      },
      hasActiveFilters: false,
    })
  },
  // Temporary filter actions
  toggleTempFilter: (filterType: NotificationFilterType, filter: NotificationFilterItem) => {
    set((state) => {
      const currentTempFilters = state.tempFilters[filterType] || []
      const isSelected = currentTempFilters.some((f) => f.id === filter.id)

      return {
        tempFilters: {
          ...state.tempFilters,
          [filterType]: isSelected
            ? currentTempFilters.filter((f) => f.id !== filter.id)
            : [...currentTempFilters, filter],
        },
      }
    })
  },
  applyTempFilters: () => {
    set((state) => {
      const newFilters = { ...state.tempFilters }

      return {
        filters: newFilters,
        hasActiveFilters: hasActiveFilters(newFilters),
      }
    })
  },
  resetTempFilters: () => {
    set((state) => ({
      tempFilters: {
        [NotificationFilterType.TYPE]: state.filters[NotificationFilterType.TYPE],
      },
    }))
  },
  initTempFilters: () => {
    set((state) => ({
      tempFilters: {
        [NotificationFilterType.TYPE]: state.filters[NotificationFilterType.TYPE],
      },
    }))
  },
  clearAllTempFilters: () => {
    set({
      tempFilters: {
        [NotificationFilterType.TYPE]: null,
      },
    })
  },

  // Service Actions
  initializeNotifications: async (language: LocaleEnum) => {
    // Set language
    set({ language })

    // Step 1: Request FCM permissions first (iOS requires this before getting token)
    try {
      console.log('📱 Requesting FCM permissions...')
      const permissionStatus = await unifiedNotificationService.requestFCMPermissions()
      console.log('📱 FCM Permission status:', permissionStatus)

      const isGranted = permissionStatus === 'granted' || permissionStatus === 1 // 1 is AuthorizationStatus.AUTHORIZED
      set({ permissionGranted: isGranted })

      if (permissionStatus === 'denied' || permissionStatus === 'not_determined') {
        console.warn('⚠️ FCM permissions not granted:', permissionStatus)
        // Continue anyway, as Android might not need explicit permission request
      }
    } catch (error) {
      console.error('❌ Failed to request FCM permissions:', error)
      set({ permissionGranted: false })
      // Continue anyway, as the error might be platform-specific
    }

    // Step 2: Initialize FCM token
    set({ fcmIsLoading: true, fcmError: null })
    try {
      const fcmToken = await unifiedNotificationService.getFCMToken()
      if (fcmToken) {
        set({
          fcmToken,
          fcmIsRegistered: true,
          fcmIsLoading: false,
        })
      } else {
        set({
          fcmToken: null,
          fcmIsRegistered: false,
          fcmIsLoading: false,
          fcmError: 'Failed to get FCM token',
        })
      }
    } catch (error) {
      console.error('Failed to get FCM token:', error)
      set({
        fcmToken: null,
        fcmIsRegistered: false,
        fcmIsLoading: false,
        fcmError: error instanceof Error ? error.message : 'Failed to get FCM token',
      })
    }

    // Initialize push notification token management
    const fcmToken = get().fcmToken
    if (fcmToken) {
      set({ pushTokenIsLoading: true, pushTokenError: null })

      try {
        // Check if token is valid on backend
        console.log('🔍 Checking token validity on backend...')
        const validityResponse = await unifiedNotificationService.checkTokenValidity({
          token: fcmToken,
          platform: Platform.OS as PushNotificationMessagesPlatformGroupsEnum,
        })

        console.log('📋 Token validation result:', {
          isValid: validityResponse.isValid,
          exists: validityResponse.exists,
          platformMismatch: validityResponse.platformMismatch,
        })

        if (
          validityResponse.isValid &&
          validityResponse.exists &&
          !validityResponse.platformMismatch
        ) {
          // Token is valid and exists on backend - use it
          console.log('✅ Token is valid and exists on backend')
          set({
            pushToken: fcmToken,
            pushTokenIsValid: true,
            pushTokenIsRegistered: true,
            lastTokenValidation: Date.now(),
            pushTokenIsLoading: false,
          })
        } else if (!validityResponse.exists) {
          // Token doesn't exist on backend - create it
          console.log('📡 Token does not exist on backend, creating...')
          set({ pushTokenIsLoading: false }) // Clear loading before calling createToken
          await get().createToken()
        } else if (validityResponse.platformMismatch) {
          // Token exists but platform mismatches - recreate it
          console.log('🔄 Platform mismatch detected, recreating token...')
          set({ pushTokenIsLoading: false }) // Clear loading before calling recreateToken
          await get().recreateToken()
        } else {
          // Token exists but is invalid - recreate it
          console.log('🔄 Token exists but is invalid, recreating...')
          set({ pushTokenIsLoading: false }) // Clear loading before calling recreateToken
          await get().recreateToken()
        }
      } catch (error) {
        console.error('Token validation failed during initialization:', error)
        // If validation fails, try to create the token anyway
        console.log('⚠️ Validation failed, attempting to create token...')
        set({ pushTokenIsLoading: false }) // Clear loading before calling createToken
        try {
          await get().createToken()
        } catch (createError) {
          console.error('Failed to create token after validation failure:', createError)
          set({
            pushToken: fcmToken,
            pushTokenIsValid: false,
            pushTokenIsRegistered: false,
            pushTokenError:
              createError instanceof Error ? createError.message : 'Token creation failed',
          })
        }
      }
    }

    // Initialize local notifications
    set({ localNotificationsLoading: true, localNotificationsError: null })
    try {
      const permissions = await unifiedNotificationService.getPermissionsStatus()
      set({
        localNotificationsEnabled: permissions.status === 'granted',
        localNotificationsLoading: false,
      })
    } catch (error) {
      console.error('Failed to initialize local notifications:', error)
      set({
        localNotificationsEnabled: false,
        localNotificationsLoading: false,
        localNotificationsError:
          error instanceof Error ? error.message : 'Failed to initialize local notifications',
      })
    }
  },

  updateAuthNotifiableStatus: async (authNotifiable: boolean) => {
    const { pushToken } = get()
    if (!pushToken) {
      console.warn('⚠️ Cannot update authNotifiable: no pushToken available')
      return
    }

    try {
      console.log(`📡 Calling backend to update authNotifiable to: ${authNotifiable}`)
      await unifiedNotificationService.updateAuthNotifiable(pushToken, authNotifiable)
      set({ authNotifiable })
      console.log(`✅ Store updated: authNotifiable = ${authNotifiable}`)
    } catch (error) {
      console.error('❌ Failed to update auth notifiable status:', error)
      throw error
    }
  },

  updateNotificationLanguage: async (language: LocaleEnum) => {
    const { pushToken } = get()
    if (!pushToken) {
      return
    }

    try {
      await unifiedNotificationService.updateNotificationLanguage(pushToken, language)
      set({ language })
    } catch (error) {
      console.error('Failed to update notification language:', error)
      throw error
    }
  },

  attachUserToToken: async () => {
    const { pushToken, userAttached, pushTokenIsLoading } = get()
    if (!pushToken || userAttached || pushTokenIsLoading) {
      return
    }

    try {
      const response = await unifiedNotificationService.attachUserToPushNotificationToken(pushToken)

      if (response.success) {
        set({ userAttached: true })
      }
    } catch (error) {
      console.error('Failed to attach user to token:', error)
      throw error
    }
  },

  validateToken: async () => {
    const { pushToken, pushTokenIsLoading } = get()
    if (!pushToken || pushTokenIsLoading) {
      return
    }

    set({ pushTokenIsLoading: true, pushTokenError: null })
    try {
      const validityResponse = await unifiedNotificationService.checkTokenValidity({
        token: pushToken,
        platform: Platform.OS as PushNotificationMessagesPlatformGroupsEnum,
      })

      const isValid =
        validityResponse.isValid && validityResponse.exists && !validityResponse.platformMismatch

      set({
        pushTokenIsValid: isValid,
        pushTokenIsRegistered: isValid,
        lastTokenValidation: Date.now(),
        pushTokenIsLoading: false,
      })

      // Log validation result but don't auto-recreate
      // Let the caller decide whether to recreate
      if (!isValid) {
        console.warn('⚠️ Token validation failed:', {
          isValid: validityResponse.isValid,
          exists: validityResponse.exists,
          platformMismatch: validityResponse.platformMismatch,
        })
      }
    } catch (error: any) {
      console.error('Token validation failed:', error)

      set({
        pushTokenIsValid: false,
        pushTokenIsRegistered: false,
        pushTokenError: error instanceof Error ? error.message : 'Token validation failed',
        pushTokenIsLoading: false,
      })
    }
  },

  createToken: async () => {
    const { fcmToken, language, authNotifiable, pushTokenIsLoading } = get()
    if (!fcmToken || pushTokenIsLoading) {
      return
    }

    set({ pushTokenIsLoading: true, pushTokenError: null })
    try {
      console.log('📡 Creating push notification token in backend...')
      const response = await unifiedNotificationService.createPushNotificationToken(
        fcmToken,
        language,
        authNotifiable,
      )

      if (response.doc?.token === fcmToken) {
        set({
          pushToken: fcmToken,
          pushTokenIsValid: true,
          pushTokenIsRegistered: true,
          lastTokenValidation: Date.now(),
        })
        console.log('✅ Token created and registered successfully in backend')
      } else {
        console.error('❌ Backend returned different token than expected')
      }
    } catch (error: any) {
      console.error('❌ Failed to create push notification token:', error)

      // Handle specific error cases
      if (error?.status === 409 || error?.message?.includes('Token already exists')) {
        // If token already exists, treat it as valid and set the state
        console.log('⚠️ Token already exists on backend, treating as valid')
        set({
          pushToken: fcmToken,
          pushTokenIsValid: true,
          pushTokenIsRegistered: true,
          lastTokenValidation: Date.now(),
          pushTokenError: null, // Clear any previous errors
        })
      } else {
        set({
          pushTokenError: error instanceof Error ? error.message : 'Token creation failed',
        })
        throw error
      }
    } finally {
      set({ pushTokenIsLoading: false })
    }
  },

  recreateToken: async () => {
    const { language, authNotifiable, pushTokenIsLoading, fcmIsLoading } = get()

    // Prevent multiple simultaneous recreations
    if (pushTokenIsLoading || fcmIsLoading) {
      console.log('⚠️ Token recreation already in progress, skipping...')
      return
    }

    set({
      pushTokenIsLoading: true,
      pushTokenError: null,
      fcmIsLoading: true,
      fcmError: null,
    })

    try {
      console.log('🔄 Recreating FCM token...')

      // Step 1: Delete old FCM token from device
      await unifiedNotificationService.deleteFCMToken()

      // Step 2: Clear old token from state
      set({
        fcmToken: null,
        pushToken: null,
        pushTokenIsValid: false,
        pushTokenIsRegistered: false,
        userAttached: false,
      })

      // Step 3: Get new FCM token from device
      const newFcmToken = await unifiedNotificationService.getFCMToken()

      if (!newFcmToken) {
        throw new Error('Failed to get new FCM token')
      }

      console.log('✅ New FCM token obtained:', newFcmToken.substring(0, 20) + '...')

      // Step 4: Update FCM token in state
      set({
        fcmToken: newFcmToken,
        fcmIsRegistered: true,
        fcmIsLoading: false,
      })

      // Step 5: Register new token with backend
      console.log('📡 Registering new token with backend...')
      const response = await unifiedNotificationService.createPushNotificationToken(
        newFcmToken,
        language,
        authNotifiable,
      )

      if (response.doc?.token === newFcmToken) {
        set({
          pushToken: newFcmToken,
          pushTokenIsValid: true,
          pushTokenIsRegistered: true,
          lastTokenValidation: Date.now(),
        })
        console.log('✅ Token recreated and registered successfully in backend')
      } else {
        console.error('❌ Backend returned different token than expected')
        throw new Error('Token mismatch after backend registration')
      }
    } catch (error: any) {
      console.error('❌ Failed to recreate token:', error)

      // Handle specific error cases
      if (error?.status === 409 || error?.message?.includes('Token already exists')) {
        const { fcmToken } = get()
        if (fcmToken) {
          // If token already exists on server, treat it as valid
          set({
            pushToken: fcmToken,
            pushTokenIsValid: true,
            pushTokenIsRegistered: true,
            lastTokenValidation: Date.now(),
            pushTokenError: null,
          })
          console.log('⚠️ Token already exists on server, using it')
        }
      } else {
        set({
          pushTokenError: error instanceof Error ? error.message : 'Token recreation failed',
        })
        throw error
      }
    } finally {
      set({
        pushTokenIsLoading: false,
        fcmIsLoading: false,
      })
    }
  },

  // Force reset user attachment when there are token conflicts
  resetUserAttachment: () => {
    set({ userAttached: false })
  },

  reset: () => set(initialState),
}))
