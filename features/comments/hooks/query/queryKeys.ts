export const commentQueryKeys = {
  main: ['comments'],
  comments: {
    base: () => ['comments'],
  },
  'nested-comments': {
    base: () => ['nested-comments'],
  },
  totalComments: {
    base: () => ['total-comments'],
  },
}

export const commentMutationKeys = {
  reactionComment: {
    base: () => ['reaction-comment'],
  },
  addComment: {
    base: () => ['add-comment'],
  },
  editComment: {
    base: () => ['edit-comment'],
  },
  deleteComment: {
    base: () => ['delete-comment'],
  },
  reportComment: {
    base: () => ['report-comment'],
  },
}
