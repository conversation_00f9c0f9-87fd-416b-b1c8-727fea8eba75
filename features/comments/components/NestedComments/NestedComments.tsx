import DoubleMessagesIcon from '@/assets/icons/double-messages-icon.svg'
import RefreshIcon from '@/assets/icons/refresh-icon.svg'
import { Skeleton } from '@/components/ui/Skeleton/Skeleton'
import { Text } from '@/components/ui/Text/Text'
import { memo, useMemo, useState } from 'react'
import { useTranslation } from 'react-i18next'
import { ActivityIndicator, TouchableOpacity, View } from 'react-native'
import { useGetInfiniteNestedComments } from '../../hooks/query/useGetInfiniteNestedComments'
import { CommentCacheManager, CommentQueryParams } from '../../services/commentCacheManager.service'
import { Comment } from '../../types'
import { CommentItem } from '../CommentItem/CommentItem'
interface NestedCommentsProps {
  totalNestedComments: number
  parentId: string
  relationTo: Comment['commentedOn']['relationTo']
  isNested?: boolean
  onReply?: (comment: any) => void
  onEditComment?: (commentId: string, content: string, parentId?: string) => void
  onStartEdit?: (commentId: string) => void
  onCancelEdit?: () => void
  onDeleteComment?: (commentId: string, parentId?: string) => void
  onRetryAddComment?: (content: string, parentId?: string) => void
  isEditCommentPending?: boolean
  editingCommentId?: string | null
  cacheManager?: CommentCacheManager
  mainParams?: CommentQueryParams
  nestedParams?: CommentQueryParams
  forceOpenNestedComments?: boolean
  nestedCommentId?: string
  showLoadmoreNestedComments?: boolean
}
export const NestedComments = memo(
  ({
    nestedCommentId,
    forceOpenNestedComments = false,
    totalNestedComments,
    parentId,
    relationTo,
    isNested = true, // Default to true since this component is for nested comments
    onReply,
    onEditComment,
    onStartEdit,
    onCancelEdit,
    onDeleteComment,
    onRetryAddComment,
    isEditCommentPending = false,
    editingCommentId,
    cacheManager,
    mainParams,
    nestedParams,
    showLoadmoreNestedComments = true,
  }: NestedCommentsProps) => {
    const [isOpen, setIsOpen] = useState(forceOpenNestedComments)
    const { t } = useTranslation()

    const queryParams = useMemo(
      () =>
        nestedParams || {
          page: 1,
          limit: 5,
          nestedCommentId: nestedCommentId || '',
        },
      [nestedParams, nestedCommentId],
    )

    const {
      nestedComments,
      isGetNestedCommentsLoading,
      isGetNestedCommentsFetchingNextPage,
      fetchNextPage,
      hasNextPage,
    } = useGetInfiniteNestedComments({
      parentId,
      relationTo,
      params: queryParams,
      config: {
        enabled: isOpen || (forceOpenNestedComments && Boolean(nestedCommentId)),
        staleTime: 0,
        gcTime: 0,
      },
    })
    const handleOpen = () => {
      setIsOpen(true)
    }

    if (totalNestedComments === 0) return null

    return (
      <>
        {!isOpen ? (
          // Collapsed state - Show expandable button with reply count
          <TouchableOpacity
            className=" w-full  gap-y-2 rounded-lg bg-primary-50 px-2 py-2"
            onPress={handleOpen}
          >
            <View className="flex flex-row items-center gap-x-2">
              <DoubleMessagesIcon />
              <Text size="body6" variant="subdued">
                {t('MES-876', { number: totalNestedComments })}
              </Text>
            </View>
          </TouchableOpacity>
        ) : (
          <>
            {/* Expanded state - Show nested comments */}
            {isGetNestedCommentsLoading && !nestedComments ? (
              <NestedCommentsLoading />
            ) : (
              <View className="rounded-lg bg-primary-50 px-2 py-2">
                <View className="flex flex-col gap-y-3">
                  {/* Render all nested comment pages */}
                  {nestedComments?.pages?.map((page) =>
                    page?.docs?.map((comment) => {
                      // SAFETY: Validate nested comment data
                      if (!comment || typeof comment === 'string' || !comment.id) {
                        console.warn('[NestedComments] Invalid comment:', comment)
                        return null
                      }

                      return (
                        <CommentItem
                          key={comment.id}
                          comment={comment}
                          relationTo={relationTo}
                          isNested={isNested}
                          onReply={onReply}
                          onEditComment={onEditComment}
                          onStartEdit={onStartEdit}
                          onCancelEdit={onCancelEdit}
                          onDeleteComment={onDeleteComment}
                          onRetryAddComment={onRetryAddComment}
                          isEditCommentPending={isEditCommentPending}
                          editingCommentId={editingCommentId}
                          cacheManager={cacheManager}
                          mainParams={mainParams}
                          nestedParams={nestedParams}
                          parentId={parentId}
                          isHighlighted={nestedCommentId === comment.id}
                          showLoadmoreNestedComments={showLoadmoreNestedComments}
                        />
                      )
                    }),
                  )}
                </View>
                {/* Load more button for pagination */}
                {hasNextPage && showLoadmoreNestedComments && (
                  <View className="mt-3">
                    {isGetNestedCommentsFetchingNextPage ? (
                      <View className="flex items-center justify-center">
                        <ActivityIndicator size="small" />
                      </View>
                    ) : (
                      <TouchableOpacity
                        onPress={() => fetchNextPage()}
                        disabled={isGetNestedCommentsFetchingNextPage}
                        className="flex w-full flex-row items-center justify-center"
                      >
                        <View className="flex flex-row items-center justify-center gap-x-2">
                          <RefreshIcon />
                          <Text size="body6" variant="primary">
                            {t('MES-848')}
                          </Text>
                        </View>
                      </TouchableOpacity>
                    )}
                  </View>
                )}
              </View>
            )}
          </>
        )}
      </>
    )
  },
)

NestedComments.displayName = 'NestedComments'
const NestedCommentsLoading = () => {
  return (
    <View className="flex flex-col gap-y-3">
      {new Array(3).fill(0).map((_, index) => (
        <View key={index} className="flex flex-row gap-x-3">
          <Skeleton className="h-12 w-12 rounded-full" />
          <View className="flex flex-1 flex-col gap-y-2">
            <Skeleton className="h-8 w-[120px]" />
            <Skeleton className="h-12 w-full" />
          </View>
        </View>
      ))}
    </View>
  )
}
