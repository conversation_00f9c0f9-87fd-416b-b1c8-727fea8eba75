import DefaultAvatarIcon from '@/assets/icons/default-avatar-icon.svg'
import JapanFlagRoundedIcon from '@/assets/icons/japan-flag.svg'
import LogoDarkRounded from '@/assets/icons/logo-dark-rounded.svg'
import VietnamFlagIcon from '@/assets/icons/vietnam-flag.svg'
import { Text } from '@/components/ui/Text/Text'
import { BLURHASH_CODE } from '@/constants/global.constant'
import { LocaleEnum } from '@/enums/locale.enum'
import { useGetUserUnviewedNotificationCount } from '@/features/notification/hooks/query/useGetUserUnviewedNotificationCount'
import { useAuthentication } from '@/hooks/auth/useAuthentication'
import { useAppLanguage } from '@/hooks/common/useAppLanguage'
import { useLoadingScreen } from '@/hooks/common/useLoadingScreen'
import { StyledExpoImage } from '@/libs/styled'
import { APP_ROUTES } from '@/routes/appRoutes'
import { Media } from '@/types/media.type'
import * as Haptics from 'expo-haptics'
import { LinearGradient } from 'expo-linear-gradient'
import { Link, LinkProps } from 'expo-router'
import React from 'react'
import { useTranslation } from 'react-i18next'
import { TouchableOpacity, View } from 'react-native'
export const MainHeader = () => {
  const { primaryLanguage, setPrimaryAndSecondary } = useAppLanguage()
  const { user, status } = useAuthentication()

  const avatarMedia = user?.avatar as Media

  const avatarURL = avatarMedia?.url || avatarMedia?.thumbnailURL || user?.oauthAvatar || ''

  const { t } = useTranslation()

  const { showLoading, hideLoading } = useLoadingScreen()
  const handleChangeLanguage = async (language: LocaleEnum) => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light)
    showLoading()
    await setPrimaryAndSecondary(language)
    hideLoading()
  }
  const { userUnviewedNotificationCount } = useGetUserUnviewedNotificationCount({
    useQueryOptions: {
      enabled: user && status === 'success',
      staleTime: 5 * 60 * 1000,
    },
  })
  return (
    <View className=" flex w-full flex-row items-center  justify-between gap-x-3 border-b border-custom-neutral-100 bg-white">
      <View className="w-full  flex-row items-center justify-between p-4">
        {/* Logo */}
        <Link href={APP_ROUTES.HOME.path as LinkProps['href']} asChild>
          <TouchableOpacity className="flex flex-row items-center gap-x-2 self-start">
            <LogoDarkRounded width={36} height={36} />
            <Text variant="default" size="body3">
              HICO
            </Text>
          </TouchableOpacity>
        </Link>
        <View className="flex flex-row items-center gap-x-2">
          {/* Switch Language */}
          <View>
            <TouchableOpacity
              onPress={() =>
                handleChangeLanguage(
                  primaryLanguage === LocaleEnum.VI ? LocaleEnum.JA : LocaleEnum.VI,
                )
              }
              className="flex items-center justify-center overflow-hidden rounded-full p-2"
            >
              {primaryLanguage === LocaleEnum.VI ? (
                <VietnamFlagIcon width={28} height={28} />
              ) : (
                <JapanFlagRoundedIcon width={28} height={28} />
              )}
            </TouchableOpacity>
          </View>
          {/* User Avatar / Login Button */}
          <Link
            href={
              user
                ? (APP_ROUTES.PROFILE.path as LinkProps['href'])
                : (APP_ROUTES.LOGIN.path as LinkProps['href'])
            }
            disabled={status === 'loading'}
            asChild
          >
            {user ? (
              <TouchableOpacity
                onPress={() => {
                  Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light)
                }}
                className="relative rounded-full border border-custom-neutral-100"
              >
                <>
                  {avatarURL ? (
                    <StyledExpoImage
                      source={{ uri: avatarURL }}
                      className="aspect-square h-9 w-9 rounded-full"
                      contentFit="cover"
                      transition={1000}
                      placeholder={BLURHASH_CODE}
                    />
                  ) : (
                    <DefaultAvatarIcon width={36} height={36} />
                  )}
                  {userUnviewedNotificationCount &&
                    userUnviewedNotificationCount.unviewedNotificationsCount > 0 && (
                      <View className="absolute -right-2.5 -top-2 flex items-center justify-center  rounded-full bg-red-500 px-[6px] py-[1px] ">
                        <Text size="body8" variant="white">
                          {userUnviewedNotificationCount.unviewedNotificationsCount <= 9
                            ? userUnviewedNotificationCount.unviewedNotificationsCount
                            : '9+'}
                        </Text>
                      </View>
                    )}
                </>
              </TouchableOpacity>
            ) : (
              <TouchableOpacity
                onPress={() => {
                  Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light)
                }}
              >
                <LinearGradient
                  colors={['#1157C8', '#1764E0', '#5BAEF3']}
                  start={{ x: 0, y: 0 }}
                  end={{ x: 1, y: 0 }}
                  className="relative flex  p-4"
                  style={{
                    borderRadius: 99,
                    padding: 0,
                    paddingHorizontal: 16,
                    paddingVertical: 8,
                  }}
                >
                  <Text variant="white" size="button5">
                    {t('MES-06')}
                  </Text>
                </LinearGradient>
              </TouchableOpacity>
            )}
          </Link>
        </View>
      </View>
    </View>
  )
}
