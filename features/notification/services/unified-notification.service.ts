import { APP_CHANNEL } from '@/constants/app-notification.constant'
import { API_ENDPOINTS } from '@/constants/endpoint.constant'
import { LocaleEnum } from '@/enums/locale.enum'
import { PushNotificationMessagesPlatformGroupsEnum } from '@/features/notification/enums/push-notification.enum'
import { appNotificationService } from '@/features/notification/services/app-notification.service'
import { fcmService } from '@/features/notification/services/fcm.service'
import { httpService } from '@/services/http/http.service'
import { PaginatedDocs } from '@/types/global.type'
import { Params } from '@/types/http.type'
import { AxiosRequestConfig } from 'axios'
import Constants from 'expo-constants'
import * as Notifications from 'expo-notifications'
import { Platform } from 'react-native'
import {
  AttachUserToPushNotificationTokenPayload,
  AttachUserToPushNotificationTokenResponse,
  BaseNotification,
  CheckTokenValidityPayload,
  CheckTokenValidityResponse,
  CreatePushNotificationTokenResponse,
  GetUserUnviewedNotificationCountResponse,
  LocalNotificationOptions,
  MarkNotificationAsViewedPayload,
  MarkNotificationAsViewedResponse,
  UpdateAuthNotifiableHandlerPayload,
  UpdateAuthNotifiableHandlerResponse,
  UpdateNotificationLanguagePayload,
  UpdateNotificationLanguageResponse,
  UserPushNotification,
} from '../types'

class UnifiedNotificationService {
  private static instance: UnifiedNotificationService

  private constructor() {}

  public static getInstance(): UnifiedNotificationService {
    if (!UnifiedNotificationService.instance) {
      UnifiedNotificationService.instance = new UnifiedNotificationService()
    }
    return UnifiedNotificationService.instance
  }

  // ==================== PUSH NOTIFICATION METHODS ====================

  /**
   * Get the current platform
   */
  private getPlatform(): PushNotificationMessagesPlatformGroupsEnum {
    if (Platform.OS === 'ios') {
      return PushNotificationMessagesPlatformGroupsEnum.IOS
    } else if (Platform.OS === 'android') {
      return PushNotificationMessagesPlatformGroupsEnum.ANDROID
    } else {
      return PushNotificationMessagesPlatformGroupsEnum.WEB
    }
  }

  /**
   * Get app version
   */
  private getAppVersion(): string | undefined {
    return Constants.expoConfig?.version || Constants.config?.version
  }

  /**
   * Check if FCM is available
   */
  isFCMAvailable(): boolean {
    return fcmService.isAvailable
  }

  /**
   * Get FCM token
   */
  async getFCMToken(): Promise<string | null> {
    if (!this.isFCMAvailable()) {
      return null
    }

    try {
      const token = await fcmService.getToken()
      return token
    } catch (error) {
      console.error('Failed to get FCM token:', error)
      return null
    }
  }

  /**
   * Delete FCM token
   */
  async deleteFCMToken(): Promise<void> {
    if (!this.isFCMAvailable()) {
      return
    }

    try {
      await fcmService.deleteToken()
      console.log('FCM token deleted successfully')
    } catch (error) {
      console.error('Failed to delete FCM token:', error)
      throw error
    }
  }

  /**
   * Check if push notification token is valid on server
   */
  async checkTokenValidity(
    payload: CheckTokenValidityPayload,
  ): Promise<CheckTokenValidityResponse> {
    const response = await httpService.post<CheckTokenValidityResponse>(
      `/${API_ENDPOINTS.push_notification_tokens_api}/check-token-validity`,
      payload,
    )
    return response
  }

  /**
   * Create new push notification token
   */
  async createPushNotificationToken(
    token: string,
    language: LocaleEnum,
    authNotifiable: boolean = true,
    options?: AxiosRequestConfig,
  ): Promise<CreatePushNotificationTokenResponse> {
    const payload = {
      platform: this.getPlatform(),
      appVersion: this.getAppVersion(),
      authNotifiable,
      language,
      token, // Include the FCM token in the payload
    }

    const response = await httpService.post<CreatePushNotificationTokenResponse>(
      `/${API_ENDPOINTS.push_notification_tokens_api}/create-token`,
      payload,
      options,
    )
    return response
  }

  /**
   * Update auth notifiable status
   */
  async updateAuthNotifiable(
    token: string,
    authNotifiable: boolean,
    options?: AxiosRequestConfig,
  ): Promise<UpdateAuthNotifiableHandlerResponse> {
    const payload: UpdateAuthNotifiableHandlerPayload = {
      token,
      authNotifiable,
    }

    const response = await httpService.patch<UpdateAuthNotifiableHandlerResponse>(
      `/${API_ENDPOINTS.push_notification_tokens_api}/update-auth-notifiable`,
      payload,
      options,
    )
    return response
  }

  /**
   * Update notification language
   */
  async updateNotificationLanguage(
    token: string,
    language: LocaleEnum,
    options?: AxiosRequestConfig,
  ): Promise<UpdateNotificationLanguageResponse> {
    const payload: UpdateNotificationLanguagePayload = {
      token,
      language,
    }

    const response = await httpService.patch<UpdateNotificationLanguageResponse>(
      `/${API_ENDPOINTS.push_notification_tokens_api}/update-notification-language`,
      payload,
      options,
    )
    return response
  }

  /**
   * Attach user to push notification token
   */
  async attachUserToPushNotificationToken(
    token: string,

    options?: AxiosRequestConfig,
  ): Promise<AttachUserToPushNotificationTokenResponse> {
    const payload: AttachUserToPushNotificationTokenPayload = {
      token,
    }

    const response = await httpService.patch<AttachUserToPushNotificationTokenResponse>(
      `/${API_ENDPOINTS.push_notification_tokens_api}/attach-user`,
      payload,
      options,
    )
    return response
  }

  // ==================== LOCAL NOTIFICATION METHODS ====================

  /**
   * Send a local notification
   */
  async sendLocalNotification(options: LocalNotificationOptions): Promise<string> {
    return await appNotificationService.sendLocalNotification({
      title: options.title,
      body: options.body,
      data: options.data,
      sound: options.sound,
      badge: options.badge,
      channelId: options.channelId || APP_CHANNEL.DEFAULT.id,
    })
  }

  /**
   * Send an immediate local notification
   */
  async sendImmediateNotification(
    title: string,
    body: string,
    data?: Record<string, any>,
    channelId?: string,
  ): Promise<string> {
    return await appNotificationService.sendImmediateNotification(title, body, data)
  }

  /**
   * Send a scheduled local notification
   */
  async sendScheduledNotification(
    title: string,
    body: string,
    trigger: Notifications.NotificationTriggerInput,
    data?: Record<string, any>,
    channelId?: string,
  ): Promise<string> {
    return await appNotificationService.sendDelayedNotification(title, body, trigger, data)
  }

  /**
   * Cancel all scheduled notifications
   */
  async cancelAllNotifications(): Promise<void> {
    return await appNotificationService.cancelAllNotifications()
  }

  /**
   * Cancel a specific notification
   */
  async cancelNotification(notificationId: string): Promise<void> {
    return await appNotificationService.cancelNotification(notificationId)
  }

  /**
   * Get all scheduled notifications
   */
  async getScheduledNotifications(): Promise<Notifications.NotificationRequest[]> {
    return await appNotificationService.getScheduledNotifications()
  }

  /**
   * Get notification permissions status
   */
  async getPermissionsStatus(): Promise<Notifications.NotificationPermissionsStatus> {
    return await appNotificationService.getPermissionsStatus()
  }

  /**
   * Request notification permissions
   */
  async requestPermissions(): Promise<Notifications.NotificationPermissionsStatus> {
    return await appNotificationService.requestPermissions()
  }

  /**
   * Setup notification channel for Android
   */
  async setupNotificationChannel(
    channelId: string = APP_CHANNEL.DEFAULT.id,
    channelName?: string,
  ): Promise<void> {
    return await appNotificationService.setupNotificationChannel(channelId, channelName)
  }

  // ==================== UNIFIED NOTIFICATION METHODS ====================

  /**
   * Send notification (works for both push and local)
   */
  async sendNotification(
    notification: BaseNotification,
    type: 'local' | 'push' = 'local',
    options?: {
      channelId?: string
      sound?: boolean
      badge?: number
      trigger?: Notifications.NotificationTriggerInput
    },
  ): Promise<string | null> {
    try {
      if (type === 'local') {
        return await this.sendLocalNotification({
          title: notification.title,
          body: notification.description,
          data: notification.data,
          channelId: options?.channelId,
          sound: options?.sound,
          badge: options?.badge,
        })
      } else {
        // For push notifications, you would typically send to a server
        return null
      }
    } catch (error) {
      console.error('Failed to send notification:', error)
      throw error
    }
  }

  /**
   * Send scheduled notification
   */
  async sendScheduledNotificationUnified(
    notification: BaseNotification,
    trigger: Notifications.NotificationTriggerInput,
    options?: {
      channelId?: string
      sound?: boolean
      badge?: number
    },
  ): Promise<string> {
    return await this.sendScheduledNotification(
      notification.title,
      notification.description,
      trigger,
      notification.data,
      options?.channelId,
    )
  }

  // ==================== FCM INTEGRATION METHODS ====================

  /**
   * Setup FCM message handlers
   */
  setupFCMMessageHandlers(
    onMessageForegroundReceived?: (message: any) => void,
    onMessageBackgroundReceived?: (message: any) => void,
    onNotificationOpened?: (message: any) => void,
  ): void {
    if (!this.isFCMAvailable()) {
      console.log('🔥 UnifiedNotificationService: FCM not available, skipping handler setup')
      return
    }

    console.log('🔥 UnifiedNotificationService: Setting up FCM message handlers')

    // Setup foreground message handler
    fcmService.setForegroundMessageHandler((remoteMessage) => {
      if (onMessageForegroundReceived) {
        onMessageForegroundReceived(remoteMessage)
      }
    })

    // Setup background message handler
    fcmService.setBackgroundMessageHandler((remoteMessage) => {
      if (onMessageBackgroundReceived) {
        onMessageBackgroundReceived(remoteMessage)
      }
    })

    // Setup notification opened handler

    fcmService.setNotificationOpenedHandler((remoteMessage) => {
      if (onNotificationOpened) {
        onNotificationOpened(remoteMessage)
      }
    })
  }

  /**
   * Setup FCM token refresh handler
   */
  setupFCMTokenRefreshHandler(onTokenRefresh: (token: string) => void): (() => void) | null {
    if (!this.isFCMAvailable()) {
      return null
    }

    return fcmService.onTokenRefresh(onTokenRefresh)
  }

  /**
   * Request FCM permissions (iOS)
   */
  async requestFCMPermissions(): Promise<any> {
    if (!this.isFCMAvailable()) {
      return null
    }

    try {
      return await fcmService.requestPermission()
    } catch (error) {
      console.error('Failed to request FCM permissions:', error)
      throw error
    }
  }

  /**
   * Check FCM permissions
   */
  async checkFCMPermissions(): Promise<any> {
    if (!this.isFCMAvailable()) {
      return null
    }

    try {
      return await fcmService.checkPermission()
    } catch (error) {
      console.error('Failed to check FCM permissions:', error)
      throw error
    }
  }

  // ==================== NOTIFICATION LISTENERS ====================

  /**
   * Add notification received listener
   */
  addNotificationReceivedListener(
    listener: (notification: Notifications.Notification) => void,
  ): Notifications.EventSubscription {
    return appNotificationService.addNotificationReceivedListener(listener)
  }

  /**
   * Add notification response received listener
   */
  addNotificationResponseReceivedListener(
    listener: (response: Notifications.NotificationResponse) => void,
  ): Notifications.EventSubscription {
    return appNotificationService.addNotificationResponseReceivedListener(listener)
  }

  /**
   * Remove notification listener
   */
  removeNotificationListener(subscription: Notifications.Subscription): void {
    appNotificationService.removeNotificationListener(subscription)
  }

  // ==================== USER PUSH NOTIFICATION  ====================

  /**
   * Get all user push notifications
   */
  async getUserPushNotifications({
    params = {},
    options = {},
  }: {
    params?: Params
    options?: AxiosRequestConfig
  } = {}): Promise<PaginatedDocs<UserPushNotification>> {
    const data = await httpService.get<PaginatedDocs<UserPushNotification>>(
      `/${API_ENDPOINTS.user_push_notifications_api}/list`,
      {
        params,
        ...options,
      },
    )
    return data
  }

  async markNotificationAsViewed(
    payload: MarkNotificationAsViewedPayload,
    options?: AxiosRequestConfig,
  ): Promise<MarkNotificationAsViewedResponse> {
    const response = await httpService.patch<MarkNotificationAsViewedResponse>(
      `/${API_ENDPOINTS.user_push_notifications_api}/mark-as-viewed`,
      payload,
      options,
    )
    return response
  }

  async markAllNotificationAsViewed(
    options?: AxiosRequestConfig,
  ): Promise<MarkNotificationAsViewedResponse> {
    const response = await httpService.post<MarkNotificationAsViewedResponse>(
      `/${API_ENDPOINTS.user_push_notifications_api}/mark-all-as-viewed`,
      {},
      options,
    )
    return response
  }

  async markAllChatNotificationAsViewed(
    options?: AxiosRequestConfig,
  ): Promise<MarkNotificationAsViewedResponse> {
    const response = await httpService.post<MarkNotificationAsViewedResponse>(
      `/${API_ENDPOINTS.user_push_notifications_api}/mark-all-chat-notifications-as-viewed`,
      {},
      options,
    )
    return response
  }

  async getUserUnviewedNotificationCount({
    options = {},
  }: {
    options?: AxiosRequestConfig
  } = {}): Promise<GetUserUnviewedNotificationCountResponse> {
    const data = await httpService.get<GetUserUnviewedNotificationCountResponse>(
      `/${API_ENDPOINTS.user_push_notifications_api}/unviewed-count`,
      {
        ...options,
      },
    )
    return data
  }
}

export const unifiedNotificationService = UnifiedNotificationService.getInstance()
export { UnifiedNotificationService }
