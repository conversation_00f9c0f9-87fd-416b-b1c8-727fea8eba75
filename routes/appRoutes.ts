export enum AppRoutesEnum {
  USER = 'USER',
  MEDICAL_HANDBOOK = 'MEDICAL_HANDBOOK',
  SUBSCRIPTION_MODIFY = 'SUBSCRIPTION_MODIFY',
  HOME = 'HOME',
  SEARCH_SUMMARY = 'SEARCH_SUMMARY',
  LOGIN = 'LOGIN',
  REGISTER = 'REGISTER',
  VERIFY_EMAIL = 'VERIFY_EMAIL',
  RESET_PASSWORD = 'RESET_PASSWORD',
  RESEND_VERIFY_EMAIL = 'RESEND_VERIFY_EMAIL',
  TUTORIAL = 'TUTORIAL',
  EXAMINATION = 'EXAMINATION',
  POSTS = 'POSTS',
  PRODUCTS = 'PRODUCTS',
  PRODUCTS_MEDICINES = 'PRODUCTS_MEDICINES',
  PRODUCTS_SUPPLEMENTS = 'PRODUCTS_SUPPLEMENTS',
  MEDICAL_DICTIONARY = 'MEDICAL_DICTIONARY',
  MEDICAL_DICTIONARY_SEARCH = 'MEDICAL_DICTIONARY_SEARCH',
  MEDICAL_DICTIONARY_KEYWORD_DETAILS = 'MEDICAL_DICTIONARY_KEYWORD_DETAILS',
  MEDICAL_HANDBOOK_FACULTIES = 'MEDICAL_HANDBOOK_FACULTIES',
  MEDICAL_DICTIONARY_CATEGORIES = 'MEDICAL_DICTIONARY_CATEGORIES',
  BODY_PARTS = 'BODY_PARTS',
  CHAT_BOT = 'CHAT_BOT',
  CHAT_BOT_SEARCH_MEDICINE = 'CHAT_BOT_SEARCH_MEDICINE',
  CHAT = 'CHAT',
  FAVORITE_MEDICINES = 'FAVORITE_MEDICINES',
  TRANSACTION_HISTORY = 'TRANSACTION_HISTORY',
  MANAGE_SUBSCRIPTIONS = 'MANAGE_SUBSCRIPTIONS',
  MEDICINE_BODY_PARTS = 'MEDICINE_BODY_PARTS',
  PRODUCTS_LIST = 'PRODUCTS_LIST',
  PRODUCTS_DETAIL = 'PRODUCTS_DETAIL',
  PRODUCTS_DETAIL_V2 = 'PRODUCTS_DETAIL_V2',
  PROFILE = 'PROFILE',
  EDIT_PROFILE = 'EDIT_PROFILE',
  SWITCH_LANGUAGE = 'SWITCH_LANGUAGE',
  CHANGE_PASSWORD = 'CHANGE_PASSWORD',
  FORGOT_PASSWORD = 'FORGOT_PASSWORD',
  SEND_RESET_PASSWORD_EMAIL_SUCCESS = 'SEND_RESET_PASSWORD_EMAIL_SUCCESS',
  PRODUCTS_SEARCH = 'PRODUCTS_SEARCH',
  FAVORITE_PRODUCT = 'FAVORITE_PRODUCT',
  MEDICAL_DICTIONARY_FAVORITE_KEYWORDS = 'MEDICAL_DICTIONARY_FAVORITE_KEYWORDS',
  MEDICAL_DICTIONARY_SEARCH_HISTORY = 'MEDICAL_DICTIONARY_SEARCH_HISTORY',
  MEDICAL_DOCUMENTS = 'MEDICAL_DOCUMENTS',
  MEDICAL_DOCUMENTS_LIST = 'MEDICAL_DOCUMENTS_LIST',
  HOSPITAL_PARTNERS = 'HOSPITAL_PARTNERS',
  HOSPITAL_PARTNERS_DETAILS = 'HOSPITAL_PARTNERS_DETAILS',
  HOSPITAL_EXAMINATION_FORM_LIST = 'HOSPITAL_EXAMINATION_FORM_LIST',
  CREATE_HOSPITAL_EXAMINATION_FORM = 'CREATE_HOSPITAL_EXAMINATION_FORM',
  HOSPITAL_EXAMINATION_FORM = 'HOSPITAL_EXAMINATION_FORM',
  HOSPITAL_PARTNERS_SEARCH = 'HOSPITAL_PARTNERS_SEARCH',
  NOTIFICATIONS = 'NOTIFICATIONS',
  USER_PUSH_NOTIFICATIONS = 'USER_PUSH_NOTIFICATIONS',
  NOTIFICATION_DETAILS = 'NOTIFICATION_DETAILS',
  CHAT_WOOT = 'CHAT_WOOT',
  COMMENTS = 'COMMENTS',
  COMMENTS_DETAILS = 'COMMENTS_DETAILS',
  REPORT_COMMENT = 'REPORT_COMMENT',
  MEDICAL_FACULTIES = 'MEDICAL_FACULTIES'
}

export const APP_ROUTES: Record<
  string,
  {
    path: string
    name: string
    tabName?: string
    tabPath?: string
    authRequired: boolean | null
    children?: Record<string, any>
    label?: Record<string, string>
  }
> = {
  [AppRoutesEnum.HOME]: {
    path: '/',
    name: 'index',
    authRequired: false,
  },
  [AppRoutesEnum.USER]: {
    path: '/user',
    name: 'user',
    authRequired: true,
  },
  [AppRoutesEnum.MEDICAL_HANDBOOK]: {
    path: '/medical-handbook',
    name: 'medical-handbook',
    authRequired: true,
    tabName: 'medical-handbook-tab',
    tabPath: '/medical-handbook-tab',
  },
  [AppRoutesEnum.BODY_PARTS]: {
    path: '/body-parts',
    name: 'body-parts',
    authRequired: true,
  },
  [AppRoutesEnum.PRODUCTS]: {
    path: '/products',
    name: 'products',
    authRequired: false,
    tabName: 'products-tab',
    tabPath: '/products-tab',
    children: {
      [AppRoutesEnum.MEDICINE_BODY_PARTS]: {
        path: '/products/medicine-body-parts',
        name: 'products-medicine-body-parts',
        authRequired: null,
      },
      [AppRoutesEnum.PRODUCTS_LIST]: {
        path: '/products/list',
        name: 'products-list',
        authRequired: null,
      },
      [AppRoutesEnum.PRODUCTS_DETAIL]: {
        path: '/products/detail',
        name: 'products-detail',
        authRequired: null,
      },
      [AppRoutesEnum.PRODUCTS_DETAIL_V2]: {
        path: '/products/detail-v2',
        name: 'products-detail-v2',
        authRequired: null,
      },
      [AppRoutesEnum.PRODUCTS_SEARCH]: {
        path: '/products/search',
        name: 'products-search',
        authRequired: null,
      },
    },
  },
  [AppRoutesEnum.PRODUCTS_MEDICINES]: {
    path: '/products/medicines',
    name: 'products-medicines',
    authRequired: true,
  },
  [AppRoutesEnum.MEDICAL_DICTIONARY]: {
    path: '/medical-dictionary',
    name: 'medical-dictionary',
    authRequired: null,
    tabName: 'medical-dictionary-tab',
    tabPath: '/medical-dictionary-tab',
    children: {
      [AppRoutesEnum.MEDICAL_DICTIONARY_CATEGORIES]: {
        path: '/medical-dictionary/categories',
        name: 'medical-dictionary-categories',
        authRequired: null,
      },
      [AppRoutesEnum.MEDICAL_DICTIONARY_SEARCH]: {
        path: '/medical-dictionary/search',
        name: 'medical-dictionary-search',
        authRequired: null,
      },
      [AppRoutesEnum.MEDICAL_DICTIONARY_KEYWORD_DETAILS]: {
        path: '/medical-dictionary/keyword-details',
        name: 'medical-dictionary-keyword-details',
        authRequired: null,
      },
      [AppRoutesEnum.MEDICAL_DICTIONARY_FAVORITE_KEYWORDS]: {
        path: '/medical-dictionary/favorite-keywords',
        name: 'medical-dictionary-favorite-keywords',
        authRequired: true,
      },
      [AppRoutesEnum.MEDICAL_DICTIONARY_SEARCH_HISTORY]: {
        path: '/medical-dictionary/search-history',
        name: 'medical-dictionary-search-history',
        authRequired: true,
      },
    },
  },
  [AppRoutesEnum.MEDICAL_HANDBOOK_FACULTIES]: {
    path: '/medical',
    name: 'medical',
    authRequired: true,
  },
  [AppRoutesEnum.POSTS]: {
    path: '/posts',
    name: 'posts',
    authRequired: true,
    tabName: 'posts-tab',
    tabPath: '/posts-tab',
  },
  [AppRoutesEnum.CHAT]: {
    path: '/chat',
    name: 'chat',
    authRequired: true,
    tabName: 'chat-tab',
    tabPath: '/chat-tab',
  },
  [AppRoutesEnum.CHAT_BOT]: {
    path: '/chatbot',
    name: 'chatbot',
    authRequired: true,
    children: {
      [AppRoutesEnum.CHAT_BOT_SEARCH_MEDICINE]: {
        path: '/chatbot/search-medicine',
        name: 'chatbot-search-medicine',
        authRequired: true,
      },
      [AppRoutesEnum.CHAT_WOOT]: {
        path: '/chatbot/chatwoot',
        name: 'chatbot-chatwoot',
        authRequired: true,
      },
    },
  },
  [AppRoutesEnum.EXAMINATION]: {
    path: '/examination',
    name: 'examination',
    authRequired: true,
  },
  [AppRoutesEnum.SEARCH_SUMMARY]: {
    path: '/search-summary',
    name: 'search-summary',
    authRequired: true,
  },
  [AppRoutesEnum.PROFILE]: {
    path: '/user/profile',
    name: 'profile',
    authRequired: true,
  },
  [AppRoutesEnum.EDIT_PROFILE]: {
    path: '/user/profile/edit-profile',
    name: 'edit-profile',
    authRequired: true,
  },
  [AppRoutesEnum.SWITCH_LANGUAGE]: {
    path: '/user/profile/switch-language',
    name: 'switch-language',
    authRequired: true,
  },
  [AppRoutesEnum.LOGIN]: {
    path: '/(auth)/login',
    name: 'login',
    authRequired: false,
  },
  [AppRoutesEnum.REGISTER]: {
    path: '/(auth)/register',
    name: 'register',
    authRequired: false,
  },
  [AppRoutesEnum.VERIFY_EMAIL]: {
    path: '/(auth)/verify-email',
    name: 'verify-email',
    authRequired: false,
  },
  [AppRoutesEnum.FAVORITE_MEDICINES]: {
    path: '/user/favorite-medicine',
    name: 'favorite-medicine',
    authRequired: true,
  },
  [AppRoutesEnum.CHANGE_PASSWORD]: {
    path: '/user/profile/change-password',
    name: 'change-password',
    authRequired: true,
  },
  [AppRoutesEnum.FORGOT_PASSWORD]: {
    path: '/(auth)/forgot-password',
    name: 'forgot-password',
    authRequired: false,
  },
  [AppRoutesEnum.SEND_RESET_PASSWORD_EMAIL_SUCCESS]: {
    path: '/(auth)/forgot-password/send-reset-password-email-success',
    name: 'send-reset-password-email-success',
    authRequired: false,
  },
  [AppRoutesEnum.FAVORITE_PRODUCT]: {
    path: '/user/favorite-product',
    name: 'favorite-product',
    authRequired: true,
  },
  [AppRoutesEnum.MEDICAL_DOCUMENTS]: {
    path: '/medical-documents',
    name: 'medical-documents',
    authRequired: null,
    children: {
      [AppRoutesEnum.MEDICAL_DOCUMENTS_LIST]: {
        path: '/medical-documents/list',
        name: 'medical-documents-list',
        authRequired: null,
      },
    },
  },
  [AppRoutesEnum.HOSPITAL_PARTNERS]: {
    path: '/hospital-partners',
    name: 'hospital-partners',
    authRequired: null,
    children: {
      [AppRoutesEnum.HOSPITAL_PARTNERS_DETAILS]: {
        path: '/hospital-partners/details',
        name: 'hospital-partners-detail',
        authRequired: null,
      },
      [AppRoutesEnum.HOSPITAL_EXAMINATION_FORM_LIST]: {
        path: '/hospital-partners/examination-form-list',
        name: 'hospital-partners-examination-form-list',
        authRequired: true,
      },
      [AppRoutesEnum.CREATE_HOSPITAL_EXAMINATION_FORM]: {
        path: '/hospital-partners/create-examination-form',
        name: 'create-hospital-examination-form',
        authRequired: true,
      },
      [AppRoutesEnum.HOSPITAL_EXAMINATION_FORM]: {
        path: '/hospital-partners/examination-form',
        name: 'hospital-partners-examination-form',
        authRequired: true,
      },
      [AppRoutesEnum.HOSPITAL_PARTNERS_SEARCH]: {
        path: '/hospital-partners/search',
        name: 'hospital-partners-search',
        authRequired: null,
      },
    },
  },
  [AppRoutesEnum.NOTIFICATIONS]: {
    path: '/notifications',
    name: 'notifications',
    authRequired: true,
    children: {
      [AppRoutesEnum.USER_PUSH_NOTIFICATIONS]: {
        path: '/notifications/user-push-notifications',
        name: 'user-push-notifications',
        authRequired: true,
      },
      [AppRoutesEnum.NOTIFICATION_DETAILS]: {
        path: '/notifications/details',
        name: 'notifications-details',
        authRequired: true,
      },
    },
  },
  [AppRoutesEnum.COMMENTS]: {
    path: '/comments',
    name: 'comments',
    authRequired: null,
    children: {
      [AppRoutesEnum.COMMENTS_DETAILS]: {
        path: '/comments/details',
        name: 'comments-details',
        authRequired: null,
      },
      [AppRoutesEnum.REPORT_COMMENT]: {
        path: '/comments/report-comment',
        name: 'report-comment',
        authRequired: true,
      },
    },
  },
  [AppRoutesEnum.MEDICAL_FACULTIES]: {
    path: '/medical-faculties',
    name: 'medical-faculties',
    authRequired: null,
  },
}

// export const PROTECTED_WEBVIEW_APP_ROUTES: Record<string, string> = Object.fromEntries(
//   Object.entries(APP_ROUTES)
//     .filter(([_, route]) => route.authRequired === true)
//     .map(([key, route]) => [key, route.path]),
// )

// export const GUEST_ONLY_WEBVIEW_APP_ROUTES: Record<string, string> = Object.fromEntries(
//   Object.entries(APP_ROUTES)
//     .filter(([_, route]) => route.authRequired === false)
//     .map(([key, route]) => [key, route.path]),
// )
