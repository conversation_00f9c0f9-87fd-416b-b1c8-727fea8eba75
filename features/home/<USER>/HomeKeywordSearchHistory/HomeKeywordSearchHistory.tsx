import MagnifyingGlass from '@/assets/icons/magnifying-glass-icon.svg'
import { Skeleton } from '@/components/ui/Skeleton/Skeleton'
import { Text } from '@/components/ui/Text/Text'
import {
  KeywordSearchHistoryBadgeList,
  KeywordSearchHistoryBadgeListLoading,
} from '@/features/medical-dictionary/components/KeywordSearchHistoryBadgeList/KeywordSearchHistoryBadgeList'
import { useGetKeywordSearchHistoryByUser } from '@/features/medical-dictionary/hooks/query/useGetKeywordSearchHistoryByUser'
import { useAuthentication } from '@/hooks/auth/useAuthentication'
import { APP_ROUTES, AppRoutesEnum } from '@/routes/appRoutes'
import { Link } from 'expo-router'
import { useTranslation } from 'react-i18next'
import { TouchableOpacity, View } from 'react-native'
export function HomeKeywordSearchHistory() {
  const { user, status } = useAuthentication()
  const { t } = useTranslation()

  const {
    keywordSearchHistoryByUser,
    isGetKeywordSearchHistoryByUserLoading,
    isFetching,

    isRefetching,
  } = useGetKeywordSearchHistoryByUser({
    params: {
      locale: 'all',
      limit: 5,
      populate: {
        keywords: {
          name: true,
        },
      },
    },
    useQueryOptions: {
      staleTime: 3 * 60 * 1000,
      enabled: Boolean(status === 'success' && user),
    },
  })
  // Show loading when authentication status is loading
  if (status === 'loading') {
    return <HomeKeywordSearchHistoryLoading />
  }

  // If no user or unauthorized, return null
  if (!user || status === 'unauthorized') return null

  // Show loading when fetching keyword search history
  if (isGetKeywordSearchHistoryByUserLoading || isFetching) {
    return <HomeKeywordSearchHistoryLoading />
  }

  // If no search history data, return null
  if (!keywordSearchHistoryByUser?.docs?.length) return null

  const isLoading = isGetKeywordSearchHistoryByUserLoading || isFetching || isRefetching

  return (
    <View className="flex flex-col gap-y-3 px-4">
      <View className="flex flex-row items-center justify-between gap-x-2">
        <View className="flex flex-row items-center gap-x-2">
          <Text size="heading8" variant="primary">
            {t('MES-776')}
          </Text>
          <MagnifyingGlass width={24} height={24} />
        </View>
        <Link
          href={
            APP_ROUTES.MEDICAL_DICTIONARY.children?.[
              AppRoutesEnum.MEDICAL_DICTIONARY_SEARCH_HISTORY
            ].path
          }
          asChild
        >
          <TouchableOpacity>
            <Text size="link3" variant="primary">
              {t('MES-141')}
            </Text>
          </TouchableOpacity>
        </Link>
      </View>
      <KeywordSearchHistoryBadgeList
        keywordSearchHistory={keywordSearchHistoryByUser?.docs}
        isLoading={isLoading}
        limitWidth={240}
      />
    </View>
  )
}

const HomeKeywordSearchHistoryLoading = () => {
  return (
    <View className="flex flex-col gap-y-4 px-4">
      <Skeleton className="h-8 w-[140px]" />
      <KeywordSearchHistoryBadgeListLoading />
    </View>
  )
}
