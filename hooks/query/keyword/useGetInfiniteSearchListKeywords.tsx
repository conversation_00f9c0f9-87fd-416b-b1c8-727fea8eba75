import { Params } from '@/types/http.type'
import { InfiniteData, UseInfiniteQueryOptions, useInfiniteQuery } from '@tanstack/react-query'

import { keywordService } from '@/services/handbook/keyword.service'
import { PaginatedDocs } from '@/types/global.type'
import { Keyword } from '@/types/keyword.type'
import { AxiosRequestConfig } from 'axios'
import { keywordQueryKeys } from './queryKeys'

type KeywordQueryConfig = Omit<
  UseInfiniteQueryOptions<
    PaginatedDocs<Pick<Keyword, 'id' | 'name'>>,
    Error,
    InfiniteData<PaginatedDocs<Pick<Keyword, 'id' | 'name'>>>,
    (string | Params)[],
    number
  >,
  'queryFn' | 'queryKey' | 'getNextPageParam' | 'initialPageParam'
>

interface UseGetInfiniteSearchListKeywordsProps {
  params?: Params
  options?: AxiosRequestConfig
  key?: string | number
  config?: KeywordQueryConfig
  overrideKey?: string[]
}

/**
 * Custom hook for fetching paginated search list keywords data
 * @param params - Query parameters for filtering and pagination
 * @param options - Request initialization options
 * @param key - Optional cache key
 * @param config - React Query configuration options
 * @param overrideKey - Override the query key
 * @returns Object containing keyword data and pagination controls
 */
export const useGetInfiniteSearchListKeywords = ({
  params = {},
  options = {},
  config = {},
  overrideKey,
}: UseGetInfiniteSearchListKeywordsProps = {}) => {
  const {
    isError: isGetSearchListKeywordsError,
    isFetching: isGetSearchListKeywordsLoading,
    data: searchListKeywords,
    fetchNextPage,
    hasNextPage,
    ...rest
  } = useInfiniteQuery({
    queryKey: overrideKey ? overrideKey : [keywordQueryKeys['keywords'].base(), params],
    queryFn: async ({ pageParam = 1 }) => {
      return keywordService.getSearchListKeywords({
        params: {
          ...params,
          page: pageParam,
        },
        options,
      })
    },
    getNextPageParam: (lastPage) => lastPage?.nextPage,
    getPreviousPageParam: (lastPage) => lastPage?.prevPage,
    initialPageParam: 1,
    ...config,
  })

  return {
    isGetSearchListKeywordsError,
    isGetSearchListKeywordsLoading,
    searchListKeywords,
    fetchNextPage,
    hasNextPage,
    ...rest,
  }
}
