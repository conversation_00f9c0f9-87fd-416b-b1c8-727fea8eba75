import { useAuthentication } from '@/hooks/auth/useAuthentication'
import { APP_ROUTES } from '@/routes/appRoutes'
import { primary } from '@/styles/_colors'
import { LinkProps, useLocalSearchParams, usePathname, useRouter } from 'expo-router'
import React, { useEffect } from 'react'
import { ActivityIndicator, View } from 'react-native'

export function withAuthentication<P extends object>(Component: React.ComponentType<P>) {
  return function AuthenticatedComponent(props: P) {
    const router = useRouter()
    const pathname = usePathname()
    const searchParams = useLocalSearchParams()

    const { status, user } = useAuthentication()

    useEffect(() => {
      // Redirect to login if not authenticated and not loading
      if (status === 'unauthorized' || status === 'failure') {
        const queryString = Object.entries(searchParams)
          .map(([key, value]) => `${key}=${value}`)
          .join('&')

        const fullUrl = queryString ? `${pathname}?${queryString}` : pathname
        if (searchParams?.overrideRedirect && typeof searchParams?.overrideRedirect === 'string') {
          router.replace({
            pathname: (searchParams?.overrideRedirect as string) || APP_ROUTES.HOME.path,
          } as LinkProps['href'])
          return
        }

        router.replace({
          pathname: APP_ROUTES.LOGIN.path,
          params: {
            redirect: fullUrl,
          },
        } as LinkProps['href'])
      }
    }, [router, status, searchParams, pathname])

    // Show loading indicator while authentication is loading
    if (status === 'loading') {
      return (
        <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center' }}>
          <ActivityIndicator size="small" color={primary[500]} />
        </View>
      )
    }

    // Don't render component if not authenticated
    if (status !== 'success' || !user) {
      return null
    }

    // Render the wrapped component if authenticated
    return <Component {...props} />
  }
}
