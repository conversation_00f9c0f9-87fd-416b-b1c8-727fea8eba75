import { LocaleEnum } from '@/enums/locale.enum'
import { CommentReportReasonsEnum } from '../enums'

export const COMMENT_REPORT_REASON_OPTIONS: Record<
  CommentReportReasonsEnum,
  {
    value: string
    label: { [key in LocaleEnum]: string }
    baseLabel: string
    translationKey: string
  }
> = {
  [CommentReportReasonsEnum.OFFENSIVE_OR_HARMFUL]: {
    value: CommentReportReasonsEnum.OFFENSIVE_OR_HARMFUL,
    label: {
      [LocaleEnum.VI]: '<PERSON><PERSON><PERSON> phạm, gây hại đến người khác',
      [LocaleEnum.JA]: '他人を侮辱・傷つける内容',
    },
    baseLabel: 'Offensive or Harmful',
    translationKey: 'MES-921',
  },
  [CommentReportReasonsEnum.UNETHICAL_OR_INAPPROPRIATE]: {
    value: CommentReportReasonsEnum.UNETHICAL_OR_INAPPROPRIATE,
    label: {
      [LocaleEnum.VI]: 'Vi phạm đạo đức, thuần phong mỹ tục',
      [LocaleEnum.JA]: '倫理や公序良俗に反する内容',
    },
    baseLabel: 'Unethical or Inappropriate',
    translationKey: 'MES-922',
  },
  [CommentReportReasonsEnum.FALSE_INFORMATION]: {
    value: CommentReportReasonsEnum.FALSE_INFORMATION,
    label: {
      [LocaleEnum.VI]: 'Sai thông tin, sai sự thật',
      [LocaleEnum.JA]: '虚偽または誤った情報',
    },
    baseLabel: 'False Information',
    translationKey: 'MES-923',
  },
  [CommentReportReasonsEnum.SPAM_OR_ADVERTISEMENT]: {
    value: CommentReportReasonsEnum.SPAM_OR_ADVERTISEMENT,
    label: {
      [LocaleEnum.VI]: 'Spam, quảng cáo',
      [LocaleEnum.JA]: 'スパム・広告',
    },
    baseLabel: 'Spam or Advertisement',
    translationKey: 'MES-924',
  },
  [CommentReportReasonsEnum.OTHER]: {
    value: CommentReportReasonsEnum.OTHER,
    label: {
      [LocaleEnum.VI]: 'Khác (Vui lòng chú thích rõ)',
      [LocaleEnum.JA]: 'その他（詳細をご記入ください）',
    },
    baseLabel: 'Other',
    translationKey: 'MES-925',
  },
}

export const MAX_COMMENT_LENGTH = 512

export const MAX_VISIBLE_COMMENT_LENGTH = 320
