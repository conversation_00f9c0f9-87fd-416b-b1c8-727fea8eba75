import DrugPillV2Icon from '@/assets/icons/drug-pill-v2-icon.svg'
import HealthCareIcon from '@/assets/icons/health-care-icon.svg'
import { Skeleton } from '@/components/ui/Skeleton/Skeleton'
import { Ta<PERSON>, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/Tabs/Tabs'
import { Text } from '@/components/ui/Text/Text'
import { BLURHASH_CODE, MAX_APP_WIDTH } from '@/constants/global.constant'
import { PRODUCT_V2_TYPE_OPTIONS } from '@/features/product/constants'
import { ProductV2TypeEnum } from '@/features/product/enums'
import { useGetProductCategories } from '@/features/product/hooks/query/product/useGetProductCategories'
import { ProductCategory } from '@/features/product/types'
import { useAppLanguage } from '@/hooks/common/useAppLanguage'
import { StyledExpoImage } from '@/libs/styled'
import { APP_ROUTES } from '@/routes/appRoutes'
import { Media } from '@/types/media.type'
import { cn } from '@/utils/cn'
import * as Haptics from 'expo-haptics'
import { LinkProps, router } from 'expo-router'
import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react'
import { useTranslation } from 'react-i18next'
import { Dimensions, FlatList, ScrollView, TouchableOpacity, View } from 'react-native'

const PANEL_ITEMS = {
  MEDICINE: {
    id: ProductV2TypeEnum.MEDICINE,
    title: PRODUCT_V2_TYPE_OPTIONS[ProductV2TypeEnum.MEDICINE].translationKey,
  },
  DIETARY_SUPPLEMENT: {
    id: ProductV2TypeEnum.DIETARY_SUPPLEMENT,
    title: PRODUCT_V2_TYPE_OPTIONS[ProductV2TypeEnum.DIETARY_SUPPLEMENT].translationKey,
  },
  MEDICAL_INSTRUMENT: {
    id: ProductV2TypeEnum.MEDICAL_INSTRUMENT,
    title: PRODUCT_V2_TYPE_OPTIONS[ProductV2TypeEnum.MEDICAL_INSTRUMENT].translationKey,
  },
}

const { width: screenWidth } = Dimensions.get('window')

const actualWidth = screenWidth

export const HomeProducts = () => {
  const { t } = useTranslation()
  const { primaryLanguage } = useAppLanguage()
  const [selectedCategory, setSelectedCategory] = useState(PANEL_ITEMS.MEDICINE.id)
  const scrollViewRef = useRef<ScrollView>(null)
  const tabRefs = useRef<{ [key: string]: View | null }>({})

  // State for grid configuration based on actual available width
  const [itemsPerPage, setItemsPerPage] = useState(screenWidth > MAX_APP_WIDTH ? 12 : 9)
  const [columns, setColumns] = useState(screenWidth > MAX_APP_WIDTH ? 4 : 3)

  const { productCategories, isGetProductCategoriesLoading } = useGetProductCategories({
    params: {
      locale: primaryLanguage,
      limit: 50,
      depth: 1,
      draft: false,
      where: {
        and: [
          {
            type: {
              equals: selectedCategory,
            },
            parent: {
              equals: null,
            },
          },
        ],
      },
    },
    useQueryOptions: {
      staleTime: 5 * 60 * 1000,
    },
  })

  const tabTriggerClass = useMemo(() => {
    return {
      default:
        'self-start min-w-[67px] border border-transparent rounded-[60px] overflow-hidden bg-custom-neutral-80 px-3 py-1 text-center',
      active: 'border border-primary bg-white text-primary',
      'text-default': 'text-custom-text-subdued text-center',
      'text-active': 'text-primary-500',
    }
  }, [])

  // Auto-scroll to active tab
  useEffect(() => {
    if (scrollViewRef.current && tabRefs.current[selectedCategory]) {
      setTimeout(() => {
        const activeTabRef = tabRefs.current[selectedCategory]
        if (activeTabRef) {
          activeTabRef.measureLayout(
            scrollViewRef.current as any,
            (x) => {
              // Scroll to center the active tab
              const scrollX = Math.max(0, x - 50) // 50px offset for better visibility
              scrollViewRef.current?.scrollTo({
                x: scrollX,
                animated: true,
              })
            },
            () => {
              // Fallback to simple scroll if measureLayout fails
              console.warn('Failed to measure tab layout')
            },
          )
        }
      }, 100)
    }
  }, [selectedCategory])

  const handleCategoryPress = useCallback(
    (category: ProductCategory) => {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light)

      // Navigate to products screen with query parameters
      router.push({
        pathname: APP_ROUTES.PRODUCTS.tabPath,
        params: {
          categoryId: category.id,
          productType: selectedCategory,
          categoryTitle: category.title,
          source: 'home', // Track that we're navigating from home
        },
      } as LinkProps['href'])
    },
    [selectedCategory],
  )

  return (
    <View className="flex-1 px-4">
      <View className="mb-3 flex-row items-center gap-3">
        <Text size="heading8" variant="primary">
          {t('MES-558')}
        </Text>
        <DrugPillV2Icon className="h-6 w-6" width={24} height={24} />
      </View>

      <Tabs
        value={selectedCategory}
        onValueChange={(value) => {
          Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light)
          setSelectedCategory(value as ProductV2TypeEnum)
        }}
      >
        <ScrollView ref={scrollViewRef} horizontal showsHorizontalScrollIndicator={false}>
          <TabsList className="flex-1 flex-row gap-x-2 self-start">
            {Object.values(PANEL_ITEMS).map((category) => (
              <TabsTrigger
                key={category.id}
                value={category.id}
                isActive={selectedCategory === category.id}
              >
                <View
                  ref={(ref) => {
                    tabRefs.current[category.id] = ref
                  }}
                  className={cn(
                    tabTriggerClass['default'],
                    selectedCategory === category.id &&
                      'border !border-primary !bg-white text-primary',
                  )}
                >
                  <Text
                    size="body6"
                    className={cn(
                      tabTriggerClass['text-default'],
                      selectedCategory === category.id && tabTriggerClass['text-active'],
                    )}
                  >
                    {t(category.title)}
                  </Text>
                </View>
              </TabsTrigger>
            ))}
          </TabsList>
        </ScrollView>

        <TabsContent value={PANEL_ITEMS.MEDICINE.id} className="mt-4">
          {isGetProductCategoriesLoading ? (
            <ListLoading columns={columns} />
          ) : (
            <CardItem
              data={productCategories?.docs || []}
              tab={PANEL_ITEMS.MEDICINE}
              itemsPerPage={itemsPerPage}
              columns={columns}
              onPressCategory={handleCategoryPress}
            />
          )}
        </TabsContent>

        <TabsContent value={PANEL_ITEMS.DIETARY_SUPPLEMENT.id} className="mt-4">
          {isGetProductCategoriesLoading ? (
            <ListLoading columns={columns} />
          ) : (
            <CardItem
              data={productCategories?.docs || []}
              tab={PANEL_ITEMS.DIETARY_SUPPLEMENT}
              itemsPerPage={itemsPerPage}
              columns={columns}
              onPressCategory={handleCategoryPress}
            />
          )}
        </TabsContent>

        <TabsContent value={PANEL_ITEMS.MEDICAL_INSTRUMENT.id} className="mt-4">
          {isGetProductCategoriesLoading ? (
            <ListLoading columns={columns} />
          ) : (
            <CardItem
              data={productCategories?.docs || []}
              tab={PANEL_ITEMS.MEDICAL_INSTRUMENT}
              itemsPerPage={itemsPerPage}
              columns={columns}
              onPressCategory={handleCategoryPress}
            />
          )}
        </TabsContent>
      </Tabs>
    </View>
  )
}

interface ListLoadingProps {
  columns: number
}

const ListLoading = ({ columns }: ListLoadingProps) => {
  const skeletonGrid = Array.from({ length: 3 }, () => new Array(columns).fill(0))

  return (
    <View className="flex-1 flex-col gap-3 px-4">
      {skeletonGrid.map((row, rowIndex) => (
        <View key={rowIndex} className="flex-row justify-between gap-3">
          {row.map((_, colIndex) => (
            <View className="flex-1 flex-row items-center justify-center p-3" key={colIndex}>
              <Skeleton className="aspect-square h-[50px] rounded-lg bg-gray-200" />
            </View>
          ))}
        </View>
      ))}
    </View>
  )
}

interface CardItemProps {
  tab: (typeof PANEL_ITEMS)[keyof typeof PANEL_ITEMS]
  data: ProductCategory[]
  itemsPerPage: number
  columns: number
  onPressCategory: (category: ProductCategory) => void
}
const CardItem = ({ data, tab, itemsPerPage, columns, onPressCategory }: CardItemProps) => {
  const groupedData = useMemo(() => {
    const groups: ProductCategory[][] = []
    for (let i = 0; i < (data?.length || 0); i += itemsPerPage) {
      groups.push(data?.slice(i, i + itemsPerPage) || [])
    }
    return groups
  }, [data, itemsPerPage])

  const renderGroupedData = ({
    item: pageItems,
    tabId,
  }: {
    item: ProductCategory[]
    tabId: string
  }) => {
    return (
      <View style={{ width: actualWidth - 32 }} className="mx-auto flex-1">
        <FlatList
          data={pageItems}
          renderItem={({ item }) => renderItem({ item, tabId })}
          numColumns={columns}
          columnWrapperStyle={{ gap: 12 }}
          contentContainerStyle={{
            paddingHorizontal: 8,
          }}
          scrollEnabled={false}
          keyExtractor={(item, index) => `${item.id}-${index}`}
        />
      </View>
    )
  }

  const renderItem = useCallback(
    ({ item, tabId }: { item: ProductCategory; tabId: string }) => {
      const thumbnailMedia = item.icon as Media
      const iconUrl = thumbnailMedia?.url || thumbnailMedia?.thumbnailURL || ''
      const title = item.title

      return (
        <TouchableOpacity
          className=" flex-col items-center justify-center "
          style={{
            width: (actualWidth - 32) / columns - 12,
          }}
          onPress={() => {
            Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light)
            onPressCategory(item)
          }}
        >
          <View className="mb-2 h-[50px] w-[50px] flex-row items-center justify-center rounded-[16px] bg-primary-50">
            {iconUrl ? (
              <StyledExpoImage
                source={iconUrl}
                contentFit="cover"
                transition={300}
                placeholder={BLURHASH_CODE}
                className="h-8 w-8"
              />
            ) : (
              <HealthCareIcon className="h-8 w-8" />
            )}
          </View>
          <Text
            size="body8"
            className="line-clamp-2 min-h-[40px] px-1 text-center"
            numberOfLines={2}
          >
            {title}
          </Text>
        </TouchableOpacity>
      )
    },
    [onPressCategory],
  )

  return (
    <FlatList
      horizontal
      pagingEnabled
      data={groupedData}
      renderItem={({ item }) => renderGroupedData({ item, tabId: tab.id })}
      showsHorizontalScrollIndicator={false}
      decelerationRate="fast"
      snapToInterval={actualWidth - 32}
      snapToAlignment="start"
      contentContainerStyle={{
        gap: 0,
      }}
      keyExtractor={(_, index) => `page-${index}-${tab.id}`}
      key={`${tab.id}-${columns}`}
    />
  )
}
