import { Params } from '@/types/http.type'
import { InfiniteData, UseInfiniteQueryOptions, useInfiniteQuery } from '@tanstack/react-query'

import { PaginatedDocs } from '@/types/global.type'
import { AxiosRequestConfig } from 'axios'

import { medicalDocumentService } from '../services/medical-document.service'
import { MedicalDocument } from '../types'
import { medicalDocumentsQueryKeys } from './queryKeys'

export type MedicalDocumentsQueryConfig = Omit<
  UseInfiniteQueryOptions<
    PaginatedDocs<MedicalDocument>,
    Error,
    InfiniteData<PaginatedDocs<MedicalDocument>>,
    (string | Params)[],
    number
  >,
  'queryFn' | 'queryKey' | 'getNextPageParam' | 'initialPageParam'
>

interface UseGetInfiniteMedicalDocumentsProps {
  params?: Params
  options?: AxiosRequestConfig
  key?: string | number
  config?: MedicalDocumentsQueryConfig
  overrideKey?: (string | Params)[]
}

export const useGetInfiniteMedicalDocuments = ({
  params = {},
  options = {},
  config = {},
  overrideKey,
}: UseGetInfiniteMedicalDocumentsProps = {}) => {
  const {
    isError: isGetMedicalDocumentsError,
    isFetching: isGetMedicalDocumentsLoading,
    data: medicalDocuments,
    fetchNextPage,
    hasNextPage,
    ...rest
  } = useInfiniteQuery({
    queryKey: overrideKey
      ? overrideKey
      : [medicalDocumentsQueryKeys['medical-documents'].base(), params],
    queryFn: async ({ pageParam = 1 }) => {
      return medicalDocumentService.getMedicalDocuments({
        params: {
          ...params,
          page: pageParam,
        },
        options,
      })
    },
    getNextPageParam: (lastPage) => lastPage?.nextPage,
    getPreviousPageParam: (lastPage) => lastPage?.prevPage,
    initialPageParam: 1,
    ...config,
  })

  return {
    isGetMedicalDocumentsError,
    isGetMedicalDocumentsLoading,
    medicalDocuments,
    fetchNextPage,
    hasNextPage,
    ...rest,
  }
}
