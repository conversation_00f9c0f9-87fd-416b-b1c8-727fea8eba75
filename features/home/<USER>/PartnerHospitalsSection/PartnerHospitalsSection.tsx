import HospitalIcon from '@/assets/icons/hospital-icon.svg'
import { Text } from '@/components/ui/Text/Text'
import { APP_ROUTES } from '@/routes/appRoutes'
import { LinearGradient } from 'expo-linear-gradient'
import { Link, LinkProps } from 'expo-router'
import React from 'react'
import { useTranslation } from 'react-i18next'
import { TouchableOpacity, View } from 'react-native'
export const PartnerHospitalsSection = () => {
  const { t } = useTranslation()
  return (
    <View className="px-4">
      <View className="relative">
        <Link href={APP_ROUTES.HOSPITAL_PARTNERS.path as LinkProps['href']} asChild>
          <TouchableOpacity className="relative  rounded-lg p-1">
            <LinearGradient
              colors={['#8295FF', '#3AA6FF']}
              start={{ x: 0.999, y: 1.046 }}
              end={{ x: 0.02, y: 0.085 }}
              className="relative flex  p-4"
              style={{
                paddingHorizontal: 24,
                paddingVertical: 12,
                flexDirection: 'row',
                justifyContent: 'space-between',
                alignItems: 'center',
                borderRadius: 8,
              }}
            >
              <View className="flex-row items-center justify-between gap-2">
                <HospitalIcon width={26} height={26} />
                <Text size="body3" variant="white">
                  {t('MES-804')}
                </Text>
              </View>
              <Text size="body6" variant="white">
                {t('MES-806')}
              </Text>
            </LinearGradient>
          </TouchableOpacity>
        </Link>
      </View>
    </View>
  )
}
