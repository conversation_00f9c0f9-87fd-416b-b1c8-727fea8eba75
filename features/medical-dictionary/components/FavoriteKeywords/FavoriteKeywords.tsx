import EmptyBoxIcon from '@/assets/icons/empty-box.svg'
import { SearchInput, SearchInputRef } from '@/components/ui/SearchInput/SearchInput'
import { Skeleton } from '@/components/ui/Skeleton/Skeleton'
import { Text } from '@/components/ui/Text/Text'
import { LocaleEnum } from '@/enums/locale.enum'
import { keywordQueryKeys } from '@/hooks/query/keyword/queryKeys'
import { useGetInfiniteFavoriteKeywords } from '@/hooks/query/keyword/useGetInfiniteFavoriteKeywords'
import colors from '@/styles/_colors'
import { FavoriteKeyword, KeywordV2 } from '@/types/keyword.type'
import { useQueryClient } from '@tanstack/react-query'
import { useCallback, useEffect, useMemo, useRef, useState } from 'react'
import { useTranslation } from 'react-i18next'
import { ActivityIndicator, FlatList, RefreshControl, View } from 'react-native'
import { useShallow } from 'zustand/react/shallow'
import { FAVORITE_KEYWORDS_SORT_BY } from '../../enums'
import { useMedicalDictionaryStore } from '../../stores/MedicalDictionaryStore'
import { KeywordItem } from '../KeywordItem/KeywordItem'
import { SortButton } from '../SortButton'

type ListItem =
  | { type: 'favorite_keyword'; favoriteKeyword: FavoriteKeyword }
  | { type: 'loading' }
  | { type: 'loading_skeleton' }

export const FavoriteKeywords = () => {
  const { t } = useTranslation()
  const queryClient = useQueryClient()
  const [searchInputValue, setSearchInputValue] = useState('')
  const [searchQuery, setSearchQuery] = useState('')
  const [refreshing, setRefreshing] = useState(false)
  const [localFavoriteKeywords, setLocalFavoriteKeywords] = useState<FavoriteKeyword[]>([])
  const [totalFavoriteKeywords, setTotalFavoriteKeywords] = useState(0)
  const [sortBy, setSortBy] = useState(FAVORITE_KEYWORDS_SORT_BY.NEWEST)
  const searchInputRef = useRef<SearchInputRef>(null)
  const shouldInvalidateQueries = useRef(false)

  const params = useMemo(() => {
    return {
      limit: 10,
      locale: 'all',
      sort: sortBy === FAVORITE_KEYWORDS_SORT_BY.NEWEST ? '-updatedAt' : 'updatedAt',
      where: {
        or: searchQuery
          ? Object.values(LocaleEnum)?.map((locale) => ({
              [`keyword.name.${locale}`]: {
                like: searchQuery,
              },
            }))
          : undefined,
      },
    }
  }, [searchQuery, sortBy])

  const {
    favoriteKeywords,
    isGetFavoriteKeywordsLoading,
    isGetFavoriteKeywordsError,
    isFetchingNextPage: isGetFavoriteKeywordsFetchingNextPage,
    fetchNextPage,
    hasNextPage,
    refetch,
  } = useGetInfiniteFavoriteKeywords({
    params,
    config: {
      staleTime: 0,
      refetchOnWindowFocus: true,
      refetchOnMount: true,
    },
  })

  // Flatten all favorite keywords from all pages
  const allFavoriteKeywords = useMemo(() => {
    if (!favoriteKeywords?.pages) return []
    return favoriteKeywords.pages.flatMap((page) => page?.docs || [])
  }, [favoriteKeywords])

  // Update local state when new data comes in
  useEffect(() => {
    setLocalFavoriteKeywords(allFavoriteKeywords)
    // Update total from server data
    const serverTotal = favoriteKeywords?.pages?.[0]?.totalDocs || 0
    setTotalFavoriteKeywords(serverTotal)
  }, [allFavoriteKeywords, favoriteKeywords])

  // Handle pull-to-refresh
  const handleRefresh = useCallback(async () => {
    setRefreshing(true)
    try {
      await refetch()
    } finally {
      setRefreshing(false)
    }
  }, [refetch])

  // Handle removing item from local state after successful deletion
  const handleRemoveFromFavorites = useCallback((keywordId: string) => {
    setLocalFavoriteKeywords((prev) => prev.filter((item) => item.id !== keywordId))
    setTotalFavoriteKeywords((prev) => Math.max(0, prev - 1))
  }, [])

  // Handle load more
  const handleLoadMore = useCallback(() => {
    if (hasNextPage && !isGetFavoriteKeywordsFetchingNextPage) {
      fetchNextPage()
    }
  }, [hasNextPage, isGetFavoriteKeywordsFetchingNextPage, fetchNextPage])

  const handleSearchInputChange = (text: string) => {
    setSearchInputValue(text)
  }

  const handleClearSearchInput = () => {
    setSearchInputValue('')
    setSearchQuery('')
    setSearchTextValue('')
  }

  const { setSearchTextValue } = useMedicalDictionaryStore(
    useShallow((state) => ({
      setSearchTextValue: state.setSearchText,
    })),
  )

  // Switch sort by
  const handleSortBy = () => {
    setSortBy(
      sortBy === FAVORITE_KEYWORDS_SORT_BY.NEWEST
        ? FAVORITE_KEYWORDS_SORT_BY.OLDEST
        : FAVORITE_KEYWORDS_SORT_BY.NEWEST,
    )
  }

  // Build data array with different ListItem types
  const data = useMemo((): ListItem[] => {
    const items: ListItem[] = []

    // Show loading skeletons during initial load or refresh (but not during pagination)
    const isShowingLoadingSkeleton =
      (isGetFavoriteKeywordsLoading || refreshing) && !isGetFavoriteKeywordsFetchingNextPage

    if (isShowingLoadingSkeleton) {
      items.push({ type: 'loading_skeleton' })
    } else {
      // Keywords as individual items - use local state for instant updates
      localFavoriteKeywords.forEach((favoriteKeyword) => {
        items.push({ type: 'favorite_keyword', favoriteKeyword })
      })

      // Loading indicator for pagination
      if (isGetFavoriteKeywordsFetchingNextPage) {
        items.push({ type: 'loading' })
      }
    }

    return items
  }, [
    localFavoriteKeywords,
    isGetFavoriteKeywordsFetchingNextPage,
    isGetFavoriteKeywordsLoading,
    refreshing,
  ])

  // Render item based on ListItem type
  const renderItem = useCallback(
    ({ item }: { item: ListItem }) => {
      switch (item.type) {
        case 'favorite_keyword':
          return (
            <KeywordItem
              keyword={item.favoriteKeyword.keyword as KeywordV2}
              isShowArchiveButton={true}
              onUpdateFavoriteKeywordSuccess={(_, action?: 'add' | 'remove') => {
                if (action === 'remove') {
                  handleRemoveFromFavorites(item.favoriteKeyword.id)
                  shouldInvalidateQueries.current = true
                }
              }}
              customParams={{
                isFromFavoriteKeywordsScreen: 'true',
              }}
            />
          )

        case 'loading':
          return (
            <View className="items-center py-4">
              <ActivityIndicator size="small" />
            </View>
          )

        case 'loading_skeleton':
          return (
            <View className="flex flex-col gap-y-4 ">
              {new Array(8).fill(0).map((_, index) => (
                <Skeleton key={index} className="h-20 w-full" />
              ))}
            </View>
          )

        default:
          return null
      }
    },
    [handleRemoveFromFavorites],
  )

  const renderEmptyComponent = useCallback(() => {
    if (isGetFavoriteKeywordsError) {
      return (
        <View className="items-center  py-8">
          <Text size="body6" className="text-red-500">
            {t('MES-197')}
          </Text>
        </View>
      )
    }
    if (isGetFavoriteKeywordsLoading || isGetFavoriteKeywordsFetchingNextPage) {
      return null
    }

    return (
      <View className="items-center  py-8">
        <EmptyBoxIcon />
        <Text size="body6" variant="default">
          {t('MES-768')}
        </Text>
      </View>
    )
  }, [
    isGetFavoriteKeywordsError,
    isGetFavoriteKeywordsLoading,
    isGetFavoriteKeywordsFetchingNextPage,
  ])

  const renderSeparator = useCallback(() => {
    return <View className="h-2" />
  }, [])

  const keyExtractor = useCallback((item: ListItem, index: number) => {
    if (item.type === 'favorite_keyword') {
      return `favorite_keyword-${item.favoriteKeyword.id}`
    }
    return `${item.type}-${index}`
  }, [])

  const handleInvalidateQueries = useCallback(() => {
    queryClient.invalidateQueries({
      queryKey: keywordQueryKeys['dictionary-keywords'].base(),
      type: 'active',
      refetchType: 'active',
      exact: false,
    })
    queryClient.invalidateQueries({
      queryKey: keywordQueryKeys['dictionary-keywords-by-category'].base(),
      type: 'active',
      refetchType: 'active',
      exact: false,
    })
  }, [shouldInvalidateQueries.current])

  useEffect(() => {
    return () => {
      if (shouldInvalidateQueries.current) {
        handleInvalidateQueries()
      }
    }
  }, [])

  return (
    <View className="flex-1 flex-col gap-y-3 bg-white pt-4">
      {/* Header */}
      <View className="flex flex-col gap-y-3 px-4">
        <View className="flex  flex-col gap-y-3 bg-white ">
          <View className="flex flex-col gap-y-3">
            <View className="flex flex-row items-center gap-x-2">
              {/* Search Input */}
              <SearchInput
                ref={searchInputRef}
                placeholder={t('MES-66')}
                value={searchInputValue}
                onChangeText={handleSearchInputChange}
                onClear={handleClearSearchInput}
                onSubmitEditing={() => {
                  setSearchQuery(searchInputValue)
                  setSearchTextValue(searchInputValue)
                }}
              />
            </View>

            {/* Panel */}
            <View className="mt-2 flex flex-row items-center justify-between">
              {/* Total Count */}
              <Text size="body7" variant="subdued">
                {t('MES-774', { number: totalFavoriteKeywords || 0 })}
              </Text>

              {/* Sort By */}
              {totalFavoriteKeywords > 0 && (
                <SortButton
                  sortBy={sortBy}
                  onPress={handleSortBy}
                  showIcon={true}
                  className="flex-row-reverse"
                />
              )}
            </View>
          </View>
        </View>
      </View>

      {/* List */}
      <View className="flex-1 px-4 pb-4">
        <FlatList
          showsVerticalScrollIndicator={false}
          data={data}
          renderItem={renderItem}
          keyExtractor={keyExtractor}
          contentContainerStyle={{
            paddingBottom: 20,
          }}
          ItemSeparatorComponent={renderSeparator}
          ListEmptyComponent={renderEmptyComponent}
          onEndReached={handleLoadMore}
          onEndReachedThreshold={0.1}
          refreshControl={
            <RefreshControl
              refreshing={refreshing}
              onRefresh={handleRefresh}
              colors={[colors.primary['500']]}
              tintColor={colors.primary['500']}
              progressBackgroundColor="#FFFFFF"
            />
          }
        />
      </View>
    </View>
  )
}
