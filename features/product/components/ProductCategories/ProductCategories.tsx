import { Button } from '@/components/ui/Button/Button'
import { Skeleton } from '@/components/ui/Skeleton/Skeleton'
import { Text } from '@/components/ui/Text/Text'
import { useAppLanguage } from '@/hooks/common/useAppLanguage'
import { StyledExpoImage } from '@/libs/styled'
import { Media } from '@/types/media.type'
import { cn } from '@/utils/cn'
import { useEffect, useMemo, useState } from 'react'
import { useTranslation } from 'react-i18next'
import { TouchableOpacity, useWindowDimensions, View } from 'react-native'
import Animated, { FadeIn, FadeOut } from 'react-native-reanimated'
import Svg, { Path } from 'react-native-svg'
import { ProductV2TypeEnum } from '../../enums'
import { useProductNavigation } from '../../hooks/common/useProductNavigation'
import { useGetProductCategories } from '../../hooks/query/product/useGetProductCategories'
import { useGetProductNestedCategories } from '../../hooks/query/product/useGetProductNestedCategories'
import { ProductCategory } from '../../types'
import { BreadcrumbNavigation } from './BreadcrumbNavigation'

interface ProductCategoriesProps {
  isRefreshing?: boolean
  productType: ProductV2TypeEnum
}

export const ProductCategories = ({ isRefreshing, productType }: ProductCategoriesProps) => {
  const [showMore, setShowMore] = useState(false)
  const { t } = useTranslation()
  const {
    selectedCategory,
    categoryHistory,
    isRoot,
    selectCategory,
    goToRoot,
    navigateToCategory,
  } = useProductNavigation(productType)

  const { primaryLanguage } = useAppLanguage()
  const { width } = useWindowDimensions()
  // Calculate proper width for 2 items per row with gap
  // Container width - padding (32px) - gap between items (8px) = available width
  // Available width / 2 = width per item
  const itemWidth = useMemo(() => (width - 32 - 8) / 2, [width])

  //   Root categories based on product type
  const {
    productCategories,
    isGetProductCategoriesLoading,
    isGetProductCategoriesError,
    isRefetching,
    refetch,
  } = useGetProductCategories({
    useQueryOptions: {
      staleTime: 5 * 60 * 1000,
      enabled: isRoot,
    },
    params: {
      limit: 25,
      depth: 1,
      locale: primaryLanguage,
      where: {
        and: [
          {
            type: {
              equals: productType,
            },
          },
          {
            parent: {
              exists: false,
            },
          },
          {
            categoryLevel: {
              equals: 0,
            },
          },
        ],
      },
      select: {
        id: true,
        title: true,
        icon: true,
      },
    },
  })

  const {
    productNestedCategories,
    isGetProductNestedCategoriesLoading,
    isGetProductNestedCategoriesError,
  } = useGetProductNestedCategories({
    categoryId: selectedCategory,
    params: {
      locale: primaryLanguage,
    },
    useQueryOptions: {
      enabled: !!selectedCategory && !isRoot,
    },
  })

  const handleCategoryPress = (selectedCategory: ProductCategory) => {
    selectCategory(selectedCategory)
  }

  const handleNavigateToCategory = (category: ProductCategory) => {
    navigateToCategory(category.id)
  }

  useEffect(() => {
    setShowMore(false)
  }, [productType])

  useEffect(() => {
    if (isRefreshing) {
      refetch()
    }
  }, [isRefreshing])

  // Determine which categories to show based on current state
  const categoriesToShow = isRoot ? productCategories?.docs : productNestedCategories?.docs
  const isLoading = isRoot ? isGetProductCategoriesLoading : isGetProductNestedCategoriesLoading
  const isError = isRoot ? isGetProductCategoriesError : isGetProductNestedCategoriesError

  // Only show full loading screen for initial root loading or refreshing
  const shouldShowFullLoading = (isRoot && isGetProductCategoriesLoading) || isRefetching

  if (shouldShowFullLoading) {
    return <ProductCategoriesLoading itemWidth={itemWidth} />
  }

  if (isError) {
    return (
      <View className="flex items-center justify-center py-8">
        <Text size="body6" className="text-red-500">
          {t('MES-197')}
        </Text>
      </View>
    )
  }

  return (
    <View className="flex w-full flex-col gap-y-3">
      {/* Breadcrumb Navigation - Always show when not at root */}
      {!isRoot && (
        <BreadcrumbNavigation
          categoryHistory={categoryHistory}
          onNavigateToCategory={handleNavigateToCategory}
          onGoToRoot={goToRoot}
          productType={productType}
        />
      )}

      {/* Show loading indicator for nested categories while keeping breadcrumb visible */}
      {!isRoot && isGetProductNestedCategoriesLoading && (
        <View className="flex flex-col gap-y-2">
          <View className="flex flex-row gap-x-2">
            <Skeleton className="h-14 flex-1 rounded-[6px] " />
            <Skeleton className="h-14 flex-1 rounded-[6px] " />
          </View>
          <View className="flex flex-row gap-x-2">
            <Skeleton className="h-14 flex-1 rounded-[6px] " />
            <Skeleton className="h-14 flex-1 rounded-[6px] " />
          </View>
        </View>
      )}

      {/* Show categories if available, or message if none */}
      {categoriesToShow && categoriesToShow.length > 0 ? (
        <>
          <View className="flex flex-row flex-wrap gap-2">
            {categoriesToShow.slice(0, 6).map((category) => (
              <ProductCategoryItem
                key={category.id}
                category={category}
                itemWidth={itemWidth}
                handleCategoryPress={handleCategoryPress}
              />
            ))}
          </View>

          {showMore && (
            <Animated.View
              entering={FadeIn.springify().stiffness(150).duration(250)}
              exiting={FadeOut.springify().stiffness(150).duration(250)}
              className="flex flex-row flex-wrap gap-2"
            >
              {categoriesToShow.slice(6, categoriesToShow.length).map((category) => (
                <ProductCategoryItem
                  key={category.id}
                  category={category}
                  itemWidth={itemWidth}
                  handleCategoryPress={handleCategoryPress}
                />
              ))}
            </Animated.View>
          )}

          {!isLoading && categoriesToShow.length > 6 && (
            <Animated.View
              entering={FadeIn.springify().stiffness(150).duration(250)}
              exiting={FadeOut.springify().stiffness(150).duration(250)}
            >
              <Button
                className="mx-auto self-start bg-transparent"
                onPress={() => setShowMore(!showMore)}
              >
                <Text size="body6" variant="primary">
                  {!showMore
                    ? t('MES-746', { number: categoriesToShow.length })
                    : t('MES-745', { number: categoriesToShow.length })}
                </Text>
                <View
                  className={cn('transition-transform duration-300')}
                  style={{
                    transform: [{ rotate: showMore ? '0deg' : '180deg' }],
                  }}
                >
                  <Svg width={16} height={16} viewBox="0 0 16 16" fill="none">
                    <Path
                      d="M12.3996 9.69452L8.77739 6.0723C8.34961 5.64452 7.64961 5.64452 7.22183 6.0723L3.59961 9.69452"
                      stroke="#1157C8"
                      strokeWidth={1.5}
                      strokeMiterlimit={10}
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    />
                  </Svg>
                </View>
              </Button>
            </Animated.View>
          )}
        </>
      ) : null}
    </View>
  )
}

const ProductCategoriesLoading = ({ itemWidth }: { itemWidth: number }) => {
  return (
    <View className="flex w-full flex-row flex-wrap gap-2">
      {new Array(6).fill(0).map((_, index) => (
        <View key={index} style={{ width: itemWidth, height: 40 }}>
          <Skeleton className="h-full w-full rounded-[6px] bg-gray-200" />
        </View>
      ))}
    </View>
  )
}

interface ProductCategoryItemProps {
  category: ProductCategory
  itemWidth: number
  handleCategoryPress: (category: ProductCategory) => void
}

const ProductCategoryItem = ({
  category,
  itemWidth,
  handleCategoryPress,
}: ProductCategoryItemProps) => {
  const { icon, title, id } = category
  const { url, thumbnailURL } = (icon as Media) || {}
  return (
    <TouchableOpacity
      className="flex min-h-12 flex-row items-center gap-4 overflow-hidden rounded-[6px] bg-custom-background-hover px-3 py-2"
      style={{ width: itemWidth }}
      accessibilityRole="button"
      accessibilityLabel={`Category: ${title}`}
      onPress={() => handleCategoryPress(category)}
    >
      {thumbnailURL ||
        (url && (
          <View className="aspect-square h-[30px] w-[30px] overflow-hidden">
            <StyledExpoImage
              source={{ uri: thumbnailURL || url }}
              className="h-full w-full"
              contentFit="cover"
            />
          </View>
        ))}

      <Text size="body6" numberOfLines={2} className="flex-1 !text-[13px]">
        {title}
      </Text>
    </TouchableOpacity>
  )
}
