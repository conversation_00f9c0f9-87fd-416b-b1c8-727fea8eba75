import { Text } from "@/components/ui/Text/Text"
import { Faculty } from "@/types/faculty.type"
import { useCallback, useMemo } from "react"
import { useTranslation } from "react-i18next"
import { ActivityIndicator, FlatList, View } from "react-native"
import { useGetInfiniteMedicalFaculties } from "../hooks/useGetInfiniteMedicalFaculties"
import { MedicalFacultyItem } from "./MedicalFacultyItem"

export const MedicalFacultyList = () => {
    const { t } = useTranslation()

    const { medicalFaculties, isGetMedicalFacultiesLoading, isGetMedicalFacultiesError,
        isFetchingNextPage
        , fetchNextPage, hasNextPage,
    } = useGetInfiniteMedicalFaculties({
        params: {
            limit: 15,
            locale: 'all',
            select: {
                questions: false,
                createdAt: false,
                updatedAt: false,
            },
        },

    })


    const allFaculties = useMemo(() => {
        if (!medicalFaculties?.pages) return []
        return medicalFaculties.pages.flatMap((page) => page?.docs || [])
    }, [medicalFaculties])

    const handleLoadMore = useCallback(() => {
        if (hasNextPage && !isGetMedicalFacultiesLoading) {
            fetchNextPage()
        }
    }, [hasNextPage, isGetMedicalFacultiesLoading, fetchNextPage])

    const renderItem = useCallback(({ item }: { item: Faculty }) => {
        return <MedicalFacultyItem faculty={item} />
    }, [])

    const renderFooter = useCallback(() => {
        if (!isFetchingNextPage) return null

        return (
            <View className="py-4">
                <ActivityIndicator size="small" />
            </View>
        )
    }, [isFetchingNextPage])


    const renderEmpty = useCallback(() => {
        if (isGetMedicalFacultiesLoading) {
            return (
                <View className="flex-1 items-center justify-center py-8">
                    <ActivityIndicator size="large" />
                </View>
            )
        }

        if (isGetMedicalFacultiesError) {
            return (
                <View className="flex-1 items-center justify-center py-8">
                    <Text size="body6" className="text-red-500">
                        Failed to load faculties. Please try again.
                    </Text>
                </View>
            )
        }

        return (
            <View className="flex-1 items-center justify-center py-8">
                <Text size="body6" className="text-gray-500">
                    No faculties found.
                </Text>
            </View>
        )
    }, [isGetMedicalFacultiesLoading, isGetMedicalFacultiesError])


    return <View className="flex-1">
        <Text size='body3' className="my-4">
            {t('MES-130')}
        </Text>

        <FlatList
            data={allFaculties}
            renderItem={renderItem}
            keyExtractor={(item) => item.id}
            onEndReached={handleLoadMore}
            onEndReachedThreshold={0.1}
            showsVerticalScrollIndicator={false}
            removeClippedSubviews={true}
            maxToRenderPerBatch={10}
            ListFooterComponent={renderFooter}
            ListEmptyComponent={renderEmpty}
            contentContainerStyle={{
                gap: 8
            }}
        />
    </View>
}