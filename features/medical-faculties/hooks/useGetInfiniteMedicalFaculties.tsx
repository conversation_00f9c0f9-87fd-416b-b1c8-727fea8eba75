import { Params } from '@/types/http.type'
import { InfiniteData, UseInfiniteQueryOptions, useInfiniteQuery } from '@tanstack/react-query'

import { PaginatedDocs } from '@/types/global.type'
import { AxiosRequestConfig } from 'axios'

import { Faculty } from '@/types/faculty.type'
import { medicalFacultyService } from '../services/faculties.service'
import { medicalFacultiesQueryKeys } from './queryKeys'

export type MedicalFacultiesQueryConfig = Omit<
    UseInfiniteQueryOptions<
        PaginatedDocs<Faculty>,
        Error,
        InfiniteData<PaginatedDocs<Faculty>>,
        (string | Params)[],
        number
    >,
    'queryFn' | 'queryKey' | 'getNextPageParam' | 'initialPageParam'
>

export const useGetInfiniteMedicalFaculties = ({
    params = {},
    options = {},
    config = {},
    overrideKey,
}: {
    params?: Params
    options?: AxiosRequestConfig
    config?: MedicalFacultiesQueryConfig
    overrideKey?: string[]
} = {}) => {
    const {
        isError: isGetMedicalFacultiesError,
        isFetching: isGetMedicalFacultiesLoading,
        data: medicalFaculties,
        fetchNextPage,
        hasNextPage,
        ...rest
    } = useInfiniteQuery({
        queryKey: overrideKey ? overrideKey : [medicalFacultiesQueryKeys['medicalFaculties'].base(), params],
        queryFn: async ({ pageParam = 1 }) => {
            return medicalFacultyService.getMedicalFaculties({
                params: {
                    ...params,
                    page: pageParam,
                },
                options,
            })
        }, getNextPageParam: (lastPage) => lastPage?.nextPage,
        getPreviousPageParam: (lastPage) => lastPage?.prevPage,
        initialPageParam: 1,
        ...config,
    })

    return {
        isGetMedicalFacultiesError,
        isGetMedicalFacultiesLoading,
        medicalFaculties,
        fetchNextPage,
        hasNextPage,
        ...rest,
    }
}
