import { APP_ROUTES, AppRoutesEnum } from '@/routes/appRoutes'
import { LinkProps, useRouter } from 'expo-router'
import { Platform } from 'react-native'
import { UserPushNotification } from '../types'

export const useGeneralNotiAction = () => {
  const router = useRouter()

  const handleOpenGeneralNoti = (data?: UserPushNotification) => {
    const route = {
      pathname: APP_ROUTES.NOTIFICATIONS.children?.[AppRoutesEnum.NOTIFICATION_DETAILS].path,
      params: {
        title: data?.title,
        description: data?.description,
        trackingId: data?.trackingId,
      },
    }
    if (Platform.OS === 'ios') {
      setTimeout(() => {
        router.push({
          pathname: route.pathname,
          params: route.params,
        } as LinkProps['href'])
      }, 1)
    } else {
      setImmediate(() => {
        router.push({
          pathname: route.pathname,
          params: route.params,
        } as LinkProps['href'])
      })
    }
  }

  return {
    handleOpenGeneralNoti,
  }
}
