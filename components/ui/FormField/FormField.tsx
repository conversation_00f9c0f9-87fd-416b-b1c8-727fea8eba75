import * as Slot from '@rn-primitives/slot'
import React from 'react'
import {
  Controller,
  ControllerProps,
  FieldPath,
  FieldValues,
  FormProvider,
  useFormContext,
} from 'react-hook-form'
import { View } from 'react-native'

import { cn } from '@/utils/cn'
import { Text } from '../Text/Text'

const Form = FormProvider

type FormFieldContextValue<
  TFieldValues extends FieldValues = FieldValues,
  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>,
> = {
  name: TName
}

const FormFieldContext = React.createContext<FormFieldContextValue>({} as FormFieldContextValue)

const FormField = <
  TFieldValues extends FieldValues = FieldValues,
  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>,
>({
  ...props
}: ControllerProps<TFieldValues, TName>) => {
  return (
    <FormFieldContext.Provider value={{ name: props.name }}>
      <Controller {...props} />
    </FormFieldContext.Provider>
  )
}

const useFormField = () => {
  const fieldContext = React.useContext(FormFieldContext)
  const itemContext = React.useContext(FormItemContext)
  const { getFieldState, formState } = useFormContext()

  const fieldState = getFieldState(fieldContext.name, formState)

  if (!fieldContext) {
    throw new Error('useFormField should be used within <FormField>')
  }

  const { id } = itemContext

  return {
    id,
    name: fieldContext.name,
    formItemId: `${id}-form-item`,
    formDescriptionId: `${id}-form-item-description`,
    formMessageId: `${id}-form-item-message`,
    ...fieldState,
  }
}

type FormItemContextValue = {
  id: string
}

const FormItemContext = React.createContext<FormItemContextValue>({} as FormItemContextValue)

type FormItemProps = React.ComponentProps<typeof View> & {
  className?: string
}

const FormItem = React.forwardRef<View, FormItemProps>(({ className, ...props }, ref) => {
  const id = React.useId()

  return (
    <FormItemContext.Provider value={{ id }}>
      <View ref={ref} className={cn('space-y-2', className)} {...props} />
    </FormItemContext.Provider>
  )
})

FormItem.displayName = 'FormItem'

type FormLabelProps = Omit<React.ComponentProps<typeof Text>, 'ref'> & {
  required?: boolean
}

// Don't forward the ref to the Text component
const FormLabel = React.forwardRef<never, FormLabelProps>(
  ({ className, required, children, ...props }, _ref) => {
    const { error } = useFormField()

    return (
      <View className="mb-0 flex-row items-center gap-x-1 pb-0">
        <Text
          className={cn(error && 'text-custom-danger-600', className)}
          size="body6"
          variant={error ? 'error' : 'default'}
          {...props}
        >
          {children}
        </Text>
        {required && (
          <Text variant="error" className="block ">
            *
          </Text>
        )}
      </View>
    )
  },
)

FormLabel.displayName = 'FormLabel'

// For React Native, we're using Slot instead of standard View
const FormControl = React.forwardRef<
  React.ComponentRef<typeof Slot.View>,
  React.ComponentPropsWithoutRef<typeof Slot.View>
>(({ className, ...props }, ref) => {
  const { error } = useFormField()

  return (
    <Slot.View
      ref={ref}
      className={cn(error && 'border-custom-danger-600', className)}
      {...props}
    />
  )
})

FormControl.displayName = 'FormControl'

type FormDescriptionProps = Omit<React.ComponentProps<typeof Text>, 'ref'>

// Use custom wrapper component with the correct ref type
const FormDescription = ({ className, ...props }: FormDescriptionProps) => {
  return <Text size="body7" variant="subdued" className={cn(className)} {...props} />
}

FormDescription.displayName = 'FormDescription'

type FormMessageProps = Omit<React.ComponentProps<typeof Text>, 'ref'>

// Use functional component instead of forwarding ref
const FormMessage = ({ className, children, ...props }: FormMessageProps) => {
  const { error } = useFormField()
  const body = error ? String(error?.message) : children

  if (!body) {
    return null
  }

  return (
    <Text size="body7" variant="error" className={cn('mt-1', className)} {...props}>
      {body}
    </Text>
  )
}

FormMessage.displayName = 'FormMessage'

interface CustomFormMessageProps extends Omit<React.ComponentProps<typeof Text>, 'ref'> {
  error?: string
}

// Use functional component instead of forwarding ref
const CustomFormMessage = ({ className, error, ...props }: CustomFormMessageProps) => {
  if (!error) return null

  return (
    <Text size="body7" variant="error" className={cn('mt-1', className)} {...props}>
      {error}
    </Text>
  )
}

CustomFormMessage.displayName = 'CustomFormMessage'

export {
  CustomFormMessage,
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  useFormField,
}
