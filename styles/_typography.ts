// Typography system converted from SCSS
export const fontWeights = {
  thin: '100' as const,
  xLight: '200' as const,
  light: '300' as const,
  regular: '400' as const,
  medium: '500' as const,
  semiBold: '600' as const,
  bold: '700' as const,
  xBold: '800' as const,
  heavy: '900' as const,
}

// Typography definitions - each entry contains [weight, fontSize, lineHeight]
export const typography = {
  heading: {
    1: {
      fontWeight: fontWeights.semiBold,
      fontSize: 64,
      lineHeight: 76,
    },
    2: {
      fontWeight: fontWeights.semiBold,
      fontSize: 48,
      lineHeight: 58,
    },
    3: {
      fontWeight: fontWeights.semiBold,
      fontSize: 36,
      lineHeight: 44,
    },
    4: {
      fontWeight: fontWeights.medium,
      fontSize: 36,
      lineHeight: 44,
    },
    5: {
      fontWeight: fontWeights.semiBold,
      fontSize: 28,
      lineHeight: 36,
    },
    6: {
      fontWeight: fontWeights.medium,
      fontSize: 28,
      lineHeight: 36,
    },
    7: {
      fontWeight: fontWeights.semiBold,
      fontSize: 20,
      lineHeight: 28,
    },
    8: {
      fontWeight: fontWeights.semiBold,
      fontSize: 16,
      lineHeight: 24,
    },
    9: {
      fontWeight: fontWeights.medium,
      fontSize: 16,
      lineHeight: 24,
    },
  },

  body: {
    1: {
      fontWeight: fontWeights.medium,
      fontSize: 24,
      lineHeight: 28,
    },
    2: {
      fontWeight: fontWeights.medium,
      fontSize: 18,
      lineHeight: 28,
    },
    3: {
      fontWeight: fontWeights.semiBold,
      fontSize: 16,
      lineHeight: 24,
    },
    4: {
      fontWeight: fontWeights.regular,
      fontSize: 16,
      lineHeight: 24,
    },
    5: {
      fontWeight: fontWeights.semiBold,
      fontSize: 16,
      lineHeight: 24,
    },
    6: {
      fontWeight: fontWeights.medium,
      fontSize: 14,
      lineHeight: 20,
    },
    7: {
      fontWeight: fontWeights.regular,
      fontSize: 14,
      lineHeight: 20,
    },
    8: {
      fontWeight: fontWeights.medium,
      fontSize: 12,
      lineHeight: 16,
    },
    9: {
      fontWeight: fontWeights.regular,
      fontSize: 12,
      lineHeight: 16,
    },
    10: {
      fontWeight: fontWeights.semiBold,
      fontSize: 14,
      lineHeight: 20,
    },
    11: {
      fontWeight: fontWeights.regular,
      fontSize: 11,
      lineHeight: 14,
    },
    12: {
      fontWeight: fontWeights.medium,
      fontSize: 16,
      lineHeight: 24,
    },
    13: {
      fontWeight: fontWeights.semiBold,
      fontSize: 16,
      lineHeight: 20,
    },
  },

  button: {
    1: {
      fontWeight: fontWeights.semiBold,
      fontSize: 18,
      lineHeight: 24,
    },
    2: {
      fontWeight: fontWeights.semiBold,
      fontSize: 16,
      lineHeight: 20,
    },
    3: {
      fontWeight: fontWeights.semiBold,
      fontSize: 14,
      lineHeight: 16,
    },
    4: {
      fontWeight: fontWeights.semiBold,
      fontSize: 14,
      lineHeight: 20,
    },
    5: {
      fontWeight: fontWeights.medium,
      fontSize: 12,
      lineHeight: 16,
    },
  },

  field: {
    1: {
      fontWeight: fontWeights.regular,
      fontSize: 14,
      lineHeight: 20,
    },
    2: {
      fontWeight: fontWeights.regular,
      fontSize: 14,
      lineHeight: 20,
    },
    3: {
      fontWeight: fontWeights.regular,
      fontSize: 14,
      lineHeight: 20,
    },
    4: {
      fontWeight: fontWeights.regular,
      fontSize: 16,
      lineHeight: 20,
    },
    5: {
      fontWeight: fontWeights.medium,
      fontSize: 12,
      lineHeight: 16,
    },
  },

  link: {
    1: {
      fontWeight: fontWeights.regular,
      fontSize: 14,
      lineHeight: 20,
    },
    2: {
      fontWeight: fontWeights.regular,
      fontSize: 12,
      lineHeight: 16,
    },
    3: {
      fontWeight: fontWeights.semiBold,
      fontSize: 14,
      lineHeight: 16,
    },
    4: {
      fontWeight: fontWeights.regular,
      fontSize: 16,
      lineHeight: 20,
    },
    5: {
      fontWeight: fontWeights.medium,
      fontSize: 12,
      lineHeight: 16,
    },
  },
} as const

// Type definitions for better TypeScript support
export type FontWeights = typeof fontWeights
export type FontWeightKey = keyof FontWeights
export type Typography = typeof typography
export type TypographyCategory = keyof Typography
export type TypographyVariant<T extends TypographyCategory> = keyof Typography[T]
export type TypographyStyle = {
  fontWeight: '100' | '200' | '300' | '400' | '500' | '600' | '700' | '800' | '900'
  fontSize: number
  lineHeight: number
}

// Default font family
export const fontFamily = {
  primary: 'Inter',
  // Add more font families as needed
} as const

// Utility functions for easier access
export const getTypographyStyle = <T extends TypographyCategory>(
  category: T,
  variant: TypographyVariant<T>,
): TypographyStyle => {
  return typography[category][variant] as TypographyStyle
}

// Specific utility functions for each category
export const getHeadingStyle = (variant: keyof typeof typography.heading): TypographyStyle => {
  return typography.heading[variant] as TypographyStyle
}

export const getBodyStyle = (variant: keyof typeof typography.body): TypographyStyle => {
  return typography.body[variant] as TypographyStyle
}

export const getButtonStyle = (variant: keyof typeof typography.button): TypographyStyle => {
  return typography.button[variant] as TypographyStyle
}

export const getFieldStyle = (variant: keyof typeof typography.field): TypographyStyle => {
  return typography.field[variant] as TypographyStyle
}

export const getLinkStyle = (variant: keyof typeof typography.link): TypographyStyle => {
  return typography.link[variant] as TypographyStyle
}

// Simpler utility functions for direct use in StyleSheet
export const typo = {
  // Heading styles
  heading1: typography.heading[1] as TypographyStyle,
  heading2: typography.heading[2] as TypographyStyle,
  heading3: typography.heading[3] as TypographyStyle,
  heading4: typography.heading[4] as TypographyStyle,
  heading5: typography.heading[5] as TypographyStyle,
  heading6: typography.heading[6] as TypographyStyle,
  heading7: typography.heading[7] as TypographyStyle,
  heading8: typography.heading[8] as TypographyStyle,
  heading9: typography.heading[8] as TypographyStyle, // Map heading9 to heading8

  // Body styles
  body1: typography.body[1] as TypographyStyle,
  body2: typography.body[2] as TypographyStyle,
  body3: typography.body[3] as TypographyStyle,
  body4: typography.body[4] as TypographyStyle,
  body5: typography.body[5] as TypographyStyle,
  body6: typography.body[6] as TypographyStyle,
  body7: typography.body[7] as TypographyStyle,
  body8: typography.body[8] as TypographyStyle,
  body9: typography.body[9] as TypographyStyle,
  body10: typography.body[10] as TypographyStyle,
  body11: typography.body[11] as TypographyStyle,
  body12: typography.body[12] as TypographyStyle,
  body13: typography.body[13] as TypographyStyle,

  // Button styles
  button1: typography.button[1] as TypographyStyle,
  button2: typography.button[2] as TypographyStyle,
  button3: typography.button[3] as TypographyStyle,
  button4: typography.button[4] as TypographyStyle,
  button5: typography.button[5] as TypographyStyle,

  // Field styles
  field1: typography.field[1] as TypographyStyle,
  field2: typography.field[2] as TypographyStyle,
  field3: typography.field[3] as TypographyStyle,
  field4: typography.field[4] as TypographyStyle,
  field5: typography.field[5] as TypographyStyle,

  // Link styles
  link1: typography.link[1] as TypographyStyle,
  link2: typography.link[2] as TypographyStyle,
  link3: typography.link[3] as TypographyStyle,
  link4: typography.link[4] as TypographyStyle,
  link5: typography.link[5] as TypographyStyle,
}

// Utility function to get font weight by name
export const getFontWeight = (weight: FontWeightKey): string => {
  return fontWeights[weight]
}

// Utility function to create complete text style for React Native
export const createTextStyle = <T extends TypographyCategory>(
  category: T,
  variant: TypographyVariant<T>,
  options?: {
    fontFamily?: string
    color?: string
    textAlign?: 'auto' | 'left' | 'right' | 'center' | 'justify'
    textDecorationLine?: 'none' | 'underline' | 'line-through' | 'underline line-through'
  },
) => {
  const baseStyle = getTypographyStyle(category, variant)

  return {
    ...baseStyle,
    fontFamily: options?.fontFamily || fontFamily.primary,
    color: options?.color,
    textAlign: options?.textAlign,
    textDecorationLine: options?.textDecorationLine,
  }
}

// Export individual categories for convenience
export const { heading, body, button, field, link } = typography

// Common text styles for quick access
export const textStyles = {
  // Headings
  h1: typo.heading1,
  h2: typo.heading2,
  h3: typo.heading3,
  h4: typo.heading4,
  h5: typo.heading5,
  h6: typo.heading6,

  // Body text
  bodyLarge: typo.body1,
  bodyMedium: typo.body4,
  bodySmall: typo.body9,

  // UI elements
  buttonLarge: typo.button1,
  buttonMedium: typo.button2,
  buttonSmall: typo.button3,

  // Form elements
  inputText: typo.field4,
  inputLabel: typo.field5,

  // Links
  linkDefault: typo.link1,
  linkSmall: typo.link2,
}

// Default export
export default typography
