import { Skeleton } from '@/components/ui/Skeleton/Skeleton'
import { Text } from '@/components/ui/Text/Text'
import { useAuthentication } from '@/hooks/auth/useAuthentication'
import { useEffect } from 'react'
import { useTranslation } from 'react-i18next'
import { View } from 'react-native'
import { useGetKeywordSearchHistoryByUser } from '../../hooks/query/useGetKeywordSearchHistoryByUser'
import {
  KeywordSearchHistoryBadgeList,
  KeywordSearchHistoryBadgeListLoading,
} from '../KeywordSearchHistoryBadgeList/KeywordSearchHistoryBadgeList'

interface MedicalDictionarySearchHistorySectionProps {
  isRefreshing?: boolean
}

export function MedicalDictionarySearchHistorySection({
  isRefreshing,
}: MedicalDictionarySearchHistorySectionProps) {
  const { user, status } = useAuthentication()
  const { t } = useTranslation()

  const {
    keywordSearchHistoryByUser,
    isGetKeywordSearchHistoryByUserLoading,
    isFetching,
    refetch,
    isRefetching,
  } = useGetKeywordSearchHistoryByUser({
    params: {
      locale: 'all',
      limit: 6,
      populate: {
        keywords: {
          name: true,
        },
      },
    },
    useQueryOptions: {
      staleTime: 3 * 60 * 1000,
      enabled: Boolean(status === 'success' && user),
    },
  })

  useEffect(() => {
    if (isRefreshing) {
      refetch()
    }
  }, [isRefreshing])

  // Show loading when authentication status is loading
  if (status === 'loading') {
    return <MedicalDictionarySearchHistorySectionLoading />
  }

  // If no user or unauthorized, return null
  if (!user || status === 'unauthorized') return null

  // Show loading when refreshing or fetching keyword search history
  if (isRefreshing || isGetKeywordSearchHistoryByUserLoading || isFetching) {
    return <MedicalDictionarySearchHistorySectionLoading />
  }

  // If no search history data, return null
  if (!keywordSearchHistoryByUser?.docs?.length) return null

  const isLoading = isGetKeywordSearchHistoryByUserLoading || isFetching || isRefetching

  return (
    <View className="flex flex-col gap-y-3 ">
      <Text size="body3">{t('MES-776')}</Text>
      <KeywordSearchHistoryBadgeList
        keywordSearchHistory={keywordSearchHistoryByUser?.docs}
        isLoading={isLoading}
      />
    </View>
  )
}

const MedicalDictionarySearchHistorySectionLoading = () => {
  return (
    <View className="flex flex-col gap-y-4">
      <Skeleton className="h-8 w-[140px]" />
      <KeywordSearchHistoryBadgeListLoading />
    </View>
  )
}
