import DefaultAvatarIcon from '@/assets/icons/default-avatar-icon.svg'
import ReportCommentIcon from '@/assets/icons/report-flag-icon.svg'
import { useOpenBaseWarningPopup } from '@/components/Popup/BaseWarningPopup/BaseWarningPopup'
import { Text } from '@/components/ui/Text/Text'
import { useAuthentication } from '@/hooks/auth/useAuthentication'
import { useAppLanguage } from '@/hooks/common/useAppLanguage'
import { StyledExpoImage } from '@/libs/styled'
import { APP_ROUTES, AppRoutesEnum } from '@/routes/appRoutes'
import { primary } from '@/styles/_colors'
import { Media } from '@/types/media.type'
import { User } from '@/types/user.type'
import { cn } from '@/utils/cn'
import { formatRelativeDate } from '@/utils/date'
import { Link } from 'expo-router'
import { memo, useEffect, useMemo, useRef, useState } from 'react'
import { useTranslation } from 'react-i18next'
import { ActivityIndicator, Keyboard, TextInput, TouchableOpacity, View } from 'react-native'
import Svg, { Path } from 'react-native-svg'
import { ErrorRetry } from '../../../../components/ErrorRetry/ErrorRetry'
import { MAX_COMMENT_LENGTH } from '../../constants'
import { useReactionComment } from '../../hooks/query/useReactionComment'
import { CommentCacheManager, CommentQueryParams } from '../../services/commentCacheManager.service'
import { Comment } from '../../types'
import { useOpenCommentActionSheet } from '../CommentActionSheet/CommentActionSheet'
import { NestedComments } from '../NestedComments/NestedComments'
interface CommentItemProps {
  comment: Comment
  relationTo: 'keywords' | 'posts' | 'products'
  isNested?: boolean
  onReply?: (comment: Comment) => void
  className?: string
  isLastComment?: boolean
  onReactionSuccess?: () => void
  onEditSuccess?: () => void
  onDeleteSuccess?: () => void
  onCommentActionSuccess?: () => void
  // Centralized handlers from useCommentManagement
  onEditComment?: (
    commentId: string,
    content: string,
    parentId?: string,
    callbacks?: {
      onSuccess?: () => void
      onError?: () => void
    },
  ) => void
  onStartEdit?: (commentId: string) => void
  onCancelEdit?: () => void
  onDeleteComment?: (
    commentId: string,
    parentId?: string,
    callbacks?: {
      onSuccess?: () => void
      onError?: () => void
    },
  ) => void
  onRetryAddComment?: (content: string, parentId?: string) => void
  isEditCommentPending?: boolean
  editingCommentId?: string | null
  cacheManager?: CommentCacheManager
  mainParams?: CommentQueryParams
  nestedParams?: CommentQueryParams
  parentId?: string // Parent comment ID for nested comments
  forceOpenNestedComments?: boolean
  nestedCommentId?: string
  isHighlighted?: boolean
  showLoadmoreNestedComments?: boolean
}
export const CommentItem = memo(
  ({
    comment,
    relationTo,
    isNested = false,
    onReply,
    className,
    isLastComment = false,
    onReactionSuccess,
    onEditSuccess,
    onDeleteSuccess,
    onCommentActionSuccess,
    onEditComment,
    onStartEdit,
    onCancelEdit,
    onDeleteComment,
    onRetryAddComment,
    isEditCommentPending = false,
    editingCommentId,
    cacheManager,
    mainParams,
    nestedParams,
    parentId: propParentId,
    forceOpenNestedComments = false,
    nestedCommentId,
    isHighlighted = false,
    showLoadmoreNestedComments = true,
  }: CommentItemProps) => {
    const { user, status } = useAuthentication()
    const { t } = useTranslation()
    const { primaryLanguage } = useAppLanguage()
    const { openBaseWarningPopup } = useOpenBaseWarningPopup()

    const [localComment, setLocalComment] = useState<Comment>(comment)
    const { author, createdAt, isAdmin } = comment

    // Use global editing state instead of local state
    const isEditing = editingCommentId === comment.id
    // Move edit content state into CommentContentEditBox to reduce re-renders here
    const [isDeletingOptimistic, setIsDeletingOptimistic] = useState(false)
    const [isEditingOptimistic, setIsEditingOptimistic] = useState(false)
    const [hasEditError, setHasEditError] = useState(false)
    const [hasDeleteError, setHasDeleteError] = useState(false)
    const [hasAddError, setHasAddError] = useState(false)
    const [pendingEditContent, setPendingEditContent] = useState('')
    const [pendingAddContent, setPendingAddContent] = useState('')

    // Check if this is an optimistic comment with an error
    const isOptimisticWithError = localComment.isOptimistic && localComment.hasError
    const isOptimisticLoading = localComment.isOptimistic && localComment.isLoading

    // Validation for edit is handled inside CommentContentEditBox
    const commentAuthor = (typeof author === 'object' ? author : null) as User | null

    const avatarMedia = commentAuthor?.avatar as Media | null | undefined
    const { thumbnailURL, url } = avatarMedia || {}

    const relativeDate = useMemo(() => {
      try {
        if (!createdAt) {
          return ''
        }

        const formattedDate = formatRelativeDate({
          dateString: createdAt,
          locale: primaryLanguage,
        })

        // If formatRelativeDate returns null, fall back to locale string or default
        if (formattedDate === null) {
          // Try to format as locale string as fallback
          try {
            const fallbackDate = new Date(createdAt).toLocaleString()
            return fallbackDate
          } catch (fallbackError) {
            console.warn('[CommentItem] Fallback date formatting failed:', fallbackError)
            return ''
          }
        }

        return formattedDate
      } catch (error) {
        return ''
      }
    }, [createdAt, primaryLanguage, comment.id])

    const totalNestedComments = useMemo(() => {
      return comment?.replies?.totalDocs || 0
    }, [comment])

    const { isReactionCommentPending, reactionCommentMutation } = useReactionComment()
    const isOwnComment = user && commentAuthor?.id === user.id
    const handleReactionComment = (type: 'like' | 'unlike') => {
      const newIsLiked = type === 'like'
      const likeChange = newIsLiked
        ? (localComment.totalLikes || 0) + 1
        : (localComment.totalLikes || 0) - 1

      // Optimistic update for immediate UI feedback
      setLocalComment((prev) => ({
        ...prev,
        isLiked: newIsLiked,
        totalLikes: likeChange,
      }))

      reactionCommentMutation(
        { commentId: comment.id, type },
        {
          onSuccess: () => {
            // Update cache with new reaction state for consistency across components
            if (cacheManager && mainParams && nestedParams) {
              // Determine parent ID for nested comments cache management
              const parentId =
                propParentId ||
                (typeof comment.parent === 'object' ? comment.parent?.id : comment.parent)
              cacheManager.updateCommentReaction(
                comment.id,
                newIsLiked,
                likeChange,
                mainParams,
                nestedParams,
                parentId || undefined,
              )
            }
            onReactionSuccess?.()
            onCommentActionSuccess?.()
          },
          onError: () => {
            // Rollback optimistic update on API failure
            setLocalComment(comment)
          },
        },
      )
    }

    const handleEditClick = () => {
      onStartEdit?.(comment.id)
    }

    const handleCancelEditClick = () => {
      onCancelEdit?.()
    }

    const handleSaveEdit = (newContent: string) => {
      if (!newContent.trim()) return

      if (newContent === localComment.content) {
        onCancelEdit?.()
        return
      }

      const parentId =
        propParentId || (typeof comment.parent === 'object' ? comment.parent?.id : comment.parent)

      setPendingEditContent(newContent)
      setHasEditError(false)
      setIsEditingOptimistic(true)

      onEditComment?.(comment.id, newContent, parentId || undefined, {
        onSuccess: () => {
          setIsEditingOptimistic(false)
          setHasEditError(false)
          setPendingEditContent('')
          onEditSuccess?.()
          onCommentActionSuccess?.()
        },
        onError: () => {
          setIsEditingOptimistic(false)
          setHasEditError(true)
          setLocalComment(comment)
        },
      })

      setLocalComment((prev) => ({
        ...prev,
        content: newContent,
        updatedAt: new Date().toISOString(),
      }))
    }

    const handleRetryEdit = () => {
      if (!pendingEditContent) return

      const parentId =
        propParentId || (typeof comment.parent === 'object' ? comment.parent?.id : comment.parent)

      setHasEditError(false)
      setIsEditingOptimistic(true)

      onEditComment?.(comment.id, pendingEditContent, parentId || undefined, {
        onSuccess: () => {
          setIsEditingOptimistic(false)
          setHasEditError(false)
          setPendingEditContent('')
          onEditSuccess?.()
          onCommentActionSuccess?.()
        },
        onError: () => {
          setIsEditingOptimistic(false)
          setHasEditError(true)
          setLocalComment(comment)
        },
      })

      // Optimistically update UI
      setLocalComment((prev) => ({
        ...prev,
        content: pendingEditContent,
        updatedAt: new Date().toISOString(),
      }))
    }

    const handleDismissEditError = () => {
      setHasEditError(false)
      setPendingEditContent('')
      setLocalComment(comment)
    }

    const handleDeleteComment = () => {
      openBaseWarningPopup({
        title: t('MES-888'),
        description: t('MES-889'),
        confirmText: t('MES-169'),
        cancelText: t('MES-224'),
        onPressConfirm: (close) => {
          // Close the popup immediately after confirmation
          close?.()

          const parentId =
            propParentId ||
            (typeof comment.parent === 'object' ? comment.parent?.id : comment.parent)

          setHasDeleteError(false)
          // Show loading state during delete operation
          setIsDeletingOptimistic(true)

          onDeleteComment?.(comment.id, parentId || undefined, {
            onSuccess: () => {
              setIsDeletingOptimistic(false)
              onDeleteSuccess?.()
              onCommentActionSuccess?.()
            },
            onError: () => {
              setIsDeletingOptimistic(false)
              setHasDeleteError(true)
            },
          })
        },
      })
    }

    const handleRetryDelete = () => {
      const parentId =
        propParentId || (typeof comment.parent === 'object' ? comment.parent?.id : comment.parent)

      setHasDeleteError(false)
      setIsDeletingOptimistic(true)

      onDeleteComment?.(comment.id, parentId || undefined, {
        onSuccess: () => {
          setIsDeletingOptimistic(false)
          onDeleteSuccess?.()
          onCommentActionSuccess?.()
        },
        onError: () => {
          setIsDeletingOptimistic(false)
          setHasDeleteError(true)
        },
      })
    }

    const handleDismissDeleteError = () => {
      setHasDeleteError(false)
    }

    const handleRetryAdd = () => {
      if (!localComment.content) return

      setHasAddError(false)
      setPendingAddContent(localComment.content)

      // Trigger the add comment mutation again
      const parentId =
        propParentId || (typeof comment.parent === 'object' ? comment.parent?.id : comment.parent)
      onRetryAddComment?.(localComment.content, parentId || undefined)
    }

    const handleDismissAddError = () => {
      setHasAddError(false)
      setPendingAddContent('')
      // Remove the optimistic comment from cache
      if (cacheManager && mainParams && nestedParams) {
        const parentId =
          propParentId || (typeof comment.parent === 'object' ? comment.parent?.id : comment.parent)
        cacheManager.removeOptimisticComment(
          comment.id,
          mainParams,
          nestedParams,
          parentId || undefined,
        )
      }
    }
    // Update local comment when prop changes (for cache updates)
    useEffect(() => {
      setLocalComment(comment)
    }, [comment])
    // SAFETY: Validate comment data to prevent crashes
    if (!comment || !comment.id) {
      console.warn('[CommentItem] Invalid comment data:', comment)
      return null
    }

    return (
      <View
        key={comment.id}
        className={cn(
          'flex flex-row justify-between gap-x-2 ',
          !isNested && 'border-b border-custom-neutral-50 py-2',
          isLastComment && 'border-b-0',
          // isHighlighted && 'bg-custom-background-titleTable',
          className,
        )}
        style={{
          // Reduce opacity during optimistic operations for visual feedback, but not for error states
          opacity:
            (comment.isOptimistic && !comment.hasError) ||
            isDeletingOptimistic ||
            isEditingOptimistic
              ? 0.7
              : 1,
        }}
      >
        {/* Main comment layout with avatar and content */}
        <View className="flex flex-1 flex-row items-start gap-x-3">
          {/* User Avatar Section */}
          <>
            {thumbnailURL || url ? (
              <View className="h-12 w-12 flex-shrink-0 rounded-full border border-gray-100">
                <StyledExpoImage
                  source={thumbnailURL || url}
                  className="h-full w-full rounded-full"
                />
              </View>
            ) : (
              <View className="flex-shrink-0">
                <DefaultAvatarIcon width={42} height={42} />
              </View>
            )}
          </>
          {/* Comment Content and Actions */}
          <View className="flex flex-1 flex-col gap-y-3">
            <View className="flex  flex-col gap-y-1">
              <View className="flex flex-col gap-y-1">
                <View className="flex flex-row items-center gap-x-2">
                  <View className="flex flex-1 flex-row items-center gap-x-2">
                    <View className="max-w-[75%]   overflow-hidden  text-ellipsis">
                      <Text size="body6" variant="default" numberOfLines={1}>
                        {commentAuthor?.name || 'Hico User'}
                      </Text>
                    </View>

                    {isAdmin && (
                      <View className="max-w-fit shrink-0 rounded-[4px] bg-custom-success-200 px-2 py-1">
                        <Text size="body8" variant="success">
                          Admin
                        </Text>
                      </View>
                    )}
                  </View>
                  <View className="shrink-0">
                    <Text size="body9" variant="subdued">
                      {relativeDate}
                    </Text>
                  </View>
                  <View className="w-[24px] shrink-0">
                    {isOwnComment &&
                      !isDeletingOptimistic &&
                      !isEditingOptimistic &&
                      !comment.isLoading && (
                        <View className="ml-auto shrink-0">
                          <CommentActionSheetEntry
                            onEdit={handleEditClick}
                            onDelete={handleDeleteComment}
                          />
                        </View>
                      )}
                  </View>
                </View>
                {isEditing ? (
                  <CommentContentEditBox
                    initialValue={localComment.content}
                    isPending={isEditCommentPending}
                    onSave={handleSaveEdit}
                    onCancel={handleCancelEditClick}
                    maxLength={MAX_COMMENT_LENGTH}
                    t={t}
                  />
                ) : (
                  <CommentContent content={localComment.content} />
                )}

                {/* Error retry components */}
                {hasEditError && (
                  <View className="mt-2">
                    <ErrorRetry
                      onRetry={handleRetryEdit}
                      onDismiss={handleDismissEditError}
                      showDismiss={true}
                    />
                  </View>
                )}

                {hasDeleteError && (
                  <View className="mt-2">
                    <ErrorRetry
                      onRetry={handleRetryDelete}
                      onDismiss={handleDismissDeleteError}
                      showDismiss={true}
                    />
                  </View>
                )}

                {isOptimisticWithError && (
                  <View className="mt-2">
                    <ErrorRetry
                      onRetry={handleRetryAdd}
                      onDismiss={handleDismissAddError}
                      showDismiss={true}
                    />
                  </View>
                )}
              </View>
              {/* Comment Actions (Like, Reply, etc.) */}
              {!isEditing && (
                <>
                  {/* Show loading state during operations */}
                  {comment.isLoading || isDeletingOptimistic || isEditingOptimistic ? (
                    <View className="flex flex-row items-center py-2">
                      <ActivityIndicator size="small" color={primary[500]} />
                      <Text size="body6" variant="subdued" className="ml-2">
                        {isDeletingOptimistic
                          ? t('MES-879') || 'Deleting...'
                          : isEditingOptimistic
                            ? t('MES-855')
                            : t('MES-855')}
                      </Text>
                    </View>
                  ) : (
                    <>
                      {/* Show action buttons for authenticated users */}
                      {user && status === 'success' && (
                        <CommentBottomAction
                          hasNestedComments={totalNestedComments > 0}
                          comment={localComment}
                          isNested={isNested}
                          onReaction={handleReactionComment}
                          onReply={() => onReply?.(comment)}
                          onDelete={handleDeleteComment}
                          onEdit={handleEditClick}
                          isLoading={isReactionCommentPending}
                          isDeleting={isDeletingOptimistic}
                          currentUserId={user.id}
                          isOwnComment={isOwnComment}
                        />
                      )}
                    </>
                  )}
                </>
              )}
            </View>

            {/* Nested Comments Section - Shows replies to this comment */}
            <NestedComments
              totalNestedComments={totalNestedComments}
              parentId={comment.id}
              relationTo={relationTo}
              isNested={true}
              onReply={onReply}
              onEditComment={onEditComment}
              onStartEdit={onStartEdit}
              onCancelEdit={onCancelEdit}
              onDeleteComment={onDeleteComment}
              onRetryAddComment={onRetryAddComment}
              isEditCommentPending={isEditCommentPending}
              editingCommentId={editingCommentId}
              cacheManager={cacheManager}
              mainParams={mainParams}
              nestedParams={nestedParams}
              forceOpenNestedComments={forceOpenNestedComments}
              nestedCommentId={nestedCommentId}
              showLoadmoreNestedComments={showLoadmoreNestedComments}
            />
          </View>
        </View>
      </View>
    )
  },
)
CommentItem.displayName = 'CommentItem'
// Like, Reply, Report
interface CommentBottomActionProps {
  comment: Comment
  isNested?: boolean
  onReaction: (type: 'like' | 'unlike') => void
  onReply?: () => void
  onEdit?: () => void
  onDelete?: () => void
  isLoading?: boolean
  isDeleting?: boolean
  currentUserId?: string
  hasNestedComments?: boolean
  isOwnComment?: boolean
}
const CommentBottomAction = memo(
  ({
    hasNestedComments = false,
    comment,
    isNested,
    onReaction,
    onReply,
    isLoading,
    isDeleting,
    isOwnComment,
  }: CommentBottomActionProps) => {
    const { isLiked } = comment

    const { t } = useTranslation()
    return (
      <View
        className="flex flex-row items-center gap-x-4"
        style={{
          gap: 16,
        }}
      >
        <TouchableOpacity
          onPress={() => onReaction(isLiked ? 'unlike' : 'like')}
          disabled={isLoading || isDeleting}
          style={{ opacity: isLoading || isDeleting ? 0.6 : 1 }}
          hitSlop={10}
        >
          <View className="flex flex-row items-center gap-x-2">
            <Svg width={18} height={18} viewBox="0 0 18 18" fill={isLiked ? primary[500] : 'none'}>
              <Path
                d="M5.60986 13.7626L7.93486 15.5626C8.23486 15.8626 8.90986 16.0126 9.35986 16.0126H12.2099C13.1099 16.0126 14.0849 15.3376 14.3099 14.4376L16.1099 8.9626C16.4849 7.9126 15.8099 7.0126 14.6849 7.0126H11.6849C11.2349 7.0126 10.8599 6.6376 10.9349 6.1126L11.3099 3.7126C11.4599 3.0376 11.0099 2.2876 10.3349 2.0626C9.73486 1.8376 8.98486 2.1376 8.68486 2.5876L5.60986 7.1626"
                stroke={isLiked ? 'none' : '#8B8C99'}
                strokeWidth={1.5}
                strokeMiterlimit={10}
              />
              <Path
                d="M1.78516 13.7625V6.41255C1.78516 5.36255 2.23516 4.98755 3.28516 4.98755H4.03516C5.08516 4.98755 5.53516 5.36255 5.53516 6.41255V13.7625C5.53516 14.8125 5.08516 15.1875 4.03516 15.1875H3.28516C2.23516 15.1875 1.78516 14.8125 1.78516 13.7625Z"
                stroke={isLiked ? 'none' : '#8B8C99'}
                strokeWidth={1.5}
                strokeLinecap="round"
                strokeLinejoin="round"
              />
            </Svg>
            <Text size="body6" variant={isLiked ? 'primary' : 'subdued'}>
              {comment.totalLikes}
            </Text>
          </View>
        </TouchableOpacity>
        {!isNested && (
          <TouchableOpacity onPress={onReply} disabled={isDeleting}>
            <Text
              size="body6"
              variant={hasNestedComments ? 'primary' : 'subdued'}
              style={{ opacity: isDeleting ? 0.6 : 1 }}
            >
              {t('MES-849')}
            </Text>
          </TouchableOpacity>
        )}

        {!isOwnComment && (
          <Link
            href={{
              pathname: APP_ROUTES.COMMENTS.children?.[AppRoutesEnum.REPORT_COMMENT].path,
              params: {
                commentId: comment.id,
              },
            }}
            asChild
          >
            <TouchableOpacity>
              <ReportCommentIcon width={18} height={18} />
            </TouchableOpacity>
          </Link>
        )}
      </View>
    )
  },
)
CommentBottomAction.displayName = 'CommentBottomAction'
// Comment Action Sheet Entry
interface CommentActionSheetEntryProps {
  onEdit?: () => void
  onDelete?: () => void
}
const CommentActionSheetEntry = memo(({ onEdit, onDelete }: CommentActionSheetEntryProps) => {
  const { handleOpenCommentActionSheet, handleCloseCommentActionSheet } =
    useOpenCommentActionSheet()
  return (
    <TouchableOpacity
      className="self-start"
      onPress={() => {
        Keyboard.dismiss()
        handleOpenCommentActionSheet({
          onPressEdit: () => {
            onEdit?.()
            handleCloseCommentActionSheet()
          },
          onPressDelete: () => {
            onDelete?.()
            handleCloseCommentActionSheet()
          },
        })
      }}
      hitSlop={10}
    >
      <Svg width={18} height={18} viewBox="0 0 18 18" fill="none">
        <Path
          d="M7.5 14.25C7.5 15.075 8.175 15.75 9 15.75C9.825 15.75 10.5 15.075 10.5 14.25C10.5 13.425 9.825 12.75 9 12.75C8.175 12.75 7.5 13.425 7.5 14.25Z"
          stroke="#1157C8"
          strokeWidth={1.5}
        />
        <Path
          d="M7.5 3.75C7.5 4.575 8.175 5.25 9 5.25C9.825 5.25 10.5 4.575 10.5 3.75C10.5 2.925 9.825 2.25 9 2.25C8.175 2.25 7.5 2.925 7.5 3.75Z"
          stroke="#1157C8"
          strokeWidth={1.5}
        />
        <Path
          d="M7.5 9C7.5 9.825 8.175 10.5 9 10.5C9.825 10.5 10.5 9.825 10.5 9C10.5 8.175 9.825 7.5 9 7.5C8.175 7.5 7.5 8.175 7.5 9Z"
          stroke="#1157C8"
          strokeWidth={1.5}
        />
      </Svg>
    </TouchableOpacity>
  )
})
CommentActionSheetEntry.displayName = 'CommentActionSheetEntry'

// Comment Content
interface CommentContentProps {
  content: string
}
const CommentContent = memo(({ content }: CommentContentProps) => {
  const [isExpanded, setIsExpanded] = useState(false)
  const { t } = useTranslation()
  const MAX_CHARACTERS = 256
  const fullContent = content || ''
  const isTruncatable = fullContent.length > MAX_CHARACTERS
  const visibleContent =
    isExpanded || !isTruncatable ? fullContent : `${fullContent.slice(0, MAX_CHARACTERS).trim()}...`

  return (
    <View>
      <Text size="body7" variant="default">
        {visibleContent}{' '}
        {isTruncatable && (
          <Text
            size="body7"
            variant="default"
            onPress={() => setIsExpanded((prev) => !prev)}
            className="ml-1 whitespace-nowrap !font-semibold underline"
            style={{ fontWeight: '500' }}
          >
            {isExpanded ? t('MES-493') : t('MES-22')}
          </Text>
        )}
      </Text>
    </View>
  )
})
CommentContent.displayName = 'CommentContent'
// Comment Content Edit Box
interface CommentContentEditBoxProps {
  initialValue: string
  isPending: boolean
  onSave: (content: string) => void
  onCancel: () => void
  maxLength: number
  t: (key: string) => string
}
const CommentContentEditBox = memo(
  ({ initialValue, isPending, onSave, onCancel, maxLength, t }: CommentContentEditBoxProps) => {
    const [value, setValue] = useState(initialValue)
    const prevcontentRef = useRef<string>(initialValue)
    const prevContentTrimmed = prevcontentRef?.current?.trim()
    const trimmedValue = value.trim()
    const isDisabled =
      isPending ||
      !trimmedValue ||
      trimmedValue.length > maxLength ||
      prevContentTrimmed === trimmedValue
    return (
      <View className="mt-2 flex flex-col gap-y-2">
        <TextInput
          value={value}
          onChangeText={setValue}
          multiline
          className="max-h-[160px] min-h-[60px] rounded-lg border border-primary-300 bg-white px-3 py-2 text-base "
          placeholder={t('MES-887')}
          autoFocus
          editable={!isPending}
          style={{ maxHeight: 160 }}
        />
        <View className="-mt-1 flex flex-row justify-between">
          <Text
            size="body8"
            variant={trimmedValue.length > maxLength ? 'error' : 'subdued'}
          >{`${trimmedValue?.length || 0}/${maxLength}`}</Text>
        </View>
        <View className="flex flex-row items-center gap-x-2">
          <TouchableOpacity
            onPress={() => onSave(trimmedValue)}
            disabled={isDisabled}
            className="rounded-md bg-primary-500 px-4 py-2"
            style={{ opacity: isDisabled ? 0.6 : 1 }}
          >
            {isPending ? (
              <ActivityIndicator size="small" color="#fff" />
            ) : (
              <Text size="body6" className="text-white">
                {t('MES-500')}
              </Text>
            )}
          </TouchableOpacity>
          <TouchableOpacity
            onPress={onCancel}
            disabled={isPending}
            className="rounded-md border border-neutral-300 px-4 py-2"
          >
            <Text size="body6" variant="subdued">
              {t('MES-45') || 'Cancel'}
            </Text>
          </TouchableOpacity>
        </View>
      </View>
    )
  },
)
CommentContentEditBox.displayName = 'CommentContentEditBox'
