import { AuthenticationContext } from '@/contexts/AuthenticationContext/AuthenticationContext'
import { useContext } from 'react'

// Custom hook to access authentication state
export const useAuthentication = () => {
  const authenticationContext = useContext(AuthenticationContext)

  if (!authenticationContext) {
    throw new Error('useAuthentication must be used within an AuthenticationProvider')
  }

  return authenticationContext
}
