import { useAuthentication } from '@/hooks/auth/useAuthentication'
import { PaginatedDocs } from '@/types/global.type'
import { InfiniteData, useQueryClient } from '@tanstack/react-query'
import { useCallback } from 'react'
import { PushNotificationMessagesTabEnum } from '../../enums/push-notification.enum'
import { GetUserUnviewedNotificationCountResponse, UserPushNotification } from '../../types'
import { unifiedNotificationQueryKeys } from '../query/queryKeys'

export const useAddNotificationToCache = () => {
  const queryClient = useQueryClient()
  const { user } = useAuthentication()

  /**
   * Check if all required query data exists in cache
   * Returns true if all data exists, false otherwise
   */
  const checkCacheExists = (): boolean => {
    const countQueryKey = [unifiedNotificationQueryKeys['user-unviewed-notification-count'].base()]
    const countData =
      queryClient.getQueryData<GetUserUnviewedNotificationCountResponse>(countQueryKey)

    const allTabQueryKey = [
      ...unifiedNotificationQueryKeys['user-push-notifications'].base(),
      PushNotificationMessagesTabEnum.ALL,
    ]
    const allTabData =
      queryClient.getQueryData<InfiniteData<PaginatedDocs<UserPushNotification>>>(allTabQueryKey)

    const notViewedTabQueryKey = [
      ...unifiedNotificationQueryKeys['user-push-notifications'].base(),
      PushNotificationMessagesTabEnum.NOT_VIEWED,
    ]
    const notViewedTabData =
      queryClient.getQueryData<InfiniteData<PaginatedDocs<UserPushNotification>>>(
        notViewedTabQueryKey,
      )

    // console.log('🔥 checkCacheExists: countData', countData)
    // console.log('🔥 checkCacheExists: allTabData', allTabData)
    // console.log('🔥 checkCacheExists: notViewedTabData', notViewedTabData)

    // Return true only if count data exists (required)
    // Tabs can be empty but count must exist
    return countData !== undefined
  }

  /**
   * Add a new notification to the cache and increment the unviewed count
   * This is used when a new notification is received (foreground/background)
   * Returns an object indicating what was updated and what needs invalidation
   */
  const handleAddNotificationToCache = useCallback(
    (
      notification: UserPushNotification,
    ): {
      countUpdated: boolean
      tabsUpdated: boolean
    } => {
      // console.log('🔥 handleAddNotificationToCache: notification', notification)

      // Don't add to cache if no user is logged in
      if (!user) {
        console.log('🔥 handleAddNotificationToCache: No user logged in, skipping cache update')
        return { countUpdated: false, tabsUpdated: false }
      }

      const countQueryKey = [
        unifiedNotificationQueryKeys['user-unviewed-notification-count'].base(),
      ]
      const countData =
        queryClient.getQueryData<GetUserUnviewedNotificationCountResponse>(countQueryKey)

      const allTabQueryKey = [
        ...unifiedNotificationQueryKeys['user-push-notifications'].base(),
        PushNotificationMessagesTabEnum.ALL,
      ]
      const allTabData =
        queryClient.getQueryData<InfiniteData<PaginatedDocs<UserPushNotification>>>(allTabQueryKey)

      const notViewedTabQueryKey = [
        ...unifiedNotificationQueryKeys['user-push-notifications'].base(),
        PushNotificationMessagesTabEnum.NOT_VIEWED,
      ]
      const notViewedTabData =
        queryClient.getQueryData<InfiniteData<PaginatedDocs<UserPushNotification>>>(
          notViewedTabQueryKey,
        )

      let countUpdated = false
      let tabsUpdated = false

      // 1. Update count if it exists
      if (countData) {
        // console.log('🔥 handleAddNotificationToCache: Updating count cache')
        queryClient.setQueryData<GetUserUnviewedNotificationCountResponse>(
          countQueryKey,
          (data) => {
            const newData = {
              ...data,
              id: data?.id || '',
              unviewedNotificationsCount: (data?.unviewedNotificationsCount || 0) + 1,
            }
            // console.log('🔥 handleAddNotificationToCache: count updated from', data, 'to', newData)
            return newData
          },
        )
        countUpdated = true
      } else {
        // console.log('🔥 handleAddNotificationToCache: Count cache does not exist, skipping')
      }

      // 2. Update tabs only if they exist
      if (allTabData || notViewedTabData) {
        // console.log('🔥 handleAddNotificationToCache: Updating tab caches')

        // Update "all" tab if it exists
        if (allTabData) {
          queryClient.setQueryData<InfiniteData<PaginatedDocs<UserPushNotification>>>(
            allTabQueryKey,
            (oldData) => {
              if (!oldData) return oldData

              // Add notification to the first page, first position
              const updatedPages = [...oldData.pages]
              if (updatedPages.length > 0) {
                updatedPages[0] = {
                  ...updatedPages[0],
                  docs: [notification, ...updatedPages[0].docs],
                  totalDocs: (updatedPages[0].totalDocs || 0) + 1,
                }
              }

              return {
                ...oldData,
                pages: updatedPages,
              }
            },
          )
          // console.log('🔥 handleAddNotificationToCache: "all" tab updated')
        }

        // Update "not-viewed" tab if it exists
        if (notViewedTabData) {
          queryClient.setQueryData<InfiniteData<PaginatedDocs<UserPushNotification>>>(
            notViewedTabQueryKey,
            (oldData) => {
              if (!oldData) return oldData

              // Add notification to the first page, first position
              const updatedPages = [...oldData.pages]
              if (updatedPages.length > 0) {
                updatedPages[0] = {
                  ...updatedPages[0],
                  docs: [notification, ...updatedPages[0].docs],
                  totalDocs: (updatedPages[0].totalDocs || 0) + 1,
                }
              }

              return {
                ...oldData,
                pages: updatedPages,
              }
            },
          )
          // console.log('🔥 handleAddNotificationToCache: "not-viewed" tab updated')
        }

        tabsUpdated = true
      } else {
        // console.log('🔥 handleAddNotificationToCache: Tab caches do not exist, skipping')
      }

      // console.log('🔥 handleAddNotificationToCache: Update summary:', {
      //   countUpdated,
      //   tabsUpdated,
      // })
      return { countUpdated, tabsUpdated }
    },
    [user, queryClient],
  )

  /**
   * Decrement the unviewed notification count
   * This is used when a notification is marked as viewed
   */
  const handleDecrementUnviewedCount = (decrementBy: number = 1) => {
    // Don't update count if no user is logged in
    if (!user) {
      console.log('🔥 handleDecrementUnviewedCount: No user logged in, skipping count update')
      return
    }

    const countQueryKey = [unifiedNotificationQueryKeys['user-unviewed-notification-count'].base()]
    queryClient.setQueryData<GetUserUnviewedNotificationCountResponse>(countQueryKey, (data) => {
      return {
        ...data,
        id: data?.id || '',
        unviewedNotificationsCount: Math.max(
          0,
          (data?.unviewedNotificationsCount || 0) - decrementBy,
        ),
      }
    })
  }

  /**
   * Reset the unviewed notification count to 0
   * This is used when all notifications are marked as viewed
   */
  const handleResetUnviewedCount = () => {
    // Don't reset count if no user is logged in
    if (!user) {
      console.log('🔥 handleResetUnviewedCount: No user logged in, skipping count reset')
      return
    }

    const countQueryKey = [unifiedNotificationQueryKeys['user-unviewed-notification-count'].base()]
    queryClient.setQueryData<GetUserUnviewedNotificationCountResponse>(countQueryKey, (data) => {
      return {
        ...data,
        id: data?.id || '',
        unviewedNotificationsCount: 0,
      }
    })
  }

  return {
    handleAddNotificationToCache,
    handleDecrementUnviewedCount,
    handleResetUnviewedCount,
    checkCacheExists,
  }
}
