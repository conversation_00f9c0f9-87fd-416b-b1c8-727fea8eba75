import { LocaleEnum } from '@/enums/locale.enum'
import dayjs from 'dayjs'
import 'dayjs/locale/ja'
import 'dayjs/locale/vi'
import relativeTime from 'dayjs/plugin/relativeTime'

// Extend dayjs with relativeTime plugin
dayjs.extend(relativeTime)

export const isDateOverlapped = (
  firstDateRange: { start: Date; end: Date },
  secondDateRange: { start: Date; end: Date },
) => {
  // f-start -----------f-end
  //          s-start -------------- s-end
  const overlappedEnd =
    firstDateRange.start < secondDateRange.start &&
    firstDateRange.end > secondDateRange.start &&
    firstDateRange.end < secondDateRange.end
  // f-start ----------------------- f-end
  //          s-start --------- s-end
  const overlappedBetween =
    firstDateRange.start < secondDateRange.start && firstDateRange.end > secondDateRange.end
  //            f-start -----------f-end
  // s-start ------------------------------ s-end
  const overlappedBetween2 =
    secondDateRange.start < firstDateRange.start && secondDateRange.end > firstDateRange.end
  //            f-start -----------f-end
  // s-start -------------- s-end
  const overlappedStart =
    firstDateRange.start > secondDateRange.start &&
    firstDateRange.start < secondDateRange.end &&
    firstDateRange.end > secondDateRange.end
  return overlappedEnd || overlappedBetween || overlappedBetween2 || overlappedStart
}

/**
 * function format time from UTC time to 00:00
 */
export const formatTime = (date: Date) => {
  if (!date) {
    return ''
  }
  const hours = date.getHours()
  const minutes = date.getMinutes()
  return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}`
}

/**
 * Get first date and last date of month
 * @param date Date
 */
export const getFirstAndLastDayOfMonth = (date: Date) => {
  const today = new Date(date)
  const firstDayOfMonth = new Date(today.getFullYear(), today.getMonth(), 1)
  const lastDayOfMonth = new Date(today.getFullYear(), today.getMonth() + 1, 0)
  const firstDayFormatted = dateToMDY(firstDayOfMonth)
  const lastDayFormatted = dateToMDY(lastDayOfMonth)

  return { startDate: firstDayFormatted, endDate: lastDayFormatted }
}

/**
 * Get the first and the last day of current week
 * @param d
 */
export const getFirstAndLastDayOfWeek = (d: Date | string) => {
  const firstDayOfWeek = 1 // Monday
  const date = new Date(d)
  const dayOfWeek = date.getDay()
  const diff = dayOfWeek >= firstDayOfWeek ? dayOfWeek - firstDayOfWeek : 6

  let firstDate = new Date(date)
  firstDate.setDate(date.getDate() - diff)
  firstDate = setStartOfDate(firstDate)

  let lastDate = new Date(firstDate)
  lastDate.setDate(firstDate.getDate() + 6)
  lastDate = setEndOfDate(lastDate)

  return {
    startDate: dateToMDY(firstDate),
    endDate: dateToMDY(lastDate),
  }
}

/**
 * Convert date to dd/MM/YYYY format
 * @param d
 */
export const dateToDMY = (d: Date | string) => {
  const date = new Date(d)
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  return `${day}/${month}/${year}`
}

/**
 * Convert date to MM/dd/yyyy format
 * @param d
 * */
export const dateToMDY = (d: Date | string) => {
  const date = new Date(d)
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  return `${month}/${day}/${year}`
}

export const dateToYMD = (d: Date | string) => {
  const date = new Date(d)
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  return `${year}/${month}/${day}`
}

/**
 * Convert date to yyyy/MM/dd format
 * @param d
 * */
export const dateToYYYYMMDD = (d: Date | string) => {
  const date = new Date(d)
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  return `${year}-${month}-${day}`
}

export const dateToYYYYMMDDHHMM = (d: Date | string, type?: 'start' | 'end') => {
  const date = new Date(d)
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  if (type && type === 'end') {
    date.setHours(23, 59, 59)
  }
  const hours = date.getHours()
  const minutes = date.getMinutes()
  return `${year}-${month}-${day} ${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}`
}

export const dateToYYYYMMDDconvert = (d: Date | string) => {
  const date = new Date(d)
  let year = date.getFullYear()
  let month = String(date.getMonth() + 1).padStart(2, '0')
  let day = String(date.getDate()).padStart(2, '0')

  if (date.getHours() === 0 && date.getMinutes() === 0 && date.getSeconds() === 0) {
    return `${year}-${month}-${day}`
  } else {
    const nextDay = new Date(date)
    nextDay.setDate(date.getDate() + 1)
    year = nextDay.getFullYear()
    month = String(nextDay.getMonth() + 1).padStart(2, '0')
    day = String(nextDay.getDate()).padStart(2, '0')
    return `${year}-${month}-${day}`
  }
}

/**
 * Add minutes to date
 * @param date Date
 * @param minutes number
 */
export const addMinutes = (date: Date, minutes: number) => {
  return new Date(date.getTime() + minutes * 60000)
}

/**
 * Convert minutes to text string
 * @param minutes number
 * @example 1 hour 30 minutes
 */
export const convertMinutesToText = (minutes: number) => {
  const hours = Math.floor(minutes / 60) // Calculate the hour portion
  const minutesRemainder = minutes % 60 // Calculate the remaining minutes

  let result = ''

  if (hours > 0) {
    result += `${hours} hour`
    if (hours > 1) {
      result += 's'
    }
  }

  if (minutesRemainder > 0) {
    if (result.length > 0) {
      result += ' '
    }
    result += `${minutesRemainder} minute`
    if (minutesRemainder > 1) {
      result += 's'
    }
  }

  return result
}

/**
 * Convert date range to hour format
 * @param start Date
 * @param end Date
 * @example 9:00  - 10:00
 */
export const convertDateRangeToText = (start: Date, end: Date): string => {
  const startTime = start
    .toLocaleTimeString([], { hour: 'numeric', minute: '2-digit', hour12: false })
    .replace(/\s*(AM|PM)/, '')
  const endTime = end
    .toLocaleTimeString([], { hour: 'numeric', minute: '2-digit', hour12: false })
    .replace(/\s*(AM|PM)/, '')

  return `${startTime} - ${endTime}`
}

/**
 * check date is between a date range
 * @param dateTime
 * @param dateRange
 */
export const isDateTimeOverlappingRange = (
  dateTime: Date,
  dateRange: { start: Date; end: Date },
): boolean => {
  const isAfterStart = dateTime >= dateRange.start
  const isBeforeEnd = dateTime <= dateRange.end
  return isAfterStart && isBeforeEnd
}

/**
 * Check is today
 * @param dateToCheck
 */
export const isToday = (dateToCheck: Date, today = new Date()): boolean => {
  return (
    dateToCheck.getFullYear() === today.getFullYear() &&
    dateToCheck.getMonth() === today.getMonth() &&
    dateToCheck.getDate() === today.getDate()
  )
}

/**
 * Set date at the end of the day
 * @param date
 */
export const setEndOfDate = (date: Date) => {
  date.setHours(23, 59, 59, 999)
  return date
}

/**
 * Set date at the start of the day
 * @param date
 */
export const setStartOfDate = (date: Date) => {
  date.setHours(0, 0, 0, 0)
  return date
}

export const minutesBetween2Dates = (start: Date, end: Date) => {
  const diffInMilliseconds = Math.abs(end.getTime() - start.getTime())
  return Math.floor(diffInMilliseconds / 1000 / 60)
}

/**
 * function format time from UTC time to get hour 00
 */
export const getHourFromUTC = (date: Date | string) => {
  const newDate = new Date(date)
  if (!date) {
    return ''
  }
  const hours = newDate.getHours()
  return `${hours.toString().padStart(2, '0')}`
}

/**
 * function format time from UTC time to get hour 00
 */
export const getMinutesFromUTC = (date: Date | string) => {
  const newDate = new Date(date)
  if (!date) {
    return ''
  }
  const minutes = newDate.getMinutes()
  return `${minutes.toString().padStart(2, '0')}`
}

/**
 * convert time from 10:44:12  --> 10:00:00
 * @param dateString
 * @returns
 */
export const convertDate = (dateString: string) => {
  const date = new Date(dateString)
  date.setMinutes(0)
  date.setSeconds(0)
  return date.toString()
}

/**
 * convert time from 10:00:00  --> 10:15:00
 * @param dateString
 * @returns
 */
export const convertDateadd15Min = (dateString: string) => {
  const date = new Date(dateString)
  date.setMinutes(15)
  date.setSeconds(0)
  return date.toString()
}

// Kiểm tra xem ngày bắt đầu và kết thức có trong 1 ngày không
export const handleCheckFullDate = (startDate: Date | string, endDate: Date | string) => {
  const startDateConvert = new Date(startDate)
  const endDateConvert = new Date(endDate)

  const minDate = new Date(0)

  const isSameDay = startDateConvert.toDateString() === endDateConvert.toDateString()

  const isNotMidnightToMidnight = !(
    startDateConvert.getHours() === 0 &&
    startDateConvert.getMinutes() === 0 &&
    endDateConvert.getHours() === 23
  )

  return (
    isSameDay &&
    isNotMidnightToMidnight &&
    startDateConvert !== minDate &&
    endDateConvert !== minDate
  )
}

/**
 * convert Thứ hai, 2024/02/21
 * @param date
 * @returns
 */
export const convertDateFormat = (date: string | Date) => {
  const newDate = new Date(date)

  const daysOfWeek = ['MES-652', 'MES-653', 'MES-654', 'MES-655', 'MES-656', 'MES-657', 'MES-658']

  const dayOfWeek = daysOfWeek[newDate.getDay()]

  const year = newDate.getFullYear()

  const month = (newDate.getMonth() + 1 < 10 ? '0' : '') + (newDate.getMonth() + 1)

  const day = (newDate.getDate() < 10 ? '0' : '') + newDate.getDate()

  const dayFormat = `${year}/${month}/${day}`

  return {
    dayOfWeek,
    dayFormat,
  }
}

export const shiftWeek = (date: Date, days: number = 1) => {
  const result = new Date(date)
  result.setDate(result.getDate() + 7 * days)
  return result
}

export const shiftMonth = (date: Date, months: number = 1) => {
  const result = new Date(date)
  return new Date(result.getFullYear(), result.getMonth() + months, 1)
}

export const getQuarterDateRange = (date: Date): { fromDate: Date; toDate: Date } => {
  const dateMonth = date.getMonth()
  const dateYear = date.getFullYear()
  // const dateQuarter = (dateMonth + 1)/3
  const fromMonth = Math.floor(dateMonth / 3) * 3
  const toMonth = Math.floor(dateMonth / 3) * 3 + 2
  const fromDate = new Date(dateYear, fromMonth, 1)
  const toDate = new Date(dateYear, toMonth + 1, 0)
  return { fromDate, toDate }
}

/**
 * get time in range, ex: start: 2024/03/12 ,end : 2024/03/15
 *  return [2024/03/12, 2024/03/13, 2024/03/14, 2024/03/15]
 * @param startDate
 * @param endDate
 * @returns
 */

export const getDatesInRange = (startDate: string | Date, endDate: string | Date) => {
  const dates: string[] = []
  const _endDate = new Date(endDate)
  const currentDate = new Date(startDate)

  while (currentDate <= _endDate) {
    dates.push(new Date(currentDate).toLocaleDateString())
    currentDate.setDate(currentDate.getDate() + 1)
  }

  return dates
}

export const getWeeklyAndDate = (date: Date | string) => {
  const newDate = new Date(date)

  const daysOfWeek = ['MES-652', 'MES-653', 'MES-654', 'MES-655', 'MES-656', 'MES-657', 'MES-658']

  const dayOfWeek = daysOfWeek[newDate.getDay()]

  const day = (newDate.getDate() < 10 ? '0' : '') + newDate.getDate()

  return {
    dayOfWeek,
    day,
  }
}

export const convertDateTime = (start: Date): string => {
  const startTime =
    start.getHours().toString().padStart(2, '0') +
    ':' +
    start.getMinutes().toString().padStart(2, '0')
  return startTime
}

export const CheckSameMonth = (date: Date, currentDate: Date) => {
  const thangNam1 = date.getMonth() + '-' + date.getFullYear()
  const thangNam2 = currentDate.getMonth() + '-' + currentDate.getFullYear()

  return thangNam1 === thangNam2
}

export const convertTimeShowMonthYear = (date: string | Date) => {
  const newDate = new Date(date)
  const month = (newDate.getMonth() < 9 ? '0' : '') + (newDate.getMonth() + 1)
  const year = newDate.getFullYear()

  return `${month} / ${year}`
}

/**
 *  convert new Date() : 2024-04-24T00:00:00.000Z --> tháng 4, 2024
 * @param date
 * @returns
 */
export const convertMonthYear = (date: Date | string) => {
  const months = [
    'MES-813',
    'MES-814',
    'MES-815',
    'MES-816',
    'MES-817',
    'MES-818',
    'MES-819',
    'MES-820',
    'MES-821',
    'MES-822',
    'MES-823',
    'MES-824',
  ]
  const newDate = new Date(date)
  const month = months[newDate.getMonth()]
  const year = newDate.getFullYear()
  return { month, year }
}

export const addHoursToDate = (date: Date, hours: number) => {
  const newDate = new Date(date)

  newDate.setHours(newDate.getHours() + hours)
  return newDate
}

export const dateFormatterWorkLoadAddHour = (newDate: Date, hour: number) => {
  if (!newDate) {
    return
  }
  const date = new Date(newDate)
  date.setHours(date.getHours() + hour)
  const year = date.getFullYear().toString().padStart(4, '0')
  const month = (date.getMonth() + 1).toString().padStart(2, '0')
  const day = date.getDate().toString().padStart(2, '0')
  const hours = date.getHours().toString().padStart(2, '0')
  const minutes = date.getMinutes().toString().padStart(2, '0')

  return `${day}-${month}-${year} ${hours}:${minutes}`
}

export const calculatorDuration = (startDate: Date, endDate: Date) => {
  const time = new Date(endDate).getTime() - new Date(startDate).getTime()

  const hour = time / 1000 / 3600
  return Math.floor(hour)
}

export const generateKey = (date: Date | string) => {
  date = new Date(date)
  return `${date.getFullYear()}-${date.getMonth().toString().padStart(2, '0')}-${date.getDate().toString().padStart(2, '0')}`
}

export const checkEqualDate = (date1: Date | string, date2: Date | string) => {
  date1 = new Date(date1)
  date2 = new Date(date2)
  return (
    date1.getDate() === date2.getDate() &&
    date1.getMonth() === date2.getMonth() &&
    date1.getFullYear() === date2.getFullYear()
  )
}

export const checkIsCurrenTime = (date1: Date | string, date2: Date | string) => {
  date1 = new Date(date1)
  date2 = new Date(date2)
  const current = new Date()
  return date1 <= current && current <= date2
}

export const calculatePreviousMonths = (date: Date, months: number) => {
  const newDate = new Date(date)
  newDate.setMonth(date.getMonth() - months)
  newDate.setDate(1)

  return newDate
}

export const calculateNextMonths = (date: Date, months: number) => {
  const newDate = new Date(date)
  newDate.setMonth(date.getMonth() + months)
  newDate.setDate(0)

  return newDate
}

export const minusDate = (date: Date, dates: number) => {
  const newDate = new Date(date)
  newDate.setDate(date.getDate() - dates)
  return newDate
}

export const addDate = (date: Date, dates: number) => {
  const newDate = new Date(date)
  newDate.setDate(date.getDate() + dates)
  return newDate
}

export const checkEqualMonth = (date1: Date | string, date2: Date | string) => {
  date1 = new Date(date1)
  date2 = new Date(date2)
  return date1.getMonth() === date2.getMonth() && date1.getFullYear() === date2.getFullYear()
}

export const checkEqualWeek = (date1: Date | string, date2: Date | string) => {
  const oneDayUnit = 24 * 60 * 60 * 1000
  date1 = new Date(date1)
  date2 = new Date(date2)

  const startOfWeek1 = new Date(
    date1.getTime() - (date1.getDay() !== 0 ? date1.getDay() - 1 : 6) * oneDayUnit,
  )
  const startOfWeek2 = new Date(
    date2.getTime() - (date2.getDay() !== 0 ? date2.getDay() - 1 : 6) * oneDayUnit,
  )

  return checkEqualDate(startOfWeek1, startOfWeek2)
}

export const setDate = (date: Date | string, year?: number, month?: number, da?: number) => {
  const newDate = new Date(date)
  if (year) newDate.setFullYear(year)
  if (month) newDate.setMonth(month)
  if (da) newDate.setDate(da)
  return newDate
}

export const getDateRangeInMonth = (date: Date) => {
  const today = new Date(date)
  const firstDayOfMonth = new Date(today.getFullYear(), today.getMonth(), 1)
  const lastDayOfMonth = new Date(today.getFullYear(), today.getMonth() + 1, 0)

  return { fromDate: firstDayOfMonth, toDate: lastDayOfMonth }
}

/**
 * Adds a specified number of milliseconds to a date and returns the new date
 * @param date The base date
 * @param ms Number of milliseconds to add
 * @returns A new Date object with the added milliseconds
 */
export const addMillisecondsToDate = (date: Date, ms: number): Date => {
  const newDate = new Date(date)
  newDate.setTime(newDate.getTime() + ms)
  return newDate
}

/**
 * Adds a specified number of seconds to a date and returns the new date
 * @param date The base date
 * @param seconds Number of seconds to add
 * @returns A new Date object with the added seconds
 */
export const addSecondsToDate = (date: Date, seconds: number): Date => {
  const newDate = new Date(date)
  newDate.setTime(newDate.getTime() + seconds * 1000)
  return newDate
}

export const DDMMYYYYregex = /^\d{2}\/\d{2}\/\d{4}$/

export const checkDateMatchFormat = (date: string, regex: RegExp) => {
  switch (regex) {
    case DDMMYYYYregex:
      return DDMMYYYYregex.test(date)
    default:
      return true
  }
}

export const formatRelativeDate = ({
  dateString,
  locale,
}: {
  dateString: string
  locale: string
}) => {
  try {
    // Validate input parameters
    if (!dateString || typeof dateString !== 'string') {
      console.warn('[formatRelativeDate] Invalid dateString:', dateString)
      return null
    }

    // Set locale based on primary language
    const defaultLocale = locale || LocaleEnum.VI
    dayjs.locale(defaultLocale)

    // Create dayjs instance and validate it
    const dayjsInstance = dayjs(dateString)
    if (!dayjsInstance.isValid()) {
      console.warn('[formatRelativeDate] Invalid date format:', dateString)
      return null
    }

    return dayjsInstance.fromNow()
  } catch (error) {
    console.warn('[formatRelativeDate] Error formatting date:', error, { dateString, locale })
    return null
  }
}
