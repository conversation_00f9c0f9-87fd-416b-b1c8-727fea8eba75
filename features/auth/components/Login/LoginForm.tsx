import { Button } from '@/components/ui/Button/Button'
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/FormField/FormField'
import { PasswordInput } from '@/components/ui/PasswordInput/PasswordInput'
import { Text } from '@/components/ui/Text/Text'
import { TextInput } from '@/components/ui/TextInput/TextInput'
import { useAuthentication } from '@/hooks/auth/useAuthentication'
import { useAppLanguage } from '@/hooks/common/useAppLanguage'
import { useLoadingScreen } from '@/hooks/common/useLoadingScreen'
import { APP_ROUTES } from '@/routes/appRoutes'
import { zodResolver } from '@hookform/resolvers/zod'
import { Link, LinkProps, router, useLocalSearchParams } from 'expo-router'
import React, { useEffect } from 'react'
import { useForm } from 'react-hook-form'
import { useTranslation } from 'react-i18next'
import { TouchableOpacity, View } from 'react-native'
import Toast from 'react-native-toast-message'
import { z } from 'zod'
export const LoginForm = () => {
  const { redirect, forceBack } = useLocalSearchParams()

  const { primaryLanguage } = useAppLanguage()
  const { t } = useTranslation()
  const { login } = useAuthentication()

  const { showLoading, hideLoading } = useLoadingScreen()
  const formSchema = z.object({
    email: z.email({ message: t('MES-225') }).min(1, { message: t('MES-523') }),

    password: z.string().min(1, { message: t('MES-523') }),
  })

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      email: '',
      password: '',
    },
    mode: 'onTouched',
  })

  const handleLogin = async (values: z.infer<typeof formSchema>) => {
    // Simulate authentication success
    const { email, password } = values || {}

    showLoading()
    try {
      await login(email, password, primaryLanguage)
      if (forceBack) {
        router.back()
      } else {
        // Navigate first
        router.replace({
          pathname: redirect || APP_ROUTES.HOME.path,
        } as LinkProps['href'])
      }

      setTimeout(() => {
        Toast.show({
          type: 'success',
          text1: t('MES-187'),
          text2: t('MES-159') + '👋',
        })
      }, 300)
    } catch (error: any) {
      Toast.show({
        type: 'error',
        text1: t('MES-241'),
        text2: t(error?.message),
        autoHide: true,
      })
      // console.log(error)
    } finally {
      hideLoading()
    }
  }

  useEffect(() => {
    //  Re-trigger to update error message when language changed
    if (!form.formState.isValid && (form.formState.isDirty || form.formState.isSubmitted)) {
      form.trigger()
    }
  }, [primaryLanguage])

  return (
    <Form {...form}>
      <View className="flex w-full flex-col gap-y-4">
        <FormField
          control={form.control}
          name="email"
          render={({ field }) => (
            <FormItem className="w-full">
              <View className="flex w-full flex-col gap-y-1.5">
                <FormLabel required>Email</FormLabel>
                <FormControl>
                  <View className="relative w-full">
                    <TextInput
                      placeholder={t('MES-120')}
                      autoCapitalize="none"
                      keyboardType="email-address"
                      onChangeText={field.onChange}
                      onBlur={field.onBlur}
                      value={field.value}

                      // isError={!!form.formState.errors.password}
                    />
                  </View>
                </FormControl>
                <FormMessage />
              </View>
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="password"
          render={({ field }) => (
            <FormItem className="w-full">
              <View className="flex w-full flex-col gap-y-1.5">
                <FormLabel required>{t('MES-03')}</FormLabel>
                <FormControl>
                  <View className="relative w-full">
                    <PasswordInput
                      className="flex-1"
                      placeholder={t('MES-645')}
                      onChangeText={field.onChange}
                      onBlur={field.onBlur}
                      value={field.value}
                    />
                  </View>
                </FormControl>
                <FormMessage />
              </View>
            </FormItem>
          )}
        />
        <Link href={APP_ROUTES.FORGOT_PASSWORD.path as LinkProps['href']} asChild>
          <TouchableOpacity className=" ml-auto flex justify-end self-start">
            <Text className="py-1 text-right text-primary-500" size="body6" variant="primary">
              {t('MES-05')}
            </Text>
          </TouchableOpacity>
        </Link>

        <Button
          className={`h-14 w-full flex-row items-center justify-center gap-2 rounded-lg px-6 py-4 `}
          onPress={form.handleSubmit(handleLogin)}
        >
          <Text size="body6" variant={'white'}>
            {t('MES-06')}
          </Text>
        </Button>
      </View>
    </Form>
  )
}
