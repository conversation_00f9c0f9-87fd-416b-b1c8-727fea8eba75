import i18n from '@/configs/i18n'
import { LOCALE_FALLBACK_MAP } from '@/constants/locale.constant'
import {
  INITIAL_LANG,
  PRIMARY_LANG,
  SECONDARY_LANG,
  SHOW_FURIGANA,
} from '@/constants/storage-key.constant'
import { LocaleEnum } from '@/enums/locale.enum'
import { asyncStorageService } from '@/services/async-storage/async-storage.service'
import * as Localization from 'expo-localization'
import React, { createContext, ReactNode, useEffect, useState } from 'react'

interface LanguageContextType {
  // Current state
  currentLanguage: string
  systemLanguage: string
  savedLanguage: string
  primaryLanguage: string
  secondaryLanguage: string
  isLoading: boolean
  showFurigana: boolean

  // Actions
  changeLanguage: (langCode: string) => Promise<void>
  setPrimaryAndSecondary: (primaryLang: string) => Promise<void>
  setAsPrimary: (langCode: string) => Promise<void>
  swapPrimarySecondary: () => Promise<void>
  resetToSystemLanguage: () => Promise<void>
  toggleFurigana: (value?: boolean) => Promise<void>

  // Utilities
  getSupportedLanguages: () => string[]
  isLanguageSupported: (langCode: string) => boolean
  getLanguageDisplayName: (langCode: string) => string
  getLanguageFlag: (langCode: string) => string
  getDeviceLocales: () => any[]
  isDifferentFromSystem: () => boolean
  isPrimaryLanguage: (langCode: string) => boolean
  isSecondaryLanguage: (langCode: string) => boolean
  // getSecondaryLanguageFor: (primaryLang: string) => string // removed as fallback logic is now centralized
}

export const LanguageContext = createContext<LanguageContextType | undefined>(undefined)

interface LanguageProviderProps {
  children: ReactNode
}

export const LanguageProvider: React.FC<LanguageProviderProps> = ({ children }) => {
  const [systemLanguage, setSystemLanguage] = useState<string>('')
  const [savedLanguage, setSavedLanguage] = useState<string>('')
  const [primaryLanguage, setPrimaryLanguage] = useState<string>(LocaleEnum.VI)
  const [secondaryLanguage, setSecondaryLanguage] = useState<string>(LocaleEnum.JA)
  const [showFurigana, setShowFurigana] = useState<boolean>(false)
  const [isLoading, setIsLoading] = useState(true)

  // Get current active language
  const currentLanguage = i18n.language

  // Initialize language detection once
  useEffect(() => {
    const initializeLanguage = async () => {
      try {
        // Get system language
        const locales = Localization.getLocales()
        const systemLang = locales[0]?.languageCode || LocaleEnum.VI
        await asyncStorageService.setItem(INITIAL_LANG, systemLang)
        setSystemLanguage(systemLang)

        // Get saved language from AsyncStorage
        const saved = await asyncStorageService.getItem(PRIMARY_LANG)
        setSavedLanguage(saved || '')

        // Get saved primary/secondary languages
        const savedPrimary = await asyncStorageService.getItem(PRIMARY_LANG)
        const savedSecondary = await asyncStorageService.getItem(SECONDARY_LANG)

        // Get furigana setting
        const furiganaSetting = await asyncStorageService.getItem(SHOW_FURIGANA)
        setShowFurigana(furiganaSetting === 'true')

        if (savedPrimary) {
          setPrimaryLanguage(savedPrimary)
        }
        if (savedSecondary) {
          setSecondaryLanguage(savedSecondary)
        }

        setIsLoading(false)
      } catch (error) {
        console.warn('Error initializing language:', error)
        setIsLoading(false)
      }
    }

    initializeLanguage()
  }, [])

  // Change language function
  const changeLanguage = async (langCode: string) => {
    try {
      await asyncStorageService.setItem(PRIMARY_LANG, langCode)
      await i18n.changeLanguage(langCode)
      setSavedLanguage(langCode)
      setPrimaryLanguage(langCode) // Add this line to update primary language state
      await autoUpdateSecondaryLanguage(langCode) // Update secondary if needed
    } catch (error) {
      console.error('Failed to change language:', error)
    }
  }

  // Toggle furigana display
  const toggleFurigana = async (value?: boolean) => {
    try {
      const newValue = value !== undefined ? value : !showFurigana
      await asyncStorageService.setItem(SHOW_FURIGANA, newValue.toString())
      setShowFurigana(newValue)
    } catch (error) {
      console.error('Failed to toggle furigana:', error)
    }
  }

  // Auto-update secondary language if it matches the new primary
  const autoUpdateSecondaryLanguage = async (newPrimary: string) => {
    if (secondaryLanguage === newPrimary) {
      const newSecondary = LOCALE_FALLBACK_MAP[newPrimary as keyof typeof LOCALE_FALLBACK_MAP]
      await asyncStorageService.setItem(SECONDARY_LANG, newSecondary)
      setSecondaryLanguage(newSecondary)
    }
  }

  // Set primary language and automatically set secondary
  const setPrimaryAndSecondary = async (primaryLang: string) => {
    try {
      // Determine secondary language based on primary
      const secondaryLang = LOCALE_FALLBACK_MAP[primaryLang as keyof typeof LOCALE_FALLBACK_MAP]

      // Save to AsyncStorage
      await asyncStorageService.setItem(PRIMARY_LANG, primaryLang)
      await asyncStorageService.setItem(SECONDARY_LANG, secondaryLang)

      // Update state
      setPrimaryLanguage(primaryLang)
      setSecondaryLanguage(secondaryLang)

      // Also change the current active language to primary
      await changeLanguage(primaryLang)

      // Ensure secondary is not the same as primary
      await autoUpdateSecondaryLanguage(primaryLang)
    } catch (error) {
      console.error('Failed to set primary/secondary languages:', error)
    }
  }

  // Swap primary and secondary languages
  const swapPrimarySecondary = async () => {
    await setPrimaryAndSecondary(secondaryLanguage)
  }

  // Set language as primary (convenience method)
  const setAsPrimary = async (langCode: string) => {
    await setPrimaryAndSecondary(langCode)
  }

  // Check if a language is currently primary
  const isPrimaryLanguage = (langCode: string) => {
    return primaryLanguage === langCode
  }

  // Check if a language is currently secondary
  const isSecondaryLanguage = (langCode: string) => {
    return secondaryLanguage === langCode
  }

  // Get supported languages
  const getSupportedLanguages = () => {
    return Object.keys(i18n.services.resourceStore.data || {})
  }

  // Check if language is supported
  const isLanguageSupported = (langCode: string) => {
    return getSupportedLanguages().includes(langCode)
  }

  // Get language display name
  const getLanguageDisplayName = (langCode: string) => {
    const languageNames: { [key: string]: string } = {
      [LocaleEnum.VI]: 'Tiếng Việt',
      [LocaleEnum.JA]: '日本語',
    }
    return languageNames[langCode] || langCode
  }

  // Get language flag emoji
  const getLanguageFlag = (langCode: string) => {
    const languageFlags: { [key: string]: string } = {
      [LocaleEnum.VI]: '🇻🇳',
      [LocaleEnum.JA]: '🇯🇵',
    }
    return languageFlags[langCode] || '🌐'
  }

  // Get all device locales
  const getDeviceLocales = () => {
    return Localization.getLocales()
  }

  // Check if current language is different from system language
  const isDifferentFromSystem = () => {
    return currentLanguage !== systemLanguage
  }

  // Reset to system language
  const resetToSystemLanguage = async () => {
    if (systemLanguage) {
      await changeLanguage(systemLanguage)
    }
  }

  const value: LanguageContextType = {
    // Current state
    currentLanguage,
    systemLanguage,
    savedLanguage,
    primaryLanguage,
    secondaryLanguage,
    isLoading,
    showFurigana,

    // Actions
    changeLanguage,
    setPrimaryAndSecondary,
    setAsPrimary,
    swapPrimarySecondary,
    resetToSystemLanguage,
    toggleFurigana,

    // Utilities
    getSupportedLanguages,
    isLanguageSupported,
    getLanguageDisplayName,
    getLanguageFlag,
    getDeviceLocales,
    isDifferentFromSystem,
    isPrimaryLanguage,
    isSecondaryLanguage,
    // getSecondaryLanguageFor, // removed as fallback logic is now centralized
  }

  return <LanguageContext.Provider value={value}>{children}</LanguageContext.Provider>
}
