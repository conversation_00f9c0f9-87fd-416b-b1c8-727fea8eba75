'use client'

import { useDialog } from '@/hooks/dialog/useDialog'
import { useCallback, useEffect } from 'react'
import { Linking, Platform, TouchableOpacity, View } from 'react-native'

import BgUpdateVersion from '@/assets/notification/bg-update-version.svg'
import NotificationBing from '@/assets/notification/notification-bing.svg'
import { Text } from '@/components/ui/Text/Text'
import { PLATFORM_TYPE } from '@/enums/version.enum'
import { useGetNewestVersion } from '@/hooks/query/version/useGetNewestVersion'
import Constants from 'expo-constants'
import { useTranslation } from 'react-i18next'

/**
 * Compare two semantic version strings (e.g., "1.0.0", "1.2.3")
 * Returns true if version1 < version2
 */
const isVersionLessThan = (version1: string, version2: string): boolean => {
  const v1Parts = version1.split('.').map((v) => parseInt(v, 10))
  const v2Parts = version2.split('.').map((v) => parseInt(v, 10))

  for (let i = 0; i < Math.max(v1Parts.length, v2Parts.length); i++) {
    const v1 = v1Parts[i] || 0
    const v2 = v2Parts[i] || 0

    if (v1 < v2) return true
    if (v1 > v2) return false
  }

  return false
}

export const UpdateVersionPopup = () => {
  const { openDialog } = useDialog()

  const { newestVersion } = useGetNewestVersion({
    options: {
      params: {
        type: PLATFORM_TYPE.APP,
      },
    },
    useQueryOptions: {
      staleTime: Infinity,
    },
  })

  const handleOpenUpdateVersionPopup = useCallback(
    (ableToClose: boolean = true) => {
      openDialog({
        children: () => <UpdateVersionDialog />,
        variant: 'blank',
        contentSize: 'md',
        bodyClassName: 'p-0 m-0 ',
        wrapperClassName: 'p-0 !w-[336px]',
        modal: ableToClose,
      })
    },
    [openDialog],
  )

  useEffect(() => {
    const currentVersion = Constants.config?.version || Constants.expoConfig?.version

    if (newestVersion && currentVersion) {
      const { forceUpdate, minVersion } = newestVersion

      // Skip if current version is greater than or equal to newest version
      if (
        currentVersion === newestVersion.version ||
        isVersionLessThan(newestVersion.version, currentVersion)
      ) {
        return
      }

      // Check if forceUpdate is true (highest priority)
      if (forceUpdate) {
        handleOpenUpdateVersionPopup(false)
        return
      }

      // Check if current version is less than minimum version
      if (minVersion && isVersionLessThan(currentVersion, minVersion)) {
        handleOpenUpdateVersionPopup(false)
        return
      }
    }

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [newestVersion])
  return null
}

const UpdateVersionDialog = () => {
  const handleUpdateApp = () => {
    const url =
      Platform.OS === 'ios'
        ? 'https://apps.apple.com/vn/app/hico/id1612218450'
        : 'https://play.google.com/store/apps/details?id=com.wap.hico.customer'
    Linking.openURL(url)
  }

  const { t } = useTranslation()

  return (
    <View className="flex w-full  flex-col items-center justify-center overflow-hidden rounded-xl bg-white pb-6">
      <BgUpdateVersion width={380} />
      <View className="h-[30px] w-[30px]">
        <NotificationBing className="h-full w-full object-cover" />
      </View>

      <View className="flex flex-col items-center justify-center gap-6 px-4 py-3">
        <View className="flex flex-col items-center justify-center gap-3">
          <Text size="body3" variant="primary">
            {t('MES-823')}
          </Text>
          <Text size="body7" variant="default" className=" text-center">
            {t('MES-824')}
          </Text>
        </View>

        <TouchableOpacity
          onPress={handleUpdateApp}
          className=" flex items-center justify-center rounded-xl bg-primary-500 px-6 py-3"
        >
          <Text size="button3" variant="white">
            {t('MES-825')}
          </Text>
        </TouchableOpacity>
      </View>
    </View>
  )
}
