import SendIconPrimary from '@/assets/icons/send-icon-primary.svg'
import { Text } from '@/components/ui/Text/Text'
import {
  TextInputWithClear,
  TextInputWithClearRef,
} from '@/components/ui/TextInput/TextInputWithClear'
import { forwardRef, useCallback, useEffect, useImperativeHandle, useRef, useState } from 'react'
import { useTranslation } from 'react-i18next'
import { Keyboard, LayoutChangeEvent, TouchableOpacity, View } from 'react-native'
import { KeyboardStickyView } from 'react-native-keyboard-controller'
import { useSafeAreaInsets } from 'react-native-safe-area-context'
import { BaseCommentInputRef } from '.'

export interface CommentInputProps {
  onSubmit: (content: string) => void
  onCancel: () => void
  isLoading?: boolean
  placeholder?: string
  replyingTo?: string
  instanceId?: string
  onContentChange?: (content: string) => void
  onCancelReply?: () => void
  onFocus?: () => void
  onBlur?: () => void
  onLayout?: (event: LayoutChangeEvent) => void
}

export interface CommentInputRef extends BaseCommentInputRef {}

export const CommentInput = forwardRef<CommentInputRef, CommentInputProps>(
  (
    {
      onSubmit,
      onCancel,
      isLoading = false,
      placeholder,
      replyingTo,
      instanceId,
      onContentChange,
      onCancelReply,
      onFocus,
      onBlur,
      onLayout,
    },
    ref,
  ) => {
    const { t } = useTranslation()
    const insets = useSafeAreaInsets()
    const [content, setContent] = useState('')
    const inputRef = useRef<TextInputWithClearRef>(null)
    const MAX_LENGTH = 512
    const isDisabled = isLoading || !content.trim() || content.trim().length > MAX_LENGTH

    // Reset when instance changes
    useEffect(() => {
      setContent('')
    }, [instanceId])

    // Cleanup
    useEffect(() => {
      return () => {
        try {
          inputRef.current?.blur()
          Keyboard.dismiss()
        } catch (error) {
          console.warn('[CommentInput] Cleanup error:', error)
        }
      }
    }, [])

    const handleSubmit = useCallback(() => {
      const trimmedContent = content.trim()
      if (trimmedContent) {
        onSubmit(trimmedContent)
        setContent('')
        onContentChange?.('')
        onBlur?.()
        Keyboard.dismiss()
      }
    }, [content, onSubmit, onContentChange, onBlur])

    const handleCancel = useCallback(() => {
      setContent('')
      onContentChange?.('')
      onBlur?.()
      onCancel()
      Keyboard.dismiss()
    }, [onCancel, onContentChange, onBlur])

    const handleInputFocus = useCallback(() => {
      onFocus?.()
    }, [onFocus])

    const handleInputBlur = useCallback(() => {
      onBlur?.()
    }, [onBlur])

    const handleContentChange = useCallback(
      (newContent: string) => {
        setContent(newContent)
        onContentChange?.(newContent)
      },
      [onContentChange],
    )

    const handleCancelReply = useCallback(() => {
      onCancelReply?.()
    }, [onCancelReply])

    // Imperative methods
    useImperativeHandle(
      ref,
      () => ({
        focus: () => {
          setTimeout(() => {
            inputRef.current?.focus()
          }, 100)
        },
        blur: () => {
          inputRef.current?.blur()
          Keyboard.dismiss()
        },
        clear: () => {
          setContent('')
          onContentChange?.('')
        },
      }),
      [onContentChange],
    )

    // Render input content
    const renderInputContent = () => (
      <View className="flex flex-col gap-y-2 bg-white">
        {replyingTo && (
          <View className="flex flex-row items-center justify-between">
            <View className=" flex flex-row">
              <Text size="body7" variant="subdued" numberOfLines={1}>
                {t('MES-849')}{' '}
              </Text>
              <View className="max-w-[80%] overflow-hidden text-ellipsis">
                <Text size="body7" variant="primary" className="font-bold" numberOfLines={1}>
                  @{replyingTo}
                </Text>
              </View>
            </View>
            <TouchableOpacity onPress={handleCancelReply} hitSlop={10} className="shrink-0">
              <Text size="body7" variant="subdued" className="text-red-500">
                {t('MES-45')}
              </Text>
            </TouchableOpacity>
          </View>
        )}
        <View className="flex flex-row items-center">
          <View className="flex-1">
            <TextInputWithClear
              ref={inputRef}
              placeholder={placeholder || t('MES-887')}
              value={content}
              onChangeText={handleContentChange}
              onClear={() => handleContentChange('')}
              multiline
              onSubmitEditing={handleSubmit}
              onFocus={handleInputFocus}
              onBlur={handleInputBlur}
              className="py-2"
              style={{ maxHeight: 120 }}
            />
          </View>

          <TouchableOpacity
            className="shrink-0 pl-2"
            hitSlop={10}
            onPress={handleSubmit}
            disabled={isDisabled}
            style={{
              opacity: isDisabled ? 0.5 : 1,
            }}
          >
            <SendIconPrimary />
          </TouchableOpacity>
        </View>
        <View className="mt-1 flex flex-row">
          <Text
            size="body8"
            variant={content.trim().length > MAX_LENGTH ? 'error' : 'subdued'}
          >{`${content.trim().length}/${MAX_LENGTH}`}</Text>
        </View>
      </View>
    )

    return (
      <KeyboardStickyView
        offset={{ closed: 0, opened: 0 }}
        style={{
          position: 'absolute',
          bottom: 0,
          left: 0,
          right: 0,
          zIndex: 1000,
        }}
        onLayout={(event) => {
          onLayout?.(event)
        }}
      >
        <View
          className="border-t border-custom-neutral-100 bg-white px-4 py-3"
          style={{
            paddingBottom: Math.max(insets.bottom, 8),
          }}
        >
          {renderInputContent()}
        </View>
      </KeyboardStickyView>
    )
  },
)

CommentInput.displayName = 'CommentInput'
