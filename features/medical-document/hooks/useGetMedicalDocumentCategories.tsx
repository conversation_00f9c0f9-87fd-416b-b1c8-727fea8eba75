import { PaginatedDocs } from '@/types/global.type'
import { Params } from '@/types/http.type'
import { UseQueryOptions, useQuery } from '@tanstack/react-query'
import { AxiosRequestConfig } from 'axios'
import { medicalDocumentService } from '../services/medical-document.service'
import { MedicalDocumentCategory } from '../types'
import { medicalDocumentsQueryKeys } from './queryKeys'

export const useGetMedicalDocumentCategories = ({
  params = {},
  options = {},
  useQueryOptions,
}: {
  params?: Params
  options?: AxiosRequestConfig
  useQueryOptions?: Omit<
    UseQueryOptions<PaginatedDocs<MedicalDocumentCategory> | null>,
    'queryKey' | 'queryFn'
  >
}) => {
  const {
    isError: isGetMedicalDocumentCategoriesError,
    isPending: isGetMedicalDocumentCategoriesLoading,
    data: medicalDocumentCategories,
    ...rest
  } = useQuery({
    queryKey: [medicalDocumentsQueryKeys['medical-document-categories'].base(), params],
    queryFn: async () =>
      medicalDocumentService.getMedicalDocumentCategories({
        params: params,
        ...options,
      }),
    ...useQueryOptions,
  })
  return {
    isGetMedicalDocumentCategoriesError,
    isGetMedicalDocumentCategoriesLoading,
    medicalDocumentCategories,
    ...rest,
  }
}
