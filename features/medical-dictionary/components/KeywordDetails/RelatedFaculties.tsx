import ArrowRightIcon from '@/assets/icons/arrow-right-icon.svg'
import HealthCareIcon from '@/assets/icons/health-care-icon.svg'
import { Skeleton } from '@/components/ui/Skeleton/Skeleton'
import { Text } from '@/components/ui/Text/Text'
import { BLURHASH_CODE } from '@/constants/global.constant'
import { useGetHomeFaculties } from '@/features/home/<USER>/query/home-faculty/useGetHomeFaculties'
import { useAppLanguage } from '@/hooks/common/useAppLanguage'
import { StyledExpoImage } from '@/libs/styled'
import { APP_ROUTES } from '@/routes/appRoutes'
import { Media } from '@/types/media.type'
import { Link, LinkProps } from 'expo-router'
import { useEffect } from 'react'
import { useTranslation } from 'react-i18next'
import { TouchableOpacity, View } from 'react-native'
interface RelatedFacultiesProps {
  id: string
  isRefreshing?: boolean
}
export const RelatedFaculties = ({ id, isRefreshing }: RelatedFacultiesProps) => {
  const { t } = useTranslation()
  const { primaryLanguage } = useAppLanguage()
  const { homeFaculties, isGetHomeFacultiesLoading, isFetching, refetch } = useGetHomeFaculties({
    params: {
      locale: primaryLanguage,
      limit: 5,
      sort: ['-hasQuestions', 'name'],
      depth: 3,
      where: {
        'questions.question.keywords': {
          in: [id],
        },
      },
    },
    useQueryOptions: {
      staleTime: 3 * 60 * 1000,
    },
  })

  useEffect(() => {
    if (isRefreshing) {
      refetch()
    }
  }, [isRefreshing])
  if (!homeFaculties?.docs?.length && !isGetHomeFacultiesLoading) return null
  return (
    <View className="flex flex-col gap-y-4 px-4">
      <Text size="body3" variant="default">
        {t('MES-775')}
      </Text>
      {isGetHomeFacultiesLoading || isFetching || isRefreshing ? (
        <RelatedFacultiesLoading />
      ) : (
        <View className="flex flex-col gap-y-2">
          {homeFaculties?.docs?.map((faculty, index) => {
            const { name, icon } = faculty
            const iconMedia = icon as Media

            const iconUrl = iconMedia?.url || iconMedia?.thumbnailURL || ''

            return (
              <Link
                href={
                  {
                    pathname: APP_ROUTES.MEDICAL_HANDBOOK_FACULTIES.path + '/[id]',
                    params: { id: faculty.id },
                  } as LinkProps['href']
                }
                key={faculty.id}
                asChild
              >
                <TouchableOpacity
                  key={faculty.id || index}
                  className="flex h-[50px] flex-row items-center gap-x-3 rounded-lg bg-custom-background-hover px-4 py-2"
                >
                  <View className="shrink-0">
                    {iconUrl ? (
                      <StyledExpoImage
                        source={iconUrl}
                        contentFit="cover"
                        transition={1000}
                        className="h-8 w-8"
                        placeholder={BLURHASH_CODE}
                      />
                    ) : (
                      <HealthCareIcon className="h-8 w-8" />
                    )}
                  </View>
                  <View className="flex-1">
                    <Text size="body6" numberOfLines={1} className="line-clamp-1">
                      {name}
                    </Text>
                  </View>
                  <ArrowRightIcon className="h-5 w-5" />
                </TouchableOpacity>
              </Link>
            )
          })}
        </View>
      )}
    </View>
  )
}

const RelatedFacultiesLoading = () => {
  return (
    <View className="flex flex-col gap-y-4 ">
      {new Array(4).fill(0).map((_, index) => (
        <Skeleton key={index} className="h-16 w-full" />
      ))}
    </View>
  )
}
