import { keywordService } from '@/services/handbook/keyword.service'
import { Params } from '@/types/http.type'
import { GetKeywordAudioResponse } from '@/types/keyword.type'
import { UseQueryOptions, useQuery } from '@tanstack/react-query'
import { AxiosRequestConfig } from 'axios'
import { keywordQueryKeys } from './queryKeys'

export const useGetKeywordAudio = ({
  id,
  params = {},
  options = {},
  useQueryOptions,
}: {
  id: string
  params?: Params
  options?: AxiosRequestConfig
  useQueryOptions?: Omit<UseQueryOptions<GetKeywordAudioResponse | null>, 'queryKey' | 'queryFn'>
}) => {
  const {
    isError: isGetKeywordAudioError,
    isPending: isGetKeywordAudioLoading,
    data: keywordAudio,
    ...rest
  } = useQuery({
    queryKey: [keywordQueryKeys['get-keyword-audio'].base(), id, params],
    queryFn: async () =>
      keywordService.getKeywordAudio(id, {
        params: params,
        ...options,
      }),
    ...useQueryOptions,
  })
  return {
    isGetKeywordAudioError,
    isGetKeywordAudioLoading,
    keywordAudio,
    ...rest,
  }
}
