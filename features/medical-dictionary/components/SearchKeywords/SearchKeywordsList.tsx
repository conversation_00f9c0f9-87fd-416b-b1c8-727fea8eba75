import CloseIconDanger from '@/assets/icons/close-icon-danger.svg'
import { BaseSelectedFilterBadge } from '@/components/Filter/BaseSelectedFilterBadge/BaseSelectedFilterBadge'
import { Text } from '@/components/ui/Text/Text'
import { useAuthentication } from '@/hooks/auth/useAuthentication'
import { keywordQueryKeys } from '@/hooks/query/keyword/queryKeys'
import { KeywordV2 } from '@/types/keyword.type'
import { useQueryClient } from '@tanstack/react-query'
import React, { useCallback, useEffect, useRef } from 'react'
import { useTranslation } from 'react-i18next'
import { TouchableOpacity, View } from 'react-native'
import { useShallow } from 'zustand/react/shallow'
import { MedicalDictionaryList } from '../../components/MedicalDictionaryList/MedicalDictionaryList'
import { useMedicalDictionaryList } from '../../hooks/common/useMedicalDictionaryList'
import { keywordSearchHistoryQueryKeys } from '../../hooks/query/queryKeys'
import { useUpdateKeywordSearchHistory } from '../../hooks/query/useUpdateKeywordSearchHistory'
import {
  MedicalDictionaryFilterItem,
  MedicalDictionaryFilterType,
  useMedicalDictionaryStore,
} from '../../stores/MedicalDictionaryStore'
export default function SearchKeywordsList() {
  const { t } = useTranslation()
  const { user, status } = useAuthentication()
  const { searchTextValue, filters, toggleFilter, clearAllFilters } = useMedicalDictionaryStore(
    useShallow((state) => ({
      searchTextValue: state.searchText,
      filters: state.filters,
      toggleFilter: state.toggleFilter,
      clearAllFilters: state.clearAllFilters,
    })),
  )
  const shouldInvalidateQueries = useRef(false)

  const handleInvalidateQueriesKeywordList = () => {
    queryClient.invalidateQueries({
      queryKey: keywordQueryKeys['dictionary-keywords-by-category'].base(),
      type: 'active',
      refetchType: 'active',
      exact: false,
    })
    queryClient.invalidateQueries({
      queryKey: keywordQueryKeys['dictionary-keywords'].base(),
      type: 'active',
      refetchType: 'active',
      exact: false,
    })
  }
  const queryClient = useQueryClient()
  const activeCategory = filters?.[MedicalDictionaryFilterType.KEYWORD_TYPE]?.map(
    (filter) => filter.id,
  )
  const {
    refreshing,
    data,
    isGetKeywordsError,
    handleRefresh,
    handleLoadMore,
    isGetKeywordsLoading,
    isFetchingNextPage,
    isRefetching,
    totalDocs,
  } = useMedicalDictionaryList({
    searchTextValue: searchTextValue || '',
    activeCategory,
    config: {
      staleTime: 0,
      gcTime: 0,
      enabled: true,
    },
    extendedKeys: [...keywordQueryKeys['searchListKeywords'].base()],
  })

  const { updateKeywordSearchHistoryMutation } = useUpdateKeywordSearchHistory()

  const handleInvalidateQueriesKeywordSearchHistory = useCallback(() => {
    queryClient.invalidateQueries({
      queryKey: [keywordSearchHistoryQueryKeys['keyword-search-history-by-user'].base()],
      type: 'active',
      refetchType: 'active',
      exact: false,
    })
  }, [])

  const handlePressKeyword = (keyword: KeywordV2) => {
    if (!user || status === 'loading' || status === 'unauthorized') return
    updateKeywordSearchHistoryMutation(
      {
        id: keyword.id,
        type: 'add',
      },
      {
        onSuccess: () => {
          handleInvalidateQueriesKeywordSearchHistory()
        },
      },
    )
  }

  const renderHeaderComponent = useCallback(() => {
    const appliedFilters = Object.entries(filters)
      .filter(([key, value]) => value && Array.isArray(value) && value.length > 0)
      .flatMap(([_, value]) => value as MedicalDictionaryFilterItem[])

    return (
      <View className="mb-3 gap-y-3">
        {/* Products header */}
        <View className="flex flex-row items-center justify-between">
          <Text size="body3" className="font-medium">
            {t('MES-71')} {`(${totalDocs || 0})`}
          </Text>
        </View>

        {/* Active Filters - Only show real applied filters */}
        {appliedFilters.length > 0 && (
          <View className="flex flex-col gap-y-2">
            <Text size="body7" variant="subdued">
              {t('MES-706')} ({appliedFilters.length}):
            </Text>
            <View className="flex flex-row flex-wrap gap-2">
              {appliedFilters.map((filter, index) => (
                <BaseSelectedFilterBadge
                  key={`${filter.id}-${index}`}
                  filterItem={filter}
                  onClearFilter={(filterItem) => {
                    toggleFilter(
                      filterItem.type as MedicalDictionaryFilterType,
                      filterItem as MedicalDictionaryFilterItem,
                    )
                  }}
                  labelKey={'label'}
                />
              ))}
              <TouchableOpacity
                onPress={() => clearAllFilters()}
                className="flex flex-row items-center gap-x-1"
              >
                <Text size="body8" variant="error">
                  {t('MES-709')}
                </Text>
                <View className="-ml-1">
                  <CloseIconDanger width={20} height={20} />
                </View>
              </TouchableOpacity>
            </View>
          </View>
        )}
      </View>
    )
  }, [
    t,
    filters,
    isGetKeywordsLoading,
    isFetchingNextPage,
    isRefetching,
    data,
    toggleFilter,
    clearAllFilters,
  ])

  useEffect(() => {
    return () => {
      if (shouldInvalidateQueries.current) {
        handleInvalidateQueriesKeywordList()
      }
    }
  }, [])
  return (
    <MedicalDictionaryList
      data={data}
      refreshing={refreshing}
      isGetKeywordsError={isGetKeywordsError}
      onRefresh={handleRefresh}
      onLoadMore={handleLoadMore}
      showArchiveButton={Boolean(user)}
      containerStyle="px-0"
      initialLoadingItem={10}
      onUpdateFavoriteKeywordSuccess={() => {
        shouldInvalidateQueries.current = true
      }}
      headerComponent={renderHeaderComponent}
      onPressKeyword={handlePressKeyword}
      customParams={{
        isFromSearchKeywordsScreen: 'true',
      }}
    />
  )
}
