import Checkbox from '@/components/ui/Checkbox/Checkbox'
import { Date<PERSON>icker<PERSON>ield } from '@/components/ui/DatePickerField/DatePickerField'
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/FormField/FormField'
import { CustomRadioIndicator, Radio, RadioGroup, RadioLabel } from '@/components/ui/Radio/Radio'
import { Text } from '@/components/ui/Text/Text'
import { TextInput } from '@/components/ui/TextInput/TextInput'

import { GenderEnum } from '@/enums/common.enum'
import { useAuthentication } from '@/hooks/auth/useAuthentication'
import { useAppLanguage } from '@/hooks/common/useAppLanguage'
import { useLoadingScreen } from '@/hooks/common/useLoadingScreen'
import { StyledExpoImage } from '@/libs/styled'
import { APP_ROUTES, AppRoutesEnum } from '@/routes/appRoutes'
import { zodResolver } from '@hookform/resolvers/zod'
import dayjs from 'dayjs'
import { LinkProps, useLocalSearchParams, useRouter } from 'expo-router'
import { useEffect, useMemo, useState } from 'react'
import { useForm } from 'react-hook-form'
import { useTranslation } from 'react-i18next'
import { TouchableOpacity, View } from 'react-native'
import { KeyboardAwareScrollView } from 'react-native-keyboard-controller'
import Toast from 'react-native-toast-message'
import z from 'zod'
import { useCreateHospitalExamination } from '../../hooks/query/useCreateHospitalExamination'
import { CreateHospitalExaminationPayload } from '../../types'

export const CreateHospitalExaminationForm = () => {
  const { examinationFormId, examinationFormName, facultyName, facultyId, hospitalId } =
    useLocalSearchParams()

  const { t } = useTranslation()
  const router = useRouter()
  const { user } = useAuthentication()
  const { primaryLanguage } = useAppLanguage()
  const [isSubmitting, setIsSubmitting] = useState(false)
  const { showLoading, hideLoading } = useLoadingScreen()
  const { createHospitalExaminationMutation } = useCreateHospitalExamination()
  const formSchema = z.object({
    fullName: z.string().min(1, { message: t('MES-523') }),
    gender: z.string().min(1, { message: t('MES-467') }),
    birthDate: z
      .string()
      .min(1, { message: t('MES-468') })
      .refine(
        (date) => {
          // Check if the date matches YYYY/MM/DD format
          const dateRegex = /^\d{4}\/\d{2}\/\d{2}$/
          return dateRegex.test(date)
        },
        { message: t('MES-666') },
      )
      .refine(
        (date) => {
          // Validate the date using dayjs with strict parsing
          const parsedDate = dayjs(date, 'YYYY/MM/DD', true)
          const isValid = parsedDate.isValid()

          return isValid
        },
        { message: t('MES-666') },
      )
      .refine(
        (date) => {
          const parsedDate = dayjs(date, 'YYYY/MM/DD', true)
          // Check if date is not in the future
          return parsedDate.isBefore(dayjs()) || parsedDate.isSame(dayjs(), 'day')
        },
        { message: t('MES-469') },
      ),
    address: z.string().min(1, { message: t('MES-859') }),
    hasInsurance: z.boolean(),
  })

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      fullName: user?.name || '',
      gender: (user?.gender as GenderEnum) || '',
      birthDate: '',
      address: '',
      hasInsurance: false,
    },
    mode: 'onTouched',
  })

  // Calculate age
  const birthDate = form.watch('birthDate')
  const age = useMemo(() => {
    if (!birthDate) return null

    const birthDateObj = dayjs(birthDate)
    if (!birthDateObj.isValid()) return null

    // Calculate age more precisely by considering month and day
    const today = dayjs()
    const birthYear = birthDateObj.year()
    const birthMonth = birthDateObj.month()
    const birthDay = birthDateObj.date()

    let calculatedAge = today.year() - birthYear

    // Adjust age if birthday hasn't occurred yet this year
    if (today.month() < birthMonth || (today.month() === birthMonth && today.date() < birthDay)) {
      calculatedAge--
    }

    return calculatedAge
  }, [birthDate])

  const handleCreate = async (values: z.infer<typeof formSchema>) => {
    if (!values) return

    if (!examinationFormId || !facultyId) return
    showLoading()
    setIsSubmitting(true)
    const { fullName, gender, birthDate, hasInsurance, address } = values
    const payload: CreateHospitalExaminationPayload = {
      patientName: fullName,
      patientGender: gender,
      patientDob: birthDate,
      hasInsurance,
      facultyId: facultyId as string,
      patientAge: Number(age || 0),
      examinationFormId: examinationFormId as string,
      patientAddress: address,
      hospitalId: hospitalId as string,
    }
    createHospitalExaminationMutation(payload, {
      onSuccess: ({ doc }) => {
        router.push({
          pathname:
            APP_ROUTES.HOSPITAL_PARTNERS?.children?.[AppRoutesEnum.HOSPITAL_EXAMINATION_FORM].path +
            '/[id]',
          params: {
            id: doc.id,
          },
        } as LinkProps['href'])
      },
      onError: (error) => {
        console.log('error', error)
        Toast.show({
          type: 'error',
          text1: t('MES-464'),
        })
      },
      onSettled: () => {
        hideLoading()
        setIsSubmitting(false)
      },
    })
  }

  useEffect(() => {
    // Handle null/undefined dob properly
    let birthDate = ''
    if (user?.dob) {
      const dobDate = dayjs(user.dob)
      if (dobDate.isValid()) {
        birthDate = dobDate.format('YYYY/MM/DD')
      } else {
        birthDate = ''
      }
    }

    form.reset({
      fullName: user?.name || '',
      gender: (user?.gender as GenderEnum) || '',
      birthDate,
      address: user?.address || '',
    })
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [user])
  return (
    <View className="flex flex-1 flex-col gap-y-4 bg-white ">
      <KeyboardAwareScrollView
        showsVerticalScrollIndicator={false}
        keyboardShouldPersistTaps="handled"
        bounces={false}
        bottomOffset={100}
      >
        {/* Form Info */}
        <View className="flex flex-col gap-y-4 px-4">
          <View className=" items-center justify-center">
            <StyledExpoImage
              source={require('@/assets/icons/examination-form-banner.png')}
              style={{
                width: 192,
                height: 160,
              }}
            />
          </View>

          <View>
            <Text size="body3" variant="default" className="text-center !text-custom-green-700">
              {facultyName}
            </Text>
            <Text
              size="heading7"
              variant="default"
              className="text-center !text-custom-neutral-500"
            >
              {examinationFormName}
            </Text>
          </View>

          <View>
            <Text size="body7" variant="default" className="text-center ">
              {t('MES-458')}
            </Text>
          </View>
        </View>

        {/* Main Form */}
        <Form {...form}>
          <View className="gap-4 p-4 pb-4">
            <FormField
              control={form.control}
              name="fullName"
              render={({ field }) => (
                <FormItem className="flex flex-col gap-1.5">
                  <FormLabel required>{t('MES-91')}</FormLabel>
                  <FormControl>
                    <TextInput
                      wrapperClassName="min-h-12"
                      placeholder={t('MES-648')}
                      onChangeText={field.onChange}
                      onBlur={field.onBlur}
                      value={field.value}
                      editable={!isSubmitting}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="gender"
              render={({ field }) => (
                <FormItem className="flex flex-col gap-1.5">
                  <FormLabel required>{t('MES-95')}</FormLabel>
                  <FormControl>
                    <RadioGroup
                      value={field.value}
                      onChange={field.onChange}
                      className="flex-row gap-4"
                      isDisabled={isSubmitting}
                    >
                      <Radio value={GenderEnum.MALE} isDisabled={isSubmitting}>
                        <CustomRadioIndicator isSelected={field.value === GenderEnum.MALE} />
                        <RadioLabel>{t('MES-92')}</RadioLabel>
                      </Radio>

                      <Radio value={GenderEnum.FEMALE} isDisabled={isSubmitting}>
                        <CustomRadioIndicator isSelected={field.value === GenderEnum.FEMALE} />
                        <RadioLabel>{t('MES-93')}</RadioLabel>
                      </Radio>
                    </RadioGroup>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="address"
              render={({ field }) => (
                <FormItem className="flex flex-col gap-1.5">
                  <FormLabel>{t('MES-97')}</FormLabel>
                  <FormControl>
                    <TextInput
                      placeholder={t('MES-652')}
                      onChangeText={field.onChange}
                      onBlur={field.onBlur}
                      value={field.value}
                      multiline
                      editable={!isSubmitting}
                      className="!py-3"
                      scrollEnabled={false}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="birthDate"
              render={({ field }) => (
                <FormItem className="flex">
                  <View className="mb-1">
                    <FormLabel required>{t('MES-96')}</FormLabel>
                  </View>
                  <FormControl>
                    <View className="flex flex-row gap-x-2">
                      <View className="flex-1">
                        <DatePickerField
                          value={field.value}
                          onDateChange={(date) => {
                            field.onChange(date)
                          }}
                          placeholder={'YYYY/MM/DD'}
                          locale={primaryLanguage}
                          disabled={isSubmitting}
                        />
                      </View>
                      <View className="mt-auto w-[80px] shrink-0 self-end">
                        <View className="flex h-12 flex-row items-center justify-between gap-x-1 overflow-hidden rounded-lg border border-custom-neutral-100 bg-custom-background-hover p-2">
                          <Text size="field2" variant="subdued">
                            {age !== null ? age : ''}
                          </Text>
                          <Text size="field3" variant="subdued">
                            {t('MES-463')}
                          </Text>
                        </View>
                      </View>
                    </View>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="hasInsurance"
              render={({ field }) => (
                <FormItem className="flex flex-col gap-1.5">
                  <FormControl>
                    <Checkbox
                      checked={field.value}
                      onCheckedChange={field.onChange}
                      disabled={isSubmitting}
                    >
                      <Text size="field2"> {t('MES-495')}</Text>
                    </Checkbox>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </View>
        </Form>
      </KeyboardAwareScrollView>

      <View
        className="mt-auto px-4 pt-2"
        style={{
          boxShadow: '0px -2px 4px 0px #a8a8a825',
        }}
      >
        <TouchableOpacity
          className="flex h-12 items-center justify-center rounded-lg bg-primary px-6 py-3"
          onPress={form.handleSubmit(handleCreate)}
        >
          <Text size="button3" variant="white">
            {t('MES-169')}
          </Text>
        </TouchableOpacity>
      </View>
    </View>
  )
}
