import * as SecureStore from 'expo-secure-store'

class SecureStoreService {
  private static instance: SecureStoreService

  private constructor() {}

  public static getInstance(): SecureStoreService {
    if (!SecureStoreService.instance) {
      SecureStoreService.instance = new SecureStoreService()
    }
    return SecureStoreService.instance
  }

  async setItem(key: string, value: string): Promise<void> {
    try {
      await SecureStore.setItemAsync(key, value)
    } catch (error) {
      console.error(`Error setting secure item ${key}:`, error)
      throw error
    }
  }

  async getItem(key: string): Promise<string | null> {
    try {
      return await SecureStore.getItemAsync(key)
    } catch (error) {
      console.error(`Error getting secure item ${key}:`, error)
      return null
    }
  }

  async removeItem(key: string): Promise<void> {
    try {
      await SecureStore.deleteItemAsync(key)
    } catch (error) {
      console.error(`Error removing secure item ${key}:`, error)
      throw error
    }
  }
}

export const secureStoreService = SecureStoreService.getInstance()
export { SecureStoreService }
