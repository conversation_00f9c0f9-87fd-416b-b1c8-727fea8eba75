import { useLocalSearchParams } from 'expo-router'
import React from 'react'

import { BaseWebView } from '@/components/BaseWebView/BaseWebView'
import { StackScreenBase } from '@/components/StackBase/StackScreenBase'
import { WEBVIEW_APP_ROUTES } from '@/routes/webviewRoutes'
import { useTranslation } from 'react-i18next'

export default function SearchMedicineScreen() {
  const { t } = useTranslation()
  const { keyword } = useLocalSearchParams<{ keyword: string }>()

  const basePath = WEBVIEW_APP_ROUTES.SEARCH_SUMMARY.path || ''
  const uri = `${basePath}${keyword ? `?${keyword}` : ''}`

  return (
    <>
      <StackScreenBase
        options={{
          headerTitle: t('MES-66'),
        }}
      />
      <BaseWebView source={{ uri }} />
    </>
  )
}
