import { productService } from '@/features/product/services/product.service'
import { ProductCategory } from '@/features/product/types'
import { PaginatedDocs } from '@/types/global.type'
import { Params } from '@/types/http.type'
import { UseQueryOptions, useQuery } from '@tanstack/react-query'
import { AxiosRequestConfig } from 'axios'
import { productQueryKeys } from './queryKeys'

export const useGetProductCategories = ({
  params = {},
  options = {},
  useQueryOptions,
}: {
  params?: Params
  options?: AxiosRequestConfig
  useQueryOptions?: Omit<
    UseQueryOptions<PaginatedDocs<ProductCategory> | null>,
    'queryKey' | 'queryFn'
  >
} = {}) => {
  const {
    isError: isGetProductCategoriesError,
    isPending: isGetProductCategoriesLoading,
    data: productCategories,
    ...rest
  } = useQuery({
    queryKey: [productQueryKeys['productCategories'].base(), params],
    queryFn: async () =>
      productService.getProductCategories({
        params: params,
        options: options,
      }),
    ...useQueryOptions,
  })
  return {
    isGetProductCategoriesError,
    isGetProductCategoriesLoading,
    productCategories,
    ...rest,
  }
}
