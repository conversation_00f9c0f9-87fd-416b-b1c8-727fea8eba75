import * as FileSystem from 'expo-file-system'
import * as Sharing from 'expo-sharing'
import { useCallback, useState } from 'react'
import { Alert, Platform } from 'react-native'

interface SavePDFOptions {
  /**
   * Whether to show success alerts when the PDF is saved
   * @default true
   */
  showSuccessAlert?: boolean
}

/**
 * Hook for saving PDF files from base64 data
 */
export const useSavePDFBase64 = () => {
  const [isSaving, setIsSaving] = useState(false)

  /**
   * Sanitize filename to avoid special character issues
   */
  const sanitizeFilename = useCallback((name: string): string => {
    // Replace spaces and special characters with underscores
    return name.replace(/[^\w.-]/g, '_').replace(/_{2,}/g, '_') // Replace multiple underscores with a single one
  }, [])

  /**
   * Share a file using the device's share functionality
   */
  const shareFile = useCallback(async (fileUri: string, filename: string) => {
    try {
      // Double-check file exists and has content
      const fileInfo = await FileSystem.getInfoAsync(fileUri)

      if (!fileInfo.exists) {
        throw new Error('File does not exist')
      }

      if ((fileInfo as any).size === 0) {
        throw new Error('File is empty (0KB)')
      }

      if (await Sharing.isAvailableAsync()) {
        await Sharing.shareAsync(fileUri, {
          mimeType: 'application/pdf',
          dialogTitle: `Save ${filename}`,
          UTI: 'com.adobe.pdf', // iOS UTI
        })
      } else {
        console.error('Sharing is not available on this device')
        Alert.alert('Error', 'Sharing is not available on this device')
      }
    } catch (error) {
      console.error('Sharing error:', error)
      Alert.alert(
        'Error',
        `Failed to share the PDF: ${error instanceof Error ? error.message : 'Unknown error'}`,
      )
    }
  }, [])

  /**
   * Save a PDF from base64 data
   */
  const savePDF = useCallback(
    async ({
      pdf,
      filename,
      options,
      onSuccess,
      onError,
    }: {
      pdf: string
      filename: string
      options?: SavePDFOptions
      onSuccess?: () => void
      onError?: (error: string) => void
    }) => {
      const { showSuccessAlert = false } = options || {}

      // Validate PDF data
      if (!pdf) {
        console.error('PDF data is missing')
        Alert.alert('Error', 'PDF data is missing')
        onError?.('PDF data is missing')
        return { success: false, error: 'PDF data is missing' }
      }

      if (!filename) {
        console.error('Filename is missing')
        Alert.alert('Error', 'Filename is missing')
        onError?.('Filename is missing')
        return { success: false, error: 'Filename is missing' }
      }

      // Sanitize the filename to avoid special character issues
      const sanitizedFilename = sanitizeFilename(filename)

      // Check if the PDF data is valid base64
      if (pdf.length < 100) {
        console.error('PDF data is too short, likely invalid')
        Alert.alert('Error', 'Invalid PDF data (too short)')
        onError?.('Invalid PDF data (too short)')
        return { success: false, error: 'Invalid PDF data (too short)' }
      }

      // Make sure the base64 string doesn't contain the data URI prefix
      if (pdf.startsWith('data:application/pdf;base64,')) {
        pdf = pdf.replace('data:application/pdf;base64,', '')
      }

      if (isSaving) {
        onError?.('Another save operation is in progress')
        return { success: false, error: 'Another save operation is in progress' } // Prevent multiple simultaneous saves
      }
      setIsSaving(true)

      try {
        // First save to cache directory (temporary)
        const tempFileUri = `${FileSystem.cacheDirectory}${sanitizedFilename}`

        try {
          // Try to write the file
          await FileSystem.writeAsStringAsync(tempFileUri, pdf, {
            encoding: FileSystem.EncodingType.Base64,
          })
        } catch (writeError) {
          console.error('Error writing file:', writeError)
          throw new Error(
            `Failed to write file: ${writeError instanceof Error ? writeError.message : 'Unknown error'}`,
          )
        }

        // Verify the file was written correctly
        const fileInfo = await FileSystem.getInfoAsync(tempFileUri)

        if (!fileInfo.exists || (fileInfo as any).size === 0) {
          // Try fallback method for iOS
          if (Platform.OS === 'ios') {
            // Create a temporary file with text content first
            const tmpPath = `${FileSystem.cacheDirectory}temp_${Date.now()}.txt`
            await FileSystem.writeAsStringAsync(tmpPath, 'test')

            // Then overwrite it with the PDF content
            await FileSystem.writeAsStringAsync(tempFileUri, pdf, {
              encoding: FileSystem.EncodingType.Base64,
            })

            // Check again
            const retryFileInfo = await FileSystem.getInfoAsync(tempFileUri)
            if (!retryFileInfo.exists || (retryFileInfo as any).size === 0) {
              throw new Error('File still empty after retry')
            }
          } else {
            throw new Error('File was not saved correctly or is empty')
          }
        }

        if (Platform.OS === 'android') {
          // Android: Use Storage Access Framework
          try {
            const permissions =
              await FileSystem.StorageAccessFramework.requestDirectoryPermissionsAsync()

            if (permissions.granted) {
              const uri = await FileSystem.StorageAccessFramework.createFileAsync(
                permissions.directoryUri,
                sanitizedFilename,
                'application/pdf',
              )

              // Read the temp file as base64 and write it to the final destination
              const fileContent = await FileSystem.readAsStringAsync(tempFileUri, {
                encoding: FileSystem.EncodingType.Base64,
              })

              await FileSystem.writeAsStringAsync(uri, fileContent, {
                encoding: FileSystem.EncodingType.Base64,
              })

              if (showSuccessAlert) {
                Alert.alert('Success', `File "${filename}" has been saved successfully`)
              }
              onSuccess?.()
              return { success: true, uri }
            } else {
              // If directory permission was denied, use sharing as fallback
              await shareFile(tempFileUri, filename)
              onSuccess?.()
              return { success: true, shared: true }
            }
          } catch (error) {
            console.error('Android save error:', error)
            // Fallback to sharing if direct save fails
            await shareFile(tempFileUri, filename)
            onSuccess?.()
            return { success: true, shared: true }
          }
        } else {
          // iOS: Use sharing
          await shareFile(tempFileUri, filename)
          onSuccess?.()
          return { success: true, shared: true }
        }
      } catch (error) {
        console.error('Error saving PDF:', error)
        Alert.alert(
          'Error',
          `Failed to save the PDF: ${error instanceof Error ? error.message : 'Unknown error'}`,
        )
        onError?.(error instanceof Error ? error.message : 'Unknown error')
        return {
          success: false,
          error: error instanceof Error ? error.message : 'Unknown error',
        }
      } finally {
        setIsSaving(false)
      }
    },
    [isSaving, sanitizeFilename, shareFile],
  )

  return {
    savePDF,
    isSaving,
  }
}
