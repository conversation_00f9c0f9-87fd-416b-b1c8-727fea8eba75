import LightBulbIcon from '@/assets/icons/light-bulb-icon.svg'
import { useAppLanguage } from '@/hooks/common/useAppLanguage'

import { LinearGradient } from 'expo-linear-gradient'
import React from 'react'
import { useTranslation } from 'react-i18next'
import { StyleSheet, Text, View } from 'react-native'
import { useGetRandomFacts } from '../../hooks/query/fact/useGetRandomFacts'

export const DailyFact = () => {
  const { t } = useTranslation()
  const { primaryLanguage } = useAppLanguage()
  const { randomFacts, isGetRandomFactsLoading } = useGetRandomFacts({
    params: {
      locale: primaryLanguage,
      limit: 1,
    },
    useQueryOptions: {
      staleTime: 1000 * 60 * 5,
    },
  })

  if (!randomFacts?.docs?.[0] && !isGetRandomFactsLoading) return null
  return (
    <View className="mx-4">
      <LinearGradient
        colors={['#8C82FA', '#C180FF']}
        start={{ x: -0.056, y: 1.074 }}
        end={{ x: 1.092, y: -0.092 }}
        className="relative flex  p-4"
        style={{
          borderRadius: 8,
          padding: 0,
        }}
      >
        {/* Decorative circles */}
        <View style={styles.decorativeCircleLeft} />
        <View style={styles.decorativeCircleRight} />

        <View className="flex gap-3  p-4 ">
          <View className="flex-row items-center gap-2">
            <LightBulbIcon width={20} height={20} />
            <Text className="text-lg font-semibold text-white"> {t('MES-576')}</Text>
          </View>
          <Text className="text-base leading-5 text-white">{randomFacts?.docs?.[0]?.content}</Text>
        </View>
      </LinearGradient>
    </View>
  )
}

const styles = StyleSheet.create({
  decorativeCircleLeft: {
    position: 'absolute',
    width: 190.36,
    height: 190.36,
    left: -68.02,
    top: -90.15,
    borderWidth: 1,
    borderColor: 'rgba(207, 213, 242, 0.2)',
    borderRadius: 95.18,
    backgroundColor: 'transparent',
  },
  decorativeCircleRight: {
    position: 'absolute',
    width: 64,
    height: 64,
    right: 54,
    top: -54,
    backgroundColor: 'rgba(207, 213, 242, 0.1)',
    borderRadius: 32,
  },
})
