export const unifiedNotificationQueryKeys = {
  'unified-notification': {
    base: () => ['unified-notification'],
  },
  'user-push-notifications': {
    base: () => ['user-push-notifications'],
  },
  'user-unviewed-notification-count': {
    base: () => ['user-unviewed-notification-count'],
  },
}

export const unifiedNotificationMutationKeys = {
  'mark-notification-as-viewed': {
    base: () => ['mark-notification-as-viewed'],
  },
  'mark-all-notifications-as-viewed': {
    base: () => ['mark-all-notifications-as-viewed'],
  },
  'mark-all-chat-notifications-as-viewed': {
    base: () => ['mark-all-chat-notifications-as-viewed'],
  },
}
