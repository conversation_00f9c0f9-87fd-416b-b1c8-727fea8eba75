import { APP_ROUTES } from '@/routes/appRoutes'
import { LinkP<PERSON>, useRouter } from 'expo-router'
import { Platform } from 'react-native'

export const useChatMessageNotiAction = () => {
  const router = useRouter()

  const handleOpenChatMessageNoti = () => {
    const navigateToChat = () => {
      router.push({
        pathname: APP_ROUTES.CHAT_BOT?.children?.CHAT_WOOT?.path,
      } as LinkProps['href'])
    }

    if (Platform.OS === 'ios') {
      setTimeout(() => {
        navigateToChat()
      }, 1)
    } else {
      setImmediate(() => {
        navigateToChat()
      })
    }
  }

  return {
    handleOpenChatMessageNoti,
  }
}
