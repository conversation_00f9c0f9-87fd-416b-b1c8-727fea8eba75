import { useMutation } from '@tanstack/react-query'

import { keywordService } from '@/services/handbook/keyword.service'

import { KeywordTextToSpeechPayload } from '@/types/keyword.type'
import { useRef } from 'react'
import { keywordMutationKeys } from './queryKeys'

export const useKeywordTextToSpeech = () => {
  // Define an AbortController to cancel previous requests
  const abortControllerRef = useRef<AbortController | null>(null)

  const {
    isError: isKeywordTextToSpeechError,
    isPending: isKeywordTextToSpeechPending,
    mutate: keywordTextToSpeechMutation,
    ...rest
  } = useMutation({
    mutationKey: keywordMutationKeys['keyword-text-to-speech'].base(),
    mutationFn: (payload: KeywordTextToSpeechPayload) => {
      // Abort any ongoing request before initiating a new one
      if (abortControllerRef.current) {
        abortControllerRef.current.abort()
      }

      // Create a new AbortController for the new request
      const abortController = new AbortController()
      abortControllerRef.current = abortController

      // Pass the signal from AbortController to the service method
      return keywordService.keywordTextToSpeech({
        payload,
        options: {
          signal: abortControllerRef.current.signal,
        },
      })
    },
  })

  return {
    isKeywordTextToSpeechError,
    isKeywordTextToSpeechPending,
    keywordTextToSpeechMutation,
    ...rest,
  }
}
