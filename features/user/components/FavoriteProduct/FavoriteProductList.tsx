import CloseIconDanger from '@/assets/icons/close-icon-danger.svg'
import { Skeleton } from '@/components/ui/Skeleton/Skeleton'
import { Text } from '@/components/ui/Text/Text'

import EmptyBoxIcon from '@/assets/icons/empty-box.svg'
import { ProductV2TypeEnum } from '@/features/product/enums'
import { Product } from '@/features/product/types'
import { useAppLanguage } from '@/hooks/common/useAppLanguage'
import colors from '@/styles/_colors'
import React, { useCallback, useEffect, useMemo, useState } from 'react'
import { useTranslation } from 'react-i18next'
import { ActivityIndicator, FlatList, RefreshControl, TouchableOpacity, View } from 'react-native'
import { useShallow } from 'zustand/react/shallow'
import { useGetInfiniteFavoriteProducts } from '../../hooks/query/useGetInfiniteFavoriteProducts'
import {
  FavoriteProductFilterItem,
  FavoriteProductFilterType,
  useFavoriteProductStore,
} from '../../stores/FavoriteProductStore'
import { FavoriteProduct } from '../../types'
import { FavoriteProductItem } from './FavoriteProductItem'
import { SelectedFilterBadge } from './FilterBox/SelectedFilterBadge'

type ListItem =
  | { type: 'favorite_product_row'; favoriteProducts: FavoriteProduct[] }
  | { type: 'loading' }
  | { type: 'loading_skeleton' }

export const FavoriteProductList = () => {
  const { primaryLanguage } = useAppLanguage()
  const { t } = useTranslation()
  const [_, setHeaderHeight] = useState(0)
  const [refreshing, setRefreshing] = useState(false)
  const [localFavoriteProducts, setLocalFavoriteProducts] = useState<FavoriteProduct[]>([])

  const {
    searchTextValue,
    searchProductFilters,
    toggleFavoriteSearchProductFilter,
    clearAllFavoriteSearchFiltersMedicineType,
    clearAllFavoriteSearchProductFilters,
  } = useFavoriteProductStore(
    useShallow((state) => ({
      searchTextValue: state.searchProductFilters['search_text'],
      searchProductFilters: state.searchProductFilters,
      toggleFavoriteSearchProductFilter: state.toggleFavoriteSearchProductFilter,
      clearAllFavoriteSearchFiltersMedicineType: state.clearAllFavoriteSearchFiltersMedicineType,
      clearAllFavoriteSearchProductFilters: state.clearAllFavoriteSearchProductFilters,
    })),
  )

  const shouldApplyMedicineTypeFilter = useMemo(() => {
    const isFilterProductTypeContainMedicineType = searchProductFilters?.[
      FavoriteProductFilterType.PRODUCT_TYPE
    ]?.some((filter) => filter.id === ProductV2TypeEnum.MEDICINE)
    const isFilterProductTypeEmpty =
      !searchProductFilters?.[FavoriteProductFilterType.PRODUCT_TYPE] ||
      searchProductFilters?.[FavoriteProductFilterType.PRODUCT_TYPE]?.length === 0
    const isContainMedicineType = searchProductFilters?.[
      FavoriteProductFilterType.MEDICINE_TYPE
    ]?.some((filter) => filter.id === ProductV2TypeEnum.MEDICINE)
    return (
      isFilterProductTypeContainMedicineType || isFilterProductTypeEmpty || isContainMedicineType
    )
  }, [searchProductFilters])

  const {
    isGetFavoriteProductsError,
    isGetFavoriteProductsLoading,
    isGetFavoriteProductsFetching,
    isGetFavoriteProductsFetchingNextPage,
    favoriteProducts,
    fetchNextPage,
    hasNextPage,
    refetch,
  } = useGetInfiniteFavoriteProducts({
    config: {
      staleTime: 0,
      gcTime: 0,
    },
    params: {
      locale: primaryLanguage,
      limit: 12,
      depth: 8,
      categories:
        searchProductFilters[FavoriteProductFilterType.CATEGORY]?.map((filter) => filter.id) ||
        undefined,
      where: {
        and: [
          {
            or: [
              {
                'product.title': {
                  like: searchTextValue ? searchTextValue : undefined,
                },
              },
              {
                'product.keywords.name': {
                  like: searchTextValue ? searchTextValue : undefined,
                },
              },
              {
                'product.jaTitle': {
                  like: searchTextValue ? searchTextValue : undefined,
                },
              },
            ],
          },
          shouldApplyMedicineTypeFilter
            ? {
                or: [
                  // Medicine products with medicine type filter
                  {
                    and: [
                      {
                        'product.type': {
                          in: [ProductV2TypeEnum.MEDICINE],
                        },
                      },
                      {
                        'product.ageGroups': {
                          in:
                            searchProductFilters[FavoriteProductFilterType.AGE_GROUP]?.map(
                              (filter) => filter.id,
                            ) || undefined,
                        },
                      },
                      {
                        'product.medicineType': {
                          in:
                            searchProductFilters[FavoriteProductFilterType.MEDICINE_TYPE]?.map(
                              (filter) => filter.id,
                            ) || undefined,
                        },
                      },
                    ],
                  },
                  // Other product types without medicine type filter
                  {
                    and: [
                      {
                        'product.ageGroups': {
                          in:
                            searchProductFilters[FavoriteProductFilterType.AGE_GROUP]?.map(
                              (filter) => filter.id,
                            ) || undefined,
                        },
                      },
                      {
                        'product.type': {
                          in:
                            searchProductFilters[FavoriteProductFilterType.PRODUCT_TYPE] &&
                            searchProductFilters[FavoriteProductFilterType.PRODUCT_TYPE].length > 0
                              ? searchProductFilters[FavoriteProductFilterType.PRODUCT_TYPE]
                                  ?.filter((filter) => filter.id !== ProductV2TypeEnum.MEDICINE)
                                  ?.map((filter) => filter.id)
                              : [
                                  ProductV2TypeEnum.DIETARY_SUPPLEMENT,
                                  ProductV2TypeEnum.MEDICAL_INSTRUMENT,
                                ],
                        },
                      },
                    ],
                  },
                ],
              }
            : {
                and: [
                  {
                    'product.ageGroups': {
                      in:
                        searchProductFilters[FavoriteProductFilterType.AGE_GROUP]?.map(
                          (filter) => filter.id,
                        ) || undefined,
                    },
                  },
                  {
                    'product.type': {
                      in: searchProductFilters[FavoriteProductFilterType.PRODUCT_TYPE]
                        ? searchProductFilters[FavoriteProductFilterType.PRODUCT_TYPE]?.map(
                            (filter) => filter.id,
                          ) || undefined
                        : undefined,
                    },
                  },
                ],
              },
        ],
      },
    },
  })

  // Handle pull-to-refresh
  const handleRefresh = useCallback(async () => {
    setRefreshing(true)
    try {
      await refetch()
    } finally {
      setRefreshing(false)
    }
  }, [refetch])

  // Flatten all products from all pages
  const allFavoriteProducts = useMemo(() => {
    if (!favoriteProducts?.pages) return []
    return favoriteProducts.pages.flatMap((page) => page?.docs || [])
  }, [favoriteProducts])

  // Update local state when new data comes in
  useEffect(() => {
    setLocalFavoriteProducts(allFavoriteProducts)
  }, [allFavoriteProducts])

  // Handle removing item from local state after successful deletion
  const handleRemoveFromFavorites = useCallback((productId: string) => {
    setLocalFavoriteProducts((prev) => prev.filter((item) => item.id !== productId))
  }, [])

  // Handle adding item to local state after successful addition
  const handleAddToFavorites = useCallback((favoriteProduct: FavoriteProduct) => {
    setLocalFavoriteProducts((prev) => [...prev, favoriteProduct])
  }, [])

  const data = useMemo((): ListItem[] => {
    const items: ListItem[] = []

    // Show loading skeletons if initial loading or refreshing
    if (isGetFavoriteProductsLoading || isGetFavoriteProductsFetching || refreshing) {
      items.push({ type: 'loading_skeleton' })
    } else {
      // Products as individual items (1 per row) - use local state for instant updates
      localFavoriteProducts.forEach((favoriteProduct) => {
        items.push({ type: 'favorite_product_row', favoriteProducts: [favoriteProduct] })
      })

      // Loading indicator for pagination
      if (isGetFavoriteProductsFetchingNextPage) {
        items.push({ type: 'loading' })
      }
    }

    return items
  }, [
    localFavoriteProducts,
    isGetFavoriteProductsFetchingNextPage,
    isGetFavoriteProductsLoading,
    refreshing,
  ])

  const handleLoadMore = useCallback(() => {
    if (hasNextPage && !isGetFavoriteProductsFetchingNextPage) {
      fetchNextPage()
    }
  }, [hasNextPage, isGetFavoriteProductsFetchingNextPage, fetchNextPage])

  const handleHeaderLayout = useCallback((event: any) => {
    const { height } = event.nativeEvent.layout
    setHeaderHeight(height)
  }, [])

  const totalProducts = useMemo(() => {
    if (isGetFavoriteProductsLoading) {
      return 0
    }
    // Use local state for accurate count after deletions
    const total = localFavoriteProducts.length

    return total > 99 ? '99+' : total
  }, [localFavoriteProducts, isGetFavoriteProductsLoading])

  const renderItem = useCallback(
    ({ item }: { item: ListItem }) => {
      switch (item.type) {
        case 'favorite_product_row':
          return (
            <View className="mb-3 flex-1">
              <View className="flex h-full flex-col">
                {item.favoriteProducts.map((favoriteProduct) => (
                  <View key={favoriteProduct.id} className="w-full">
                    <FavoriteProductItem
                      product={favoriteProduct.product as Product}
                      favoriteProductId={favoriteProduct.id}
                      onRemoveFromFavorites={handleRemoveFromFavorites}
                      onAddToFavorites={handleAddToFavorites}
                    />
                  </View>
                ))}
              </View>
            </View>
          )

        case 'loading':
          return (
            <View className="items-center py-4">
              <ActivityIndicator size="small" />
            </View>
          )

        case 'loading_skeleton':
          return (
            <View className="flex flex-1 flex-col gap-3">
              {[...Array.from({ length: 3 }).fill(1)].map((_, index) => (
                <View
                  key={index}
                  className="w-full flex-row gap-x-3 overflow-hidden rounded-lg bg-custom-background-hover px-3 py-2"
                >
                  {/* Image skeleton */}
                  <View className="h-[80px] w-32 shrink-0 rounded-lg bg-gray-200" />

                  {/* Content skeleton */}
                  <View className="flex-1 flex-col gap-y-1">
                    <Skeleton className="h-4 w-3/4 rounded bg-gray-200" />
                    <Skeleton className="h-3 w-1/2 rounded bg-gray-200" />
                    <View className="mt-auto flex-row items-center justify-between">
                      <View className="flex-row items-center gap-2">
                        <Skeleton className="h-5 w-5 rounded bg-gray-200" />
                        <Skeleton className="h-5 w-5 rounded bg-gray-200" />
                        <Skeleton className="h-5 w-5 rounded bg-gray-200" />
                      </View>
                      <Skeleton className="h-5 w-5 rounded bg-gray-200" />
                    </View>
                  </View>
                </View>
              ))}
            </View>
          )

        default:
          return null
      }
    },
    [handleRemoveFromFavorites, handleAddToFavorites],
  )

  const renderHeaderComponent = useCallback(() => {
    const appliedFilters = Object.entries(searchProductFilters)
      .filter(
        ([key, value]) =>
          key !== FavoriteProductFilterType.SEARCH_TEXT &&
          value &&
          Array.isArray(value) &&
          value.length > 0,
      )
      .flatMap(([_, value]) => value as FavoriteProductFilterItem[])

    return (
      <View onLayout={handleHeaderLayout} className="mb-3 gap-y-3">
        {/* Products header */}
        <View className="flex flex-row items-center justify-between">
          <Text size="body3" className="font-medium">
            {t('MES-141')}{' '}
            {!isGetFavoriteProductsLoading &&
              !isGetFavoriteProductsFetchingNextPage &&
              !isGetFavoriteProductsFetching &&
              `(${totalProducts})`}
          </Text>
        </View>

        {/* Active Filters - Only show real applied filters */}
        {appliedFilters.length > 0 && (
          <View className="flex flex-col gap-y-2">
            <Text size="body7" variant="subdued">
              {t('MES-706')} ({appliedFilters.length}):
            </Text>
            <View className="flex flex-row flex-wrap gap-2">
              {appliedFilters.map((filter: FavoriteProductFilterItem) => (
                <SelectedFilterBadge
                  key={`${filter.type}-${filter.id}`}
                  filterItem={filter}
                  onClearFilter={(filterItem) => {
                    toggleFavoriteSearchProductFilter(
                      filterItem.type as FavoriteProductFilterType,
                      filterItem as FavoriteProductFilterItem,
                    )
                  }}
                />
              ))}
              <TouchableOpacity
                onPress={() => clearAllFavoriteSearchProductFilters()}
                className="flex flex-row items-center gap-x-1"
              >
                <Text size="body8" variant="error">
                  {t('MES-709')}
                </Text>
                <View className="-ml-1">
                  <CloseIconDanger width={20} height={20} />
                </View>
              </TouchableOpacity>
            </View>
          </View>
        )}
      </View>
    )
  }, [
    handleHeaderLayout,
    refreshing,
    t,
    totalProducts,
    isGetFavoriteProductsLoading,
    localFavoriteProducts,
    searchProductFilters,
    shouldApplyMedicineTypeFilter,
  ])

  const renderEmptyComponent = useCallback(() => {
    if (isGetFavoriteProductsError) {
      return (
        <View className="items-center  py-8">
          <Text size="body6" className="text-red-500">
            {t('MES-197')}
          </Text>
        </View>
      )
    }

    return (
      <View className="items-center  py-8">
        <EmptyBoxIcon />
        <Text size="body6" variant="default">
          {t('MES-717')}
        </Text>
      </View>
    )
  }, [isGetFavoriteProductsError])

  const keyExtractor = useCallback((item: ListItem, index: number) => {
    if (item.type === 'favorite_product_row') {
      return `favorite_product_row-${item.favoriteProducts.map((p) => p.id).join('-')}-${index}`
    }
    return `${item.type}-${index}`
  }, [])

  useEffect(() => {
    if (!shouldApplyMedicineTypeFilter) {
      clearAllFavoriteSearchFiltersMedicineType()
    }
  }, [shouldApplyMedicineTypeFilter])

  return (
    <FlatList
      className="flex-1"
      data={data}
      renderItem={renderItem}
      keyExtractor={keyExtractor}
      ListHeaderComponent={renderHeaderComponent}
      onEndReached={handleLoadMore}
      onEndReachedThreshold={0.1}
      ListEmptyComponent={renderEmptyComponent}
      showsVerticalScrollIndicator={false}
      removeClippedSubviews={false}
      maxToRenderPerBatch={10}
      windowSize={10}
      initialNumToRender={5}
      contentContainerStyle={{ paddingBottom: 20 }}
      refreshControl={
        <RefreshControl
          refreshing={refreshing}
          onRefresh={handleRefresh}
          colors={[colors.primary['500']]}
          tintColor={colors.primary['500']}
          progressBackgroundColor="#FFFFFF"
        />
      }
    />
  )
}
