import { BaseFilterFooterActions } from '@/components/Filter/BaseFilterFooterActions/BaseFilterFooterActions'
import { ProductFilterBadgeList } from '@/components/Filter/ProductFilterBadgeList/ProductFilterBadgeList'
import { BottomSheetFooter, BottomSheetFooterProps } from '@gorhom/bottom-sheet'
import * as Haptics from 'expo-haptics'
import { forwardRef, memo, useMemo } from 'react'
import { useTranslation } from 'react-i18next'
import { View } from 'react-native'
import { useShallow } from 'zustand/react/shallow'
import { ProductV2TypeEnum } from '../../enums'
import { ProductFilterItem, ProductFilterType, useProductStore } from '../../stores/ProductStore'

interface FilterFooterProps extends BottomSheetFooterProps {
  productType: string
  closeSheet?: () => void
}

export const FilterFooter = memo(
  forwardRef<View, FilterFooterProps>(({ productType, closeSheet, ...props }, ref) => {
    return (
      <BottomSheetFooter {...props}>
        <FitlerFooter ref={ref} productType={productType} closeSheet={closeSheet} {...props} />
      </BottomSheetFooter>
    )
  }),
)

interface FilterFooterProps extends BottomSheetFooterProps {
  productType: string
  closeSheet?: () => void
  onLayout?: () => void
}

const FitlerFooter = forwardRef<View, FilterFooterProps>(
  ({ productType, closeSheet, onLayout }, ref) => {
    const { t } = useTranslation()
    const { productTabState, applyTempFilters, resetTempFilters, toggleTempFilter } =
      useProductStore(
        useShallow((state) => ({
          productTabState: state.productTabState,
          removeFilter: state.removeFilter,
          applyTempFilters: state.applyTempFilters,
          resetTempFilters: state.resetTempFilters,
          toggleTempFilter: state.toggleTempFilter,
        })),
      )

    const handleClearFilter = (filterItem: any) => {
      if (
        filterItem.type === ProductFilterType.AGE_GROUP ||
        filterItem.type === ProductFilterType.MEDICINE_TYPE
      ) {
        const productFilterItem = filterItem as ProductFilterItem
        const currentTempFilters =
          productTabState[productType as ProductV2TypeEnum].tempFilters[productFilterItem.type] ||
          []
        const isSelected = currentTempFilters.some((f) => f.id === productFilterItem.id)

        if (isSelected) {
          toggleTempFilter(
            productType as ProductV2TypeEnum,
            productFilterItem.type,
            productFilterItem,
          )
        }
      }
    }

    const handleApplyFilters = () => {
      applyTempFilters(productType as ProductV2TypeEnum)
      closeSheet?.()
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light)
    }

    const handleResetFilters = () => {
      resetTempFilters(productType as ProductV2TypeEnum)
    }

    const activeFilters = useMemo(() => {
      return Object.values(productTabState[productType as ProductV2TypeEnum].tempFilters).flat()
    }, [productTabState, productType])

    return (
      <View
        ref={ref}
        className="relative flex flex-col gap-y-4 border-t border-gray-200 bg-white px-4 py-4 pb-10"
        onLayout={onLayout}
      >
        {/* Selected Filter Badges using ProductFilterBadgeList */}
        <ProductFilterBadgeList
          activeFilters={activeFilters}
          onClearFilter={handleClearFilter}
          maxDisplayCount={5}
        />

        {/* Footer Actions using BaseFilterFooterActions */}
        <BaseFilterFooterActions
          onApply={handleApplyFilters}
          onReset={handleResetFilters}
          applyText={t('MES-281')}
          resetText={t('MES-105')}
        />
      </View>
    )
  },
)
