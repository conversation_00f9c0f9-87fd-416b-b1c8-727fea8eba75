import { authService } from '@/services/auth/auth.service'
import { GoogleLoginResponse } from '@/types/auth.type'
import { useCallback, useEffect, useState } from 'react'
import { useLoadingScreen } from '../common/useLoadingScreen'
const isProd = process.env.EXPO_PUBLIC_APP_ENVIROMENT === 'production'

export const useGoogleSignIn = ({
  showLoadingScreen = true,
}: { showLoadingScreen?: boolean } = {}) => {
  const [isGoogleSigningIn, setGoogleSigningIn] = useState(false)
  const [isAppLoggingIn, setAppLoggingIn] = useState(false)
  const { showLoading, hideLoading } = useLoadingScreen()

  useEffect(() => {
    if (isAppLoggingIn && showLoadingScreen) {
      showLoading()
    } else {
      hideLoading()
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isAppLoggingIn, showLoadingScreen])

  const googleSignIn = useCallback(async (): Promise<GoogleLoginResponse | null> => {
    if (!isProd) {
      console.warn('Google Sign-In is disabled in development or Expo Go.')
      return null
    }

    setGoogleSigningIn(true)

    try {
      const {
        GoogleSignin,
        isSuccessResponse,

        // eslint-disable-next-line @typescript-eslint/no-require-imports
      } = require('@react-native-google-signin/google-signin')

      await GoogleSignin.hasPlayServices()
      await GoogleSignin.signOut()
      const response = await GoogleSignin.signIn()

      if (isSuccessResponse(response)) {
        const idToken = response?.data?.idToken

        if (idToken) {
          try {
            setAppLoggingIn(true)
            const res = await authService.googleLogin(idToken)

            return res || null
          } catch (error) {
            console.log('Google Login App error', error)
            return null
          } finally {
            setAppLoggingIn(false)
            setGoogleSigningIn(false)
          }
        }
      }
    } catch (error) {
      console.log('Google Sign-in error', error)

      // handle specific sign-in errors
      try {
        const {
          isErrorWithCode,
          statusCodes,
          // eslint-disable-next-line @typescript-eslint/no-require-imports
        } = require('@react-native-google-signin/google-signin')
        if (isErrorWithCode(error as any)) {
          switch ((error as any).code) {
            case statusCodes.IN_PROGRESS:
              console.log('Sign-in already in progress')
              break
            case statusCodes.PLAY_SERVICES_NOT_AVAILABLE:
              console.log('Play Services not available')
              break
            default:
              console.log('Unhandled Google Sign-in error:', error)
          }
        }
      } catch (_) {
        // skip error handling if module not available
      }
    } finally {
      setGoogleSigningIn(false)
      setAppLoggingIn(false)
    }

    return null
  }, [])

  return { googleSignIn, isGoogleSigningIn, isAppLoggingIn }
}
