import { Params } from '@/types/http.type'
import { InfiniteData, UseInfiniteQueryOptions, useInfiniteQuery } from '@tanstack/react-query'

import { PaginatedDocs } from '@/types/global.type'
import { AxiosRequestConfig } from 'axios'
import { hospitalPartnerService } from '../../services/hospital-partner.service'
import { Hospital } from '../../types'
import { hospitalPartnersQueryKeys } from './queryKeys'

export type HospitalPartnersQueryConfig = Omit<
  UseInfiniteQueryOptions<
    PaginatedDocs<Hospital>,
    Error,
    InfiniteData<PaginatedDocs<Hospital>>,
    (string | Params)[],
    number
  >,
  'queryFn' | 'queryKey' | 'getNextPageParam' | 'initialPageParam'
>

interface UseGetInfiniteHospitalPartnersProps {
  params?: Params
  options?: AxiosRequestConfig
  key?: string | number
  config?: HospitalPartnersQueryConfig
  overrideKey?: (string | Params)[]
}

export const useGetInfiniteHospitalPartners = ({
  params = {},
  options = {},
  config = {},
  overrideKey,
}: UseGetInfiniteHospitalPartnersProps = {}) => {
  const {
    isError: isGetHospitalPartnersError,
    isFetching: isGetHospitalPartnersLoading,
    data: hospitalPartners,
    fetchNextPage,
    hasNextPage,
    ...rest
  } = useInfiniteQuery({
    queryKey: overrideKey
      ? overrideKey
      : [hospitalPartnersQueryKeys['hospital-partners'].base(), params],
    queryFn: async ({ pageParam = 1 }) => {
      return hospitalPartnerService.getHospitalPartners({
        params: {
          ...params,
          page: pageParam,
        },
        options,
      })
    },
    getNextPageParam: (lastPage) => lastPage?.nextPage,
    getPreviousPageParam: (lastPage) => lastPage?.prevPage,
    initialPageParam: 1,
    ...config,
  })

  return {
    isGetHospitalPartnersError,
    isGetHospitalPartnersLoading,
    hospitalPartners,
    fetchNextPage,
    hasNextPage,
    ...rest,
  }
}
