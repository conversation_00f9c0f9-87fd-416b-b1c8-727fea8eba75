// utils/colors.ts
function hexToRgb(hex: string) {
  const parsed = hex.replace(/^#/, '')
  const bigint = parseInt(parsed, 16)

  const r = (bigint >> 16) & 255
  const g = (bigint >> 8) & 255
  const b = bigint & 255

  return [r, g, b]
}

function rgbToHex(r: number, g: number, b: number) {
  return (
    '#' +
    [r, g, b]
      .map((x) => {
        const hex = x.toString(16)
        return hex.length === 1 ? '0' + hex : hex
      })
      .join('')
  )
}

/**
 * Mix color with white to create a softer background.
 * @param hex Base color (e.g. #1157C8)
 * @param ratio 0 → original color, 1 → pure white
 */
export function generateBackgroundColor(hex: string, ratio = 0.9) {
  const [r, g, b] = hexToRgb(hex)

  const newR = Math.round(r + (255 - r) * ratio)
  const newG = Math.round(g + (255 - g) * ratio)
  const newB = Math.round(b + (255 - b) * ratio)

  return rgbToHex(newR, newG, newB)
}
