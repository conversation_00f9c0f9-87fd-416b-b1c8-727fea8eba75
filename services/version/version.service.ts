import { API_ENDPOINTS } from '@/constants/endpoint.constant'
import { Params } from '@/types/http.type'

import { PaginatedDocs } from '@/types/global.type'
import { Version } from '@/types/version.type'
import { AxiosRequestConfig } from 'axios'
import { httpService } from '../http/http.service'

// SERVER / CLIENT
class VersionService {
  private static instance: VersionService

  private constructor() {}

  public static getInstance(): VersionService {
    if (!VersionService.instance) {
      VersionService.instance = new VersionService()
    }
    return VersionService.instance
  }

  /**
   * Fetch all app versions using the HTTP service .
   *
   * @param params - Optional query parameters for filtering or paginating .
   * @param options - Optional fetch options such as headers, method, etc.
   * @returns A promise resolving to PaginatedDocs<medicine> or null in case of an error.
   */
  public async getAppVersions({
    params = {},
    options = {},
  }: {
    params?: Params
    options?: AxiosRequestConfig
  } = {}): Promise<PaginatedDocs<Version> | null> {
    const data = await httpService.get<PaginatedDocs<Version> | null>(
      `/${API_ENDPOINTS.versions_api}`,
      {
        params,
        ...options,
      },
    )
    return data
  }

  /**
   * Fetch  app version using the HTTP service .
   *
   * @param params - Optional query parameters for filtering or paginating .
   * @param options - Optional fetch options such as headers, method, etc.
   * @returns A promise resolving to PaginatedDocs<medicine> or null in case of an error.
   */
  public async getAppVersion({
    id,
    params = {},
    options = {},
  }: {
    id: string
    params?: Params
    options?: AxiosRequestConfig
  }): Promise<Version | null> {
    const data = await httpService.get<Version | null>(`/${API_ENDPOINTS.versions_api}/${id}`, {
      params,
      ...options,
    })
    return data
  }

  /**
   * Fetch  newest app version using the HTTP service .
   *
   * @param params - Optional query parameters for filtering or paginating .
   * @param options - Optional fetch options such as headers, method, etc.
   * @returns A promise resolving to PaginatedDocs<medicine> or null in case of an error.
   */
  public async getNewestAppVersion(options?: AxiosRequestConfig): Promise<Version | null> {
    const data = await httpService.get<Version | null>(`/${API_ENDPOINTS.versions_api}/newest`, {
      ...options,
    })
    return data
  }
}

export const versionService = VersionService.getInstance()
