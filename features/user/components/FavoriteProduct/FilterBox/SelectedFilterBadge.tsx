import CloseIcon from '@/assets/icons/close-icon.svg'
import { Text } from '@/components/ui/Text/Text'
import { FavoriteProductFilterItem } from '@/features/user/stores/FavoriteProductStore'
import { TouchableOpacity } from 'react-native'

interface SelectedFilterBadgeProps {
  filterItem: FavoriteProductFilterItem
  onClearFilter?: (item: FavoriteProductFilterItem) => void
}

export const SelectedFilterBadge = ({ filterItem, onClearFilter }: SelectedFilterBadgeProps) => {
  const getFilterLabel = (item: FavoriteProductFilterItem): string => {
    return item.label
  }

  return (
    <TouchableOpacity
      onPress={() => onClearFilter?.(filterItem)}
      className="flex flex-row items-center gap-x-1 self-start rounded border border-primary p-1"
    >
      <Text size="body7" variant="primary">
        {getFilterLabel(filterItem)}
      </Text>
      <CloseIcon width={18} height={18} />
    </TouchableOpacity>
  )
}
