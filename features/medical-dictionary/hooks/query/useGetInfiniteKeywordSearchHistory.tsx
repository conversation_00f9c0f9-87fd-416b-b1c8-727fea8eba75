import { Params } from '@/types/http.type'
import { InfiniteData, UseInfiniteQueryOptions, useInfiniteQuery } from '@tanstack/react-query'

import { PaginatedDocs } from '@/types/global.type'
import { AxiosRequestConfig } from 'axios'
import { keywordSearchHistoryService } from '../../services/keyword-search-history.service'
import { KeywordSearchHistory } from '../../types'
import { keywordSearchHistoryQueryKeys } from './queryKeys'

export type KeywordSearchHistoryQueryConfig = Omit<
  UseInfiniteQueryOptions<
    PaginatedDocs<KeywordSearchHistory>,
    Error,
    InfiniteData<PaginatedDocs<KeywordSearchHistory>>,
    (string | Params)[],
    number
  >,
  'queryFn' | 'queryKey' | 'getNextPageParam' | 'initialPageParam'
>

interface UseGetInfiniteKeywordSearchHistoryProps {
  params?: Params
  options?: AxiosRequestConfig
  key?: string | number
  config?: KeywordSearchHistoryQueryConfig
  overrideKey?: (string | Params)[]
}

export const useGetInfiniteKeywordSearchHistory = ({
  params = {},
  options = {},
  config = {},
  overrideKey,
}: UseGetInfiniteKeywordSearchHistoryProps = {}) => {
  const {
    isError: isGetKeywordSearchHistoryByUserError,
    isFetching: isGetKeywordSearchHistoryByUserLoading,
    data: keywordSearchHistoryByUser,
    fetchNextPage,
    hasNextPage,
    ...rest
  } = useInfiniteQuery({
    queryKey: overrideKey
      ? overrideKey
      : [keywordSearchHistoryQueryKeys['keyword-search-history-by-user'].base(), params],
    queryFn: async ({ pageParam = 1 }) => {
      return keywordSearchHistoryService.getKeywordSearchHistoryByUser({
        params: {
          ...params,
          page: pageParam,
        },
        options,
      })
    },
    getNextPageParam: (lastPage) => lastPage?.nextPage,
    getPreviousPageParam: (lastPage) => lastPage?.prevPage,
    initialPageParam: 1,
    ...config,
  })

  return {
    isGetKeywordSearchHistoryByUserError,
    isGetKeywordSearchHistoryByUserLoading,
    keywordSearchHistoryByUser,
    fetchNextPage,
    hasNextPage,
    ...rest,
  }
}
