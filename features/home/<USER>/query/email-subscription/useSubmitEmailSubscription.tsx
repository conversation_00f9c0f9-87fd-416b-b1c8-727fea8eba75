import { useMutation } from '@tanstack/react-query'

import { emailSubscriptionService } from '@/features/home/<USER>/email-subsription.service'
import { useRef } from 'react'
import { emailSubscriptionMutationKeys } from './queryKeys'

export const useSubmitEmailSubscription = () => {
  // Define an AbortController to cancel previous requests
  const abortControllerRef = useRef<AbortController | null>(null)

  const {
    isError: isSubmitEmailSubscriptionError,
    isPending: isSubmitEmailSubscriptionPending,
    mutate: submitEmailSubscriptionMutation,
    ...rest
  } = useMutation({
    mutationKey: emailSubscriptionMutationKeys['submit-email-subscription'].base(),
    mutationFn: (payload: string) => {
      // Abort any ongoing request before initiating a new one
      if (abortControllerRef.current) {
        abortControllerRef.current.abort()
      }

      // Create a new AbortController for the new request
      const abortController = new AbortController()
      abortControllerRef.current = abortController

      // Pass the signal from AbortController to the service method
      return emailSubscriptionService.submitEmailSubscription(payload, {
        signal: abortControllerRef.current.signal,
      })
    },
  })

  return {
    isSubmitEmailSubscriptionError,
    isSubmitEmailSubscriptionPending,
    submitEmailSubscriptionMutation,
    ...rest,
  }
}
