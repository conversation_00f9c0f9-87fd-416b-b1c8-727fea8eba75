import WhiteEmailIcon from '@/assets/icons/white-email-icon.svg'
import { Button } from '@/components/ui/Button/Button'
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/FormField/FormField'
import { Text } from '@/components/ui/Text/Text'
import { TextInput } from '@/components/ui/TextInput/TextInput'
import { useAppLanguage } from '@/hooks/common/useAppLanguage'
import { useLoadingScreen } from '@/hooks/common/useLoadingScreen'
import { APP_ROUTES } from '@/routes/appRoutes'
import { authService } from '@/services/auth/auth.service'
import { zodResolver } from '@hookform/resolvers/zod'
import { LinkProps, router } from 'expo-router'
import React, { useEffect } from 'react'
import { useForm } from 'react-hook-form'
import { useTranslation } from 'react-i18next'
import { View } from 'react-native'
import Toast from 'react-native-toast-message'
import { z } from 'zod'

export const ForgotPasswordForm = () => {
  const { t } = useTranslation()
  const { primaryLanguage } = useAppLanguage()
  const { showLoading, hideLoading, isLoading } = useLoadingScreen()
  const formSchema = z.object({
    email: z.email({ message: t('MES-225') }).min(1, { message: t('MES-523') }),
  })

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      email: '',
    },
    mode: 'onTouched',
  })

  const handleLogin = async (values: z.infer<typeof formSchema>) => {
    const { email } = values || {}
    showLoading()
    try {
      const res = await authService.sendResetPasswordEmail({
        email,
        language: primaryLanguage,
      })
      if (res.success) {
        router.push({
          pathname: APP_ROUTES.SEND_RESET_PASSWORD_EMAIL_SUCCESS.path,
        } as LinkProps['href'])
      } else {
        Toast.show({
          type: 'error',
          text1: t('MES-174'),
          text2: res.message,
        })
      }
    } catch (error: any) {
      Toast.show({
        type: 'error',
        text1: t('MES-174'),
        text2: error?.message,
      })
      // console.log(error)
    } finally {
      hideLoading()
    }
  }
  useEffect(() => {
    //  Re-trigger to update error message when language changed
    if (!form.formState.isValid && (form.formState.isDirty || form.formState.isSubmitted)) {
      form.trigger()
    }
  }, [primaryLanguage])
  return (
    <Form {...form}>
      <View className="flex w-full flex-col gap-y-4">
        <FormField
          control={form.control}
          name="email"
          render={({ field }) => (
            <FormItem className="w-full">
              <View className="flex w-full flex-col gap-y-1.5">
                <FormLabel required>Email</FormLabel>
                <FormControl>
                  <View className="relative w-full">
                    <TextInput
                      placeholder={t('MES-120')}
                      autoCapitalize="none"
                      keyboardType="email-address"
                      onChangeText={field.onChange}
                      onBlur={field.onBlur}
                      value={field.value}

                      // isError={!!form.formState.errors.password}
                    />
                  </View>
                </FormControl>
                <FormMessage />
              </View>
            </FormItem>
          )}
        />

        <Button
          className={`min-h-12 w-full flex-row items-center justify-center gap-2 rounded-lg px-6 py-3 `}
          disabled={form.formState.isSubmitting || isLoading}
          onPress={form.handleSubmit(handleLogin)}
        >
          <View className="flex-row items-center gap-2">
            <WhiteEmailIcon />
            <Text size="body6" variant={'white'}>
              {t('MES-655')}
            </Text>
          </View>
        </Button>
      </View>
    </Form>
  )
}
