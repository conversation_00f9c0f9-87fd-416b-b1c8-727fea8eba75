import { StackScreenBase } from '@/components/StackBase/StackScreenBase'
import { PostDetailsScreen } from '@/features/post/screens/PostDetailsScreen/PostDetailsScreen'
import { useLocalSearchParams } from 'expo-router'
import React from 'react'
import { useTranslation } from 'react-i18next'
import { SafeAreaView } from 'react-native-safe-area-context'
export default function PostDetailScreen() {
  const { t } = useTranslation()
  const { slug, id } = useLocalSearchParams()

  return (
    <SafeAreaView style={{ flex: 1, backgroundColor: 'white' }} edges={['left', 'right']}>
      <StackScreenBase
        options={{
          headerTitle: t('MES-619'),
        }}
      />
      <PostDetailsScreen slug={slug as string} id={id as string}></PostDetailsScreen>
    </SafeAreaView>
  )
}
