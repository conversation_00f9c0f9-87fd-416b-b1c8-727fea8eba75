import { useLocalSearchParams } from 'expo-router'
import React from 'react'

import { BaseWebView } from '@/components/BaseWebView/BaseWebView'
import { StackScreenBase } from '@/components/StackBase/StackScreenBase'
import { WEBVIEW_APP_ROUTES } from '@/routes/webviewRoutes'
import { useTranslation } from 'react-i18next'

export default function MedicineBodyPartsScreen() {
  const { id } = useLocalSearchParams<{
    id: string
  }>()

  const { t } = useTranslation()
  const basePath = WEBVIEW_APP_ROUTES.PRODUCTS?.children?.BODY_PARTS.path || ''
  const uri = `${basePath}/medicine?bodyPartId=${encodeURIComponent(id ?? '')}`

  return (
    <>
      <StackScreenBase
        options={{
          headerTitle: t('MES-665'),
        }}
      />
      <BaseWebView source={{ uri }} />
    </>
  )
}
