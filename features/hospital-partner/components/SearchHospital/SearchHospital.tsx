import { useEffect, useMemo } from 'react'
import { View } from 'react-native'
import { useShallow } from 'zustand/react/shallow'
import { useHospitalStore } from '../../stores/HospitalStore'
import { SearchHospitalHeader } from './SearchHospitalHeader'
import { SearchHospitalList } from './SearchHospitalList'

export const SearchHospital = () => {
  const { searchTextValue, hasActiveFilters, clearAllSearchHospitalFiltersAndSearchText } =
    useHospitalStore(
      useShallow((state) => ({
        searchTextValue: state.searchHospitalFilters['search_text'],
        hasActiveFilters: state.hasActiveFilters,
        clearAllSearchHospitalFiltersAndSearchText:
          state.clearAllSearchHospitalFiltersAndSearchText,
      })),
    )

  const shouldShowHospitalList = useMemo(() => {
    return searchTextValue || hasActiveFilters
  }, [searchTextValue, hasActiveFilters])

  useEffect(() => {
    return () => {
      clearAllSearchHospitalFiltersAndSearchText()
    }
  }, [])
  return (
    <View className="flex flex-1 flex-col gap-y-3 pt-4">
      <SearchHospitalHeader showTipsBox={!shouldShowHospitalList} />
      {shouldShowHospitalList && <SearchHospitalList />}
    </View>
  )
}
