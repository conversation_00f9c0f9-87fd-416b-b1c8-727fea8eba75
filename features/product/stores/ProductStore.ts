import { create } from 'zustand'
import { devtools } from 'zustand/middleware'
import { ProductV2TypeEnum } from '../enums'
import { ProductCategory } from '../types'

export interface ProductState {
  selectedProductType: ProductV2TypeEnum
  productTabState: {
    [ProductV2TypeEnum.DIETARY_SUPPLEMENT]: ProductTabState
    [ProductV2TypeEnum.MEDICINE]: ProductTabState
    [ProductV2TypeEnum.MEDICAL_INSTRUMENT]: ProductTabState
  }
  searchProductFilters: SearchProductFilter
  tempSearchFilters: TempSearchProductFilter
  hasActiveFilters: boolean
}

export enum ProductFilterType {
  AGE_GROUP = 'age_group',
  MEDICINE_TYPE = 'medicine_type',
}

export enum SearchProductFilterType {
  AGE_GROUP = 'age_group',
  MEDICINE_TYPE = 'medicine_type',
  PRODUCT_TYPE = 'product_type',
  CATEGORY = 'category',
  SEARCH_TEXT = 'search_text',
}

export interface ProductFilterItem {
  id: string
  label: string
  type: ProductFilterType
}

export interface SearchProductFilterItem {
  id: string
  label: string
  type: SearchProductFilterType
}

export interface ProductFilter {
  [ProductFilterType.AGE_GROUP]?: ProductFilterItem[]
  [ProductFilterType.MEDICINE_TYPE]?: ProductFilterItem[]
}

export interface SearchProductFilter {
  [SearchProductFilterType.AGE_GROUP]: SearchProductFilterItem[] | null
  [SearchProductFilterType.MEDICINE_TYPE]: SearchProductFilterItem[] | null
  [SearchProductFilterType.PRODUCT_TYPE]: SearchProductFilterItem[] | null
  [SearchProductFilterType.CATEGORY]: SearchProductFilterItem[] | null
  [SearchProductFilterType.SEARCH_TEXT]: string | null
}

export interface TempSearchProductFilter {
  [SearchProductFilterType.AGE_GROUP]: SearchProductFilterItem[] | null
  [SearchProductFilterType.MEDICINE_TYPE]: SearchProductFilterItem[] | null
  [SearchProductFilterType.PRODUCT_TYPE]: SearchProductFilterItem[] | null
  [SearchProductFilterType.CATEGORY]: SearchProductFilterItem[] | null
}

export interface ProductTabState {
  selectedCategory: string
  selectedCategoryData: ProductCategory | null
  categoryHistory: ProductCategory[]
  nestedCategories: ProductCategory[] | null
  isRoot: boolean
  filters: ProductFilter
  tempFilters: ProductFilter
  hasActiveFilters: boolean
}

export interface ProductActions {
  setSelectedProductType: (productType: ProductV2TypeEnum) => void
  resetProductStore: () => void
  setSelectedCategory: (productType: ProductV2TypeEnum, category: ProductCategory) => void
  goBackToPreviousCategory: (productType: ProductV2TypeEnum) => void
  goBackToRoot: (productType: ProductV2TypeEnum) => void
  navigateToCategoryInHistory: (productType: ProductV2TypeEnum, categoryId: string) => void
  setNestedCategories: (
    productType: ProductV2TypeEnum,
    categories: ProductCategory[] | null,
  ) => void
  setIsRoot: (productType: ProductV2TypeEnum, isRoot: boolean) => void
  addFilter: (
    productType: ProductV2TypeEnum,
    filterType: ProductFilterType,
    filter: ProductFilterItem,
  ) => void
  removeFilter: (
    productType: ProductV2TypeEnum,
    filterType: ProductFilterType,
    filterId: string,
  ) => void
  toggleFilter: (
    productType: ProductV2TypeEnum,
    filterType: ProductFilterType,
    filter: ProductFilterItem,
  ) => void
  clearFilters: (productType: ProductV2TypeEnum, filterType?: ProductFilterType) => void
  // New actions for temporary filters
  toggleTempFilter: (
    productType: ProductV2TypeEnum,
    filterType: ProductFilterType,
    filter: ProductFilterItem,
  ) => void
  applyTempFilters: (productType: ProductV2TypeEnum) => void
  resetTempFilters: (productType: ProductV2TypeEnum) => void
  // Initialize temp filters with current filters
  initTempFilters: (productType: ProductV2TypeEnum) => void
  clearAllFilters: (productType: ProductV2TypeEnum) => void
  // Search product filter actions
  toggleSearchProductFilter: (
    productType: ProductV2TypeEnum,
    filterType: SearchProductFilterType,
    filter: SearchProductFilterItem,
  ) => void
  clearSearchProductFilters: (
    productType?: ProductV2TypeEnum,
    filterType?: ProductFilterType,
  ) => void
  setSearchProductFilters: (productType: ProductV2TypeEnum, filter: ProductFilterItem) => void
  setSearchTextValue: (searchTextValue: string) => void
  // New actions for temporary search filters
  toggleTempSearchFilter: (
    filterType: SearchProductFilterType,
    filter: SearchProductFilterItem,
  ) => void
  applyTempSearchFilters: () => void
  resetTempSearchFilters: () => void
  initTempSearchFilters: () => void
  clearAllSearchProductFiltersAndSearchText: () => void
  clearAllTempSearchFiltersMedicineType: () => void
  clearAllSearchFiltersMedicineType: () => void
  clearAllTempSearchFilters: () => void
  clearAllSearchProductFilters: () => void
  filterCategoriesByProductType: (categories: { id: string; type: string }[]) => void
}

// Initial state for the product store
const initialState: ProductState = {
  selectedProductType: ProductV2TypeEnum.MEDICINE,
  hasActiveFilters: false,
  productTabState: {
    [ProductV2TypeEnum.DIETARY_SUPPLEMENT]: {
      selectedCategory: '',
      selectedCategoryData: null,
      categoryHistory: [],
      nestedCategories: null,
      isRoot: true,
      filters: {
        [ProductFilterType.AGE_GROUP]: [],
        [ProductFilterType.MEDICINE_TYPE]: [],
      },
      tempFilters: {
        [ProductFilterType.AGE_GROUP]: [],
        [ProductFilterType.MEDICINE_TYPE]: [],
      },
      hasActiveFilters: false,
    },

    [ProductV2TypeEnum.MEDICINE]: {
      selectedCategory: '',
      selectedCategoryData: null,
      categoryHistory: [],
      nestedCategories: null,
      isRoot: true,
      filters: {
        [ProductFilterType.AGE_GROUP]: [],
        [ProductFilterType.MEDICINE_TYPE]: [],
      },
      tempFilters: {
        [ProductFilterType.AGE_GROUP]: [],
        [ProductFilterType.MEDICINE_TYPE]: [],
      },
      hasActiveFilters: false,
    },
    [ProductV2TypeEnum.MEDICAL_INSTRUMENT]: {
      selectedCategory: '',
      selectedCategoryData: null,
      categoryHistory: [],
      nestedCategories: null,
      isRoot: true,
      filters: {
        [ProductFilterType.AGE_GROUP]: [],
        [ProductFilterType.MEDICINE_TYPE]: [],
      },
      tempFilters: {
        [ProductFilterType.AGE_GROUP]: [],
        [ProductFilterType.MEDICINE_TYPE]: [],
      },
      hasActiveFilters: false,
    },
  },
  searchProductFilters: {
    [SearchProductFilterType.AGE_GROUP]: null,
    [SearchProductFilterType.MEDICINE_TYPE]: null,
    [SearchProductFilterType.PRODUCT_TYPE]: null,
    [SearchProductFilterType.CATEGORY]: null,
    [SearchProductFilterType.SEARCH_TEXT]: null,
  },
  tempSearchFilters: {
    [SearchProductFilterType.AGE_GROUP]: null,
    [SearchProductFilterType.MEDICINE_TYPE]: null,
    [SearchProductFilterType.PRODUCT_TYPE]: null,
    [SearchProductFilterType.CATEGORY]: null,
  },
}

// Helper function to check if filters are active
const hasActiveFilters = (filters: ProductFilter): boolean => {
  return Object.values(filters).some((filterArray) => filterArray && filterArray.length > 0)
}

// Helper function to check if search filters are active
const hasActiveSearchFilters = (filters: SearchProductFilter): boolean => {
  return Object.entries(filters).some(([key, value]) => {
    if (key === SearchProductFilterType.SEARCH_TEXT) {
      return false
    }
    return value && Array.isArray(value) && value.length > 0
  })
}

// Create the product store with Zustand
export const useProductStore = create<ProductState & ProductActions>()(
  devtools(
    (set, get) => ({
      ...initialState,
      setSelectedProductType: (productType: ProductV2TypeEnum) => {
        // Validate that the product type is valid
        if (Object.values(ProductV2TypeEnum).includes(productType)) {
          set({ selectedProductType: productType })
        } else {
          console.warn(`Invalid product type: ${productType}`)
        }
      },
      resetProductStore: () => {
        set(initialState)
      },
      setSelectedCategory: (productType: ProductV2TypeEnum, category: ProductCategory) => {
        const currentState = get().productTabState[productType]

        // Check if this category is already in the history
        const existingIndex = currentState.categoryHistory.findIndex(
          (cat) => cat.id === category.id,
        )

        if (existingIndex !== -1) {
          // Category exists in history, truncate to that point
          const truncatedHistory = currentState.categoryHistory.slice(0, existingIndex + 1)

          set({
            productTabState: {
              ...get().productTabState,
              [productType]: {
                ...currentState,
                selectedCategory: category.id,
                selectedCategoryData: category,
                categoryHistory: truncatedHistory,
                nestedCategories: null, // Reset nested categories when selecting a category
                isRoot: false,
              },
            },
          })
        } else {
          // New category, add to history
          set({
            productTabState: {
              ...get().productTabState,
              [productType]: {
                ...currentState,
                selectedCategory: category.id,
                selectedCategoryData: category,
                categoryHistory: [...currentState.categoryHistory, category],
                nestedCategories: null, // Reset nested categories when selecting a category
                isRoot: false,
              },
            },
          })
        }
      },
      goBackToPreviousCategory: (productType: ProductV2TypeEnum) => {
        const currentState = get().productTabState[productType]
        const history = [...currentState.categoryHistory]

        if (history.length > 1) {
          // Remove current category and get the previous one
          history.pop()
          const previousCategory = history[history.length - 1]

          set({
            productTabState: {
              ...get().productTabState,
              [productType]: {
                ...currentState,
                selectedCategory: previousCategory.id,
                selectedCategoryData: previousCategory,
                categoryHistory: history,
                nestedCategories: null, // Reset nested categories when going back
                isRoot: history.length === 1, // Root if only one category in history
              },
            },
          })
        } else if (history.length === 1) {
          // Go back to root
          get().goBackToRoot(productType)
        }
      },
      goBackToRoot: (productType: ProductV2TypeEnum) => {
        set({
          productTabState: {
            ...get().productTabState,
            [productType]: {
              ...get().productTabState[productType],
              selectedCategory: '',
              selectedCategoryData: null,
              categoryHistory: [],
              nestedCategories: null,
              isRoot: true,
            },
          },
        })
      },
      navigateToCategoryInHistory: (productType: ProductV2TypeEnum, categoryId: string) => {
        const currentState = get().productTabState[productType]
        const history = [...currentState.categoryHistory]

        // Find the index of the category in history
        const categoryIndex = history.findIndex((cat) => cat.id === categoryId)

        if (categoryIndex !== -1) {
          // Truncate history to this category
          const truncatedHistory = history.slice(0, categoryIndex + 1)
          const targetCategory = truncatedHistory[truncatedHistory.length - 1]

          set({
            productTabState: {
              ...get().productTabState,
              [productType]: {
                ...currentState,
                selectedCategory: targetCategory.id,
                selectedCategoryData: targetCategory,
                categoryHistory: truncatedHistory,
                nestedCategories: null, // Reset nested categories when navigating
                isRoot: false, // Always false when navigating to a category in history
              },
            },
          })
        }
      },
      setNestedCategories: (
        productType: ProductV2TypeEnum,
        categories: ProductCategory[] | null,
      ) => {
        const currentState = get().productTabState[productType]

        set({
          productTabState: {
            ...get().productTabState,
            [productType]: {
              ...currentState,
              nestedCategories: categories,
            },
          },
        })
      },
      setIsRoot: (productType: ProductV2TypeEnum, isRoot: boolean) => {
        set({
          productTabState: {
            ...get().productTabState,
            [productType]: {
              ...get().productTabState[productType],
              isRoot,
            },
          },
        })
      },
      addFilter: (
        productType: ProductV2TypeEnum,
        filterType: ProductFilterType,
        filter: ProductFilterItem,
      ) => {
        set((state) => {
          const newFilters = {
            ...state.productTabState[productType].filters,
            [filterType]: [
              ...(state.productTabState[productType].filters[filterType] || []),
              filter,
            ],
          }

          return {
            productTabState: {
              ...state.productTabState,
              [productType]: {
                ...state.productTabState[productType],
                filters: newFilters,
                hasActiveFilters: hasActiveFilters(newFilters),
              },
            },
          }
        })
      },
      removeFilter: (
        productType: ProductV2TypeEnum,
        filterType: ProductFilterType,
        filterId: string,
      ) => {
        set((state) => {
          const newFilters = {
            ...state.productTabState[productType].filters,
            [filterType]:
              state.productTabState[productType].filters[filterType]?.filter(
                (f) => f.id !== filterId,
              ) || [],
          }

          return {
            productTabState: {
              ...state.productTabState,
              [productType]: {
                ...state.productTabState[productType],
                filters: newFilters,
                hasActiveFilters: hasActiveFilters(newFilters),
              },
            },
          }
        })
      },
      toggleFilter: (
        productType: ProductV2TypeEnum,
        filterType: ProductFilterType,
        filter: ProductFilterItem,
      ) => {
        set((state) => {
          const currentFilters = state.productTabState[productType].filters[filterType] || []
          const isSelected = currentFilters.some((f) => f.id === filter.id)

          const newFilters = {
            ...state.productTabState[productType].filters,
            [filterType]: isSelected
              ? currentFilters.filter((f) => f.id !== filter.id)
              : [...currentFilters, filter],
          }

          return {
            productTabState: {
              ...state.productTabState,
              [productType]: {
                ...state.productTabState[productType],
                filters: newFilters,
                hasActiveFilters: hasActiveFilters(newFilters),
              },
            },
          }
        })
      },
      clearFilters: (productType: ProductV2TypeEnum, filterType?: ProductFilterType) => {
        set((state) => {
          const newFilters = filterType
            ? {
                ...state.productTabState[productType].filters,
                [filterType]: [],
              }
            : {
                [ProductFilterType.AGE_GROUP]: [],
                [ProductFilterType.MEDICINE_TYPE]: [],
              }

          return {
            productTabState: {
              ...state.productTabState,
              [productType]: {
                ...state.productTabState[productType],
                filters: newFilters,
                hasActiveFilters: hasActiveFilters(newFilters),
              },
            },
          }
        })
      },
      // New actions for temporary filters
      toggleTempFilter: (
        productType: ProductV2TypeEnum,
        filterType: ProductFilterType,
        filter: ProductFilterItem,
      ) => {
        set((state) => {
          const currentTempFilters =
            state.productTabState[productType].tempFilters[filterType] || []
          const isSelected = currentTempFilters.some((f) => f.id === filter.id)

          return {
            productTabState: {
              ...state.productTabState,
              [productType]: {
                ...state.productTabState[productType],
                tempFilters: {
                  ...state.productTabState[productType].tempFilters,
                  [filterType]: isSelected
                    ? currentTempFilters.filter((f) => f.id !== filter.id)
                    : [...currentTempFilters, filter],
                },
              },
            },
          }
        })
      },
      applyTempFilters: (productType: ProductV2TypeEnum) => {
        set((state) => {
          const newFilters = state.productTabState[productType].tempFilters

          return {
            productTabState: {
              ...state.productTabState,
              [productType]: {
                ...state.productTabState[productType],
                filters: newFilters,
                hasActiveFilters: hasActiveFilters(newFilters),
              },
            },
          }
        })
      },
      resetTempFilters: (productType: ProductV2TypeEnum) => {
        set((state) => ({
          productTabState: {
            ...state.productTabState,
            [productType]: {
              ...state.productTabState[productType],
              tempFilters: {
                [ProductFilterType.AGE_GROUP]: [],
                [ProductFilterType.MEDICINE_TYPE]: [],
              },
            },
          },
        }))
      },
      // Initialize temp filters with current filters
      initTempFilters: (productType: ProductV2TypeEnum) => {
        set((state) => ({
          productTabState: {
            ...state.productTabState,
            [productType]: {
              ...state.productTabState[productType],
              tempFilters: {
                [ProductFilterType.AGE_GROUP]: [
                  ...(state.productTabState[productType].filters[ProductFilterType.AGE_GROUP] ||
                    []),
                ],
                [ProductFilterType.MEDICINE_TYPE]: [
                  ...(state.productTabState[productType].filters[ProductFilterType.MEDICINE_TYPE] ||
                    []),
                ],
              },
            },
          },
        }))
      },

      clearAllFilters: (productType: ProductV2TypeEnum) => {
        set((state) => {
          const newFilters = {
            [ProductFilterType.AGE_GROUP]: [],
            [ProductFilterType.MEDICINE_TYPE]: [],
          }

          return {
            productTabState: {
              ...state.productTabState,
              [productType]: {
                ...state.productTabState[productType],
                filters: newFilters,
                hasActiveFilters: hasActiveFilters(newFilters),
              },
            },
          }
        })
      },
      // Search product filter actions
      toggleSearchProductFilter: (
        productType: ProductV2TypeEnum,
        filterType: SearchProductFilterType,
        filter: SearchProductFilterItem,
      ) => {
        set((state) => {
          // Handle different filter types
          if (filterType === SearchProductFilterType.SEARCH_TEXT) {
            return state
          }

          const currentFilters =
            (state.searchProductFilters[filterType] as SearchProductFilterItem[] | null) || []
          const isSelected = currentFilters.some((f: SearchProductFilterItem) => f.id === filter.id)

          const newSearchFilters = {
            ...state.searchProductFilters,
            [filterType]: isSelected
              ? currentFilters.filter((f: SearchProductFilterItem) => f.id !== filter.id)
              : [...currentFilters, filter],
          }

          return {
            searchProductFilters: newSearchFilters,
            hasActiveFilters: hasActiveSearchFilters(newSearchFilters),
          }
        })
      },
      clearSearchProductFilters: (
        productType?: ProductV2TypeEnum,
        filterType?: ProductFilterType,
      ) => {
        set((state) => {
          const newSearchFilters = {
            ...state.searchProductFilters,
            ...(filterType
              ? { [filterType]: [] }
              : {
                  [ProductFilterType.AGE_GROUP]: [],
                  [ProductFilterType.MEDICINE_TYPE]: [],
                }),
          }

          return {
            searchProductFilters: newSearchFilters,
            hasActiveFilters: hasActiveSearchFilters(newSearchFilters),
          }
        })
      },
      setSearchProductFilters: (productType: ProductV2TypeEnum, filter: ProductFilterItem) => {
        // Implementation here
      },
      setSearchTextValue: (searchTextValue: string) => {
        set((state) => {
          const newSearchFilters = {
            ...state.searchProductFilters,
            [SearchProductFilterType.SEARCH_TEXT]: searchTextValue,
          }

          return {
            searchProductFilters: newSearchFilters,
            hasActiveFilters: hasActiveSearchFilters(newSearchFilters),
          }
        })
      },
      // New actions for temporary search filters
      toggleTempSearchFilter: (
        filterType: SearchProductFilterType,
        filter: SearchProductFilterItem,
      ) => {
        set((state) => {
          // Only handle filter types that are arrays, not strings like SEARCH_TEXT
          if (filterType === SearchProductFilterType.SEARCH_TEXT) {
            return state
          }

          // Type assertion to ensure we're working with array types
          const currentTempFilters =
            (state.tempSearchFilters[filterType] as SearchProductFilterItem[] | null) || []
          const isSelected = currentTempFilters.some(
            (f: SearchProductFilterItem) => f.id === filter.id,
          )

          const newTempFilters = {
            ...state.tempSearchFilters,
            [filterType]: isSelected
              ? currentTempFilters.filter((f: SearchProductFilterItem) => f.id !== filter.id)
              : [...currentTempFilters, filter],
          }

          // If we're toggling a product type, filter out categories that don't match the selected product types
          if (filterType === SearchProductFilterType.PRODUCT_TYPE) {
            const selectedProductTypes = newTempFilters[SearchProductFilterType.PRODUCT_TYPE] || []
            const currentCategories = newTempFilters[SearchProductFilterType.CATEGORY] || []

            // Only filter categories if there are selected product types
            if (selectedProductTypes.length > 0 && currentCategories.length > 0) {
              // Note: We need to get the actual category data with type information to filter properly
              // Since we don't have access to the category data with type information in the store,
              // we'll need to handle this filtering in the component where we have access to productCategories
              // For now, we'll just return the new filters and handle the filtering in the component
            }
          }

          return {
            tempSearchFilters: newTempFilters,
          }
        })
      },
      applyTempSearchFilters: () => {
        set((state) => {
          const newSearchFilters = {
            ...state.searchProductFilters,
            [SearchProductFilterType.AGE_GROUP]:
              state.tempSearchFilters[SearchProductFilterType.AGE_GROUP],
            [SearchProductFilterType.MEDICINE_TYPE]:
              state.tempSearchFilters[SearchProductFilterType.MEDICINE_TYPE],
            [SearchProductFilterType.PRODUCT_TYPE]:
              state.tempSearchFilters[SearchProductFilterType.PRODUCT_TYPE],
            [SearchProductFilterType.CATEGORY]:
              state.tempSearchFilters[SearchProductFilterType.CATEGORY],
            [SearchProductFilterType.SEARCH_TEXT]:
              state.searchProductFilters[SearchProductFilterType.SEARCH_TEXT], // Preserve search text
          }

          return {
            searchProductFilters: newSearchFilters,
            hasActiveFilters: hasActiveSearchFilters(newSearchFilters),
          }
        })
      },
      resetTempSearchFilters: () => {
        set((state) => ({
          tempSearchFilters: {
            [SearchProductFilterType.AGE_GROUP]:
              state.searchProductFilters[SearchProductFilterType.AGE_GROUP],
            [SearchProductFilterType.MEDICINE_TYPE]:
              state.searchProductFilters[SearchProductFilterType.MEDICINE_TYPE],
            [SearchProductFilterType.PRODUCT_TYPE]:
              state.searchProductFilters[SearchProductFilterType.PRODUCT_TYPE],
            [SearchProductFilterType.CATEGORY]:
              state.searchProductFilters[SearchProductFilterType.CATEGORY],
          },
        }))
      },
      clearAllTempSearchFilters: () => {
        set({
          tempSearchFilters: initialState.tempSearchFilters,
        })
      },
      initTempSearchFilters: () => {
        set((state) => ({
          tempSearchFilters: {
            [SearchProductFilterType.AGE_GROUP]:
              state.searchProductFilters[SearchProductFilterType.AGE_GROUP],
            [SearchProductFilterType.MEDICINE_TYPE]:
              state.searchProductFilters[SearchProductFilterType.MEDICINE_TYPE],
            [SearchProductFilterType.PRODUCT_TYPE]:
              state.searchProductFilters[SearchProductFilterType.PRODUCT_TYPE],
            [SearchProductFilterType.CATEGORY]:
              state.searchProductFilters[SearchProductFilterType.CATEGORY],
          },
        }))
      },
      clearAllSearchProductFiltersAndSearchText: () => {
        set({
          searchProductFilters: initialState.searchProductFilters,
          tempSearchFilters: initialState.tempSearchFilters,
          hasActiveFilters: false,
        })
      },
      clearAllTempSearchFiltersMedicineType: () => {
        set((state) => ({
          tempSearchFilters: {
            ...state.tempSearchFilters,
            [SearchProductFilterType.MEDICINE_TYPE]: [],
          },
        }))
      },
      clearAllSearchFiltersMedicineType: () => {
        set((state) => {
          const newSearchFilters = {
            ...state.searchProductFilters,
            [SearchProductFilterType.MEDICINE_TYPE]: [],
          }

          return {
            searchProductFilters: newSearchFilters,
            hasActiveFilters: hasActiveSearchFilters(newSearchFilters),
          }
        })
      },
      clearAllSearchProductFilters: () => {
        set((state) => ({
          searchProductFilters: {
            ...initialState.searchProductFilters,
            [SearchProductFilterType.SEARCH_TEXT]:
              state.searchProductFilters[SearchProductFilterType.SEARCH_TEXT],
          },
          tempSearchFilters: {
            ...state.tempSearchFilters,
          },
          hasActiveFilters: false,
        }))
      },
      filterCategoriesByProductType: (categories: { id: string; type: string }[]) => {
        set((state) => {
          const selectedProductTypes =
            state.tempSearchFilters[SearchProductFilterType.PRODUCT_TYPE] || []
          const currentCategories = state.tempSearchFilters[SearchProductFilterType.CATEGORY] || []

          // Only filter categories if there are selected product types
          if (selectedProductTypes.length > 0 && currentCategories.length > 0) {
            // Get the IDs of selected product types
            const selectedProductTypeIds = selectedProductTypes.map((pt) => pt.id)

            // Filter categories to only keep those that match the selected product types
            const filteredCategories = currentCategories.filter((category) => {
              // Find the category data to get its type
              const categoryData = categories.find((cat) => cat.id === category.id)
              return categoryData && selectedProductTypeIds.includes(categoryData.type)
            })

            return {
              tempSearchFilters: {
                ...state.tempSearchFilters,
                [SearchProductFilterType.CATEGORY]: filteredCategories,
              },
            }
          }

          return state
        })
      },
    }),
    {
      name: 'product-store',
    },
  ),
)
