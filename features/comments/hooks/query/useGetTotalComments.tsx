import { Params } from '@/types/http.type'

import { UseQueryOptions, useQuery } from '@tanstack/react-query'
import { AxiosRequestConfig } from 'axios'
import { commentService } from '../../services/comment.service'
import { TotalCommentsResponse } from '../../types'
import { commentQueryKeys } from './queryKeys'
export type TotalCommentsQueryConfig = Partial<
  Omit<UseQueryOptions<TotalCommentsResponse>, 'queryKey' | 'queryFn'>
>
export interface UseGetTotalCommentsProps {}
export const useGetTotalComments = ({
  params = {},
  options = {},
  documentId,
  relationTo,
  useQueryOptions,
  overrideKey,
}: {
  params?: Params
  options?: AxiosRequestConfig
  documentId: string
  relationTo: string
  useQueryOptions?: Omit<UseQueryOptions<TotalCommentsResponse>, 'queryKey' | 'queryFn'>
  overrideKey?: (string | Params)[]
}) => {
  const {
    isError: isGetTotalCommentsError,
    isPending: isGetTotalCommentsLoading,
    data: totalComments,
    ...rest
  } = useQuery({
    queryKey: overrideKey
      ? overrideKey
      : [commentQueryKeys['totalComments'].base(), documentId, relationTo, params],
    queryFn: async () =>
      commentService.getTotalComments({
        documentId,
        relationTo,
        params: params,
        options: options,
      }),
    ...useQueryOptions,
  })
  return {
    isGetTotalCommentsError,
    isGetTotalCommentsLoading,
    totalComments,
    ...rest,
  }
}
