import { API_ENDPOINTS } from '@/constants/endpoint.constant'
import { httpService } from '@/services/http/http.service'
import { Faculty } from '@/types/faculty.type'
import { PaginatedDocs } from '@/types/global.type'

import { Params } from '@/types/http.type'
import { AxiosRequestConfig } from 'axios'
import { SearchKeywordSuggestions } from '../types'

// SERVER / CLIENT
class FacultyService {
  private static instance: FacultyService

  private constructor() {}

  public static getInstance(): FacultyService {
    if (!FacultyService.instance) {
      FacultyService.instance = new FacultyService()
    }
    return FacultyService.instance
  }

  /**
   * Fetch faculties using the HTTP service with the fetch API.
   *
   * @param params - Optional query parameters for filtering or paginating faculties.
   * @param options - Optional fetch options such as headers, method, etc.
   * @returns A promise resolving to PaginatedDocs<Faculty> or null in case of an error.
   */
  public async getFaculties({
    params = {},
    options = {},
  }: {
    params?: Params
    options?: AxiosRequestConfig
  } = {}): Promise<PaginatedDocs<Faculty> | null> {
    const data = await httpService.get<PaginatedDocs<Faculty>>(`/${API_ENDPOINTS.faculties_api}`, {
      params,
      ...options,
    })
    return data
  }
  /**
   * Fetch faculty using the HTTP service with the fetch API.
   *
   *
   * @param id - The id of the faculty to fetch.
   * @param params - Optional query parameters
   * @param options - Optional fetch options such as headers, method, etc.
   * @returns A promise resolving to Faculty or null in case of an error.
   */
  public async getFaculty({
    id,
    params = {},
    options = {},
  }: {
    id: string
    params?: Params
    options?: AxiosRequestConfig
  }): Promise<Faculty | null> {
    const data = await httpService.get<Faculty>(`/${API_ENDPOINTS.faculties_api}/${id}`, {
      params,
      ...options,
    })
    return data
  }

  /**
   * Fetch search keyword suggestions using the HTTP service with the fetch API.
   *
   * @param params - Optional query parameters
   * @param options - Optional fetch options such as headers, method, etc.
   * @returns A promise resolving to PaginatedDocs<SearchKeywordSuggestions> or null in case of an error.
   */

  public async getSearchKeywordSuggestions({
    params = {},
    options = {},
  }: {
    params?: Params
    options?: AxiosRequestConfig
  } = {}): Promise<PaginatedDocs<SearchKeywordSuggestions> | null> {
    const data = await httpService.get<PaginatedDocs<SearchKeywordSuggestions>>(
      `/${API_ENDPOINTS.faculties_api}/search-keyword-suggestions`,
      { params, ...options },
    )
    return data
  }
}

export const facultyService = FacultyService.getInstance()
