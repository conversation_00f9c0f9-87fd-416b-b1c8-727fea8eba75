import ArchiveIcon from '@/assets/icons/archive-icon.svg'
import ArchiveTickIcon from '@/assets/icons/archive-tick-icon.svg'
import BackScreenIcon from '@/assets/icons/back-screen-icon.svg'
import { Text } from '@/components/ui/Text/Text'
import { useAuthentication } from '@/hooks/auth/useAuthentication'
import { keywordQueryKeys } from '@/hooks/query/keyword/queryKeys'
import { useUpdateFavoriteKeyword } from '@/hooks/query/keyword/useUpdateFavoriteKeyword'
import { cn } from '@/utils/cn'
import { useQueryClient } from '@tanstack/react-query'
import * as Haptics from 'expo-haptics'
import { useLocalSearchParams, useRouter } from 'expo-router'
import { useCallback, useEffect, useState } from 'react'
import { useTranslation } from 'react-i18next'
import { TouchableOpacity, View } from 'react-native'
interface KeywordDetailsHeaderProps {
  isFavorite?: boolean
  id: string
  isLoading?: boolean
}
export const KeywordDetailsHeader = ({ isFavorite, id, isLoading }: KeywordDetailsHeaderProps) => {
  const { user, status } = useAuthentication()
  const { isFromFavoriteKeywordsScreen, isFromSearchKeywordsScreen } = useLocalSearchParams()

  const [isFavoriteKeyword, setIsFavoriteKeyword] = useState<boolean>(Boolean(isFavorite))

  // Sync state with prop changes
  useEffect(() => {
    setIsFavoriteKeyword(Boolean(isFavorite))
  }, [isFavorite])

  const queryClient = useQueryClient()
  const { updateFavoriteKeywordMutation, isUpdateFavoriteKeywordPending } =
    useUpdateFavoriteKeyword()
  const handleRevalidateQueries = () => {
    queryClient.invalidateQueries({
      queryKey: keywordQueryKeys['dictionary-keywords-by-category'].base(),
      type: 'active',
      refetchType: 'active',
      exact: false,
    })
    queryClient.invalidateQueries({
      queryKey: keywordQueryKeys['dictionary-keywords'].base(),
      type: 'active',
      refetchType: 'active',
      exact: false,
    })
    if (isFromFavoriteKeywordsScreen) {
      queryClient.invalidateQueries({
        queryKey: [keywordQueryKeys['favorite-keywords-by-user'].base()],
        type: 'active',
        refetchType: 'active',
        exact: false,
      })
    }
    if (isFromSearchKeywordsScreen) {
      queryClient.invalidateQueries({
        queryKey: [...keywordQueryKeys['searchListKeywords'].base()],
        type: 'active',
        refetchType: 'active',
        exact: false,
      })
    }
  }
  const handleUpdateFavoriteKeyword = useCallback(
    (id: string, isFavorite: boolean) => {
      const type = isFavorite ? 'delete' : 'add'
      updateFavoriteKeywordMutation(
        { id, type: isFavorite ? 'delete' : 'add' },
        {
          onSuccess: () => {
            setIsFavoriteKeyword(type === 'add' ? true : false)
            handleRevalidateQueries()
          },
        },
      )
    },
    [updateFavoriteKeywordMutation, id, isFavorite],
  )
  const { t } = useTranslation()
  const router = useRouter()
  return (
    <View className="flex flex-row  items-center justify-between gap-x-2 px-4 py-3">
      <TouchableOpacity onPress={() => router.back()}>
        <BackScreenIcon width={24} height={24} />
      </TouchableOpacity>

      <Text size="body3" variant="primary">
        {t('MES-566')}
      </Text>
      {user && status === 'success' ? (
        <TouchableOpacity
          disabled={isUpdateFavoriteKeywordPending || isLoading}
          onPress={() => {
            Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light)
            handleUpdateFavoriteKeyword(id, isFavoriteKeyword)
          }}
          className={cn('h-7 w-7', (isUpdateFavoriteKeywordPending || isLoading) && 'opacity-50')}
        >
          {!isLoading && (
            <>
              {isFavoriteKeyword ? (
                <ArchiveTickIcon width={24} height={24} />
              ) : (
                <ArchiveIcon width={24} height={24} />
              )}
            </>
          )}
        </TouchableOpacity>
      ) : (
        <View className="h-7 w-7" />
      )}
    </View>
  )
}
