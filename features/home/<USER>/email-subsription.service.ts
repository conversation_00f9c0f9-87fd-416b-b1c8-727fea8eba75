import { API_ENDPOINTS } from '@/constants/endpoint.constant'
import { httpService } from '@/services/http/http.service'
import { AxiosRequestConfig } from 'axios'

// SERVER / CLIENT
class EmailSubscriptionService {
  private static instance: EmailSubscriptionService

  private constructor() {}

  public static getInstance(): EmailSubscriptionService {
    if (!EmailSubscriptionService.instance) {
      EmailSubscriptionService.instance = new EmailSubscriptionService()
    }
    return EmailSubscriptionService.instance
  }

  async submitEmailSubscription(email: string, options?: AxiosRequestConfig) {
    await httpService.post(
      `/${API_ENDPOINTS.email_subscriptions_api}`,
      {
        email,
      },
      options,
    )
  }
}

export const emailSubscriptionService = EmailSubscriptionService.getInstance()
