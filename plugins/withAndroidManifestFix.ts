import { ConfigPlugin, withAndroidManifest } from 'expo/config-plugins'

const withAndroidManifestFix: ConfigPlugin = (config) => {
  return withAndroidManifest(config, (config) => {
    const androidManifest = config.modResults
    if (!androidManifest) return config
    // Ensure the namespace 'tools' is added to the manifest to support 'tools:replace' attributes
    if (!androidManifest.manifest.$['xmlns:tools']) {
      androidManifest.manifest.$['xmlns:tools'] = 'http://schemas.android.com/tools'
    }

    const application = androidManifest?.manifest?.application?.[0]
    if (!application) return config
    application['meta-data'] ??= [] // Ensure 'meta-data' exists in the application node

    // Helper function to add or update meta-data with tools:replace
    const addOrUpdateMetaData = (name: string, value: string, replaceAttribute: string) => {
      const existingMetaData = application['meta-data']?.find(
        (item) => item['$']['android:name'] === name,
      )

      if (existingMetaData) {
        // If it exists, add or update the 'tools:replace' attribute to resolve conflicts
        ;(existingMetaData as any)['$']['tools:replace'] = replaceAttribute
      } else {
        // If it does not exist, create the meta-data entry with the required attributes
        const metaDataEntry: any = {
          $: {
            'android:name': name,
            'tools:replace': replaceAttribute,
          },
        }

        // Add the appropriate value attribute based on the type
        if (replaceAttribute === 'android:value') {
          metaDataEntry.$['android:value'] = value
        } else if (replaceAttribute === 'android:resource') {
          metaDataEntry.$['android:resource'] = value
        }

        ;(application as any)['meta-data'].push(metaDataEntry)
      }
    }

    // Handle default_notification_channel_id
    addOrUpdateMetaData(
      'com.google.firebase.messaging.default_notification_channel_id',
      'default',
      'android:value',
    )

    // Handle default_notification_color
    addOrUpdateMetaData(
      'com.google.firebase.messaging.default_notification_color',
      '@color/notification_icon_color',
      'android:resource',
    )

    // Return the updated config
    return config
  })
}

export default withAndroidManifestFix
