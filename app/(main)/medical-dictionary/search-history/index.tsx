import { StackScreenBase } from '@/components/StackBase/StackScreenBase'
import MedicalDictionarySearchHistoryScreen from '@/features/medical-dictionary/screens/MedicalDictionarySearchHistoryScreen/MedicalDictionarySearchHistoryScreen'
import { withAuthentication } from '@/hoc/withAuthentication'

import { useTranslation } from 'react-i18next'
function MedicalDictionarySearchHistoryAppScreen() {
  const { t } = useTranslation()
  return (
    <>
      <StackScreenBase
        options={{
          headerTitle: t('MES-777'),
        }}
      />
      <MedicalDictionarySearchHistoryScreen></MedicalDictionarySearchHistoryScreen>
    </>
  )
}
export default withAuthentication(MedicalDictionarySearchHistoryAppScreen)
