import { StackScreenBase } from '@/components/StackBase/StackScreenBase'
import { FavoriteProductScreen } from '@/features/user/screens/FavoriteProductScreen/FavoriteProductScreen'

import React from 'react'
import { useTranslation } from 'react-i18next'
export default function FavoriteProductAppScreen() {
  const { t } = useTranslation()

  return (
    <>
      <StackScreenBase
        options={{
          headerTitle: t('MES-715'),
          headerBackTitle: '',
        }}
      />
      <FavoriteProductScreen />
    </>
  )
}
