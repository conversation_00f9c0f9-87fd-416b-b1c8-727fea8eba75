import { useLocalSearchParams } from 'expo-router'
import { View } from 'react-native'
import { SafeAreaView } from 'react-native-safe-area-context'
import { MedicalDocumentCategories } from '../../components/MedicalDocumentCategories/MedicalDocumentCategories'
import MedicalDocumentsHeader from '../../components/MedicalDocumentsHeader/MedicalDocumentsHeader'

export default function MedicalDocumentsScreen() {
  const { shouldFilterByHospital, hospitalId } = useLocalSearchParams()

  return (
    <SafeAreaView className="flex-1 bg-white">
      <View className="flex-1">
        <MedicalDocumentsHeader />
        <MedicalDocumentCategories
          shouldFilterByHospital={shouldFilterByHospital === 'true'}
          hospitalId={hospitalId as string | undefined}
        />
      </View>
    </SafeAreaView>
  )
}
