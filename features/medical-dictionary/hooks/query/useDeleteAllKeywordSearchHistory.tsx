import { MutationOptions, useMutation } from '@tanstack/react-query'
import { useRef } from 'react'
import { keywordSearchHistoryService } from '../../services/keyword-search-history.service'
import { keywordSearchHistoryMutationKeys } from './queryKeys'

export const useDeleteAllKeywordSearchHistory = ({
  options,
}: {
  options?: Omit<MutationOptions | undefined, 'mutationFn' | 'mutationKey'>
} = {}) => {
  // Define an AbortController to cancel previous requests
  const abortControllerRef = useRef<AbortController | null>(null)

  const {
    isError: isDeleteAllKeywordSearchHistoryError,
    isPending: isDeleteAllKeywordSearchHistoryPending,
    mutate: deleteAllKeywordSearchHistoryMutation,
    ...rest
  } = useMutation({
    mutationKey: keywordSearchHistoryMutationKeys['delete-all-keyword-search-history'].base(),
    mutationFn: async () => {
      // Abort any ongoing request before initiating a new one
      if (abortControllerRef.current) {
        abortControllerRef.current.abort()
      }

      // Create a new AbortController for the new request
      const abortController = new AbortController()
      abortControllerRef.current = abortController

      // Pass the signal from AbortController to the service method
      return keywordSearchHistoryService.deleteAllKeywordSearchHistory({
        signal: abortControllerRef.current.signal,
      })
    },
    ...options,
  })

  return {
    isDeleteAllKeywordSearchHistoryError,
    isDeleteAllKeywordSearchHistoryPending,
    deleteAllKeywordSearchHistoryMutation,
    ...rest,
  }
}
