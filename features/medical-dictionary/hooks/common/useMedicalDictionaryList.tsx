import { LocaleEnum } from '@/enums/locale.enum'
import { useAuthentication } from '@/hooks/auth/useAuthentication'
import { keywordQueryKeys } from '@/hooks/query/keyword/queryKeys'
import {
  KeywordQueryConfig,
  useGetInfiniteKeywords,
} from '@/hooks/query/keyword/useGetInfiniteKeywords'
import { Keyword } from '@/types/keyword.type'
import { useQueryClient } from '@tanstack/react-query'
import { useCallback, useMemo, useState } from 'react'

export type ListItem =
  | { type: 'keyword'; item: Keyword }
  | { type: 'loading' }
  | { type: 'loading_skeleton' }

interface UseMedicalDictionaryListParams {
  activeCategory?: string[]
  config?: KeywordQueryConfig
  extendedKeys?: string[]
  searchTextValue?: string
}

export function useMedicalDictionaryList({
  activeCategory,
  config,
  extendedKeys,
  searchTextValue,
}: UseMedicalDictionaryListParams = {}) {
  const [refreshing, setRefreshing] = useState(false)
  const { user, status } = useAuthentication()
  const queryClient = useQueryClient()

  const params = useMemo(
    () => ({
      locale: 'all',
      limit: 10,
      depth: 3,
      withFavoriteFlag: Boolean(user),
      where: {
        categories: {
          in: [...(activeCategory || [])],
        },
        or: searchTextValue
          ? Object.values(LocaleEnum)?.map((locale) => ({
              [`name.${locale}`]: {
                like: searchTextValue,
              },
            }))
          : undefined,
      },
      select: {
        id: true,
        name: true,
        hiragana: true,
      },
    }),
    [user, activeCategory, searchTextValue],
  )

  const overrideKey = [...(extendedKeys || []), ...keywordQueryKeys['keywords'].base(), params]

  const {
    keywords,
    isGetKeywordsLoading,
    isRefetching,
    isFetchingNextPage,
    hasNextPage,
    fetchNextPage,
    isGetKeywordsError,
    refetch,
  } = useGetInfiniteKeywords({
    params,
    overrideKey: overrideKey,
    config: {
      staleTime: 5 * 60 * 1000,
      gcTime: 5 * 60 * 1000,
      enabled: status !== 'loading',
      ...config,
    },
  })

  const totalDocs = useMemo(() => {
    return keywords?.pages?.[0]?.totalDocs || 0
  }, [keywords])

  // Handle pull-to-refresh with improved state management
  const handleRefresh = useCallback(async () => {
    setRefreshing(true)
    try {
      // Refetch the current query instead of resetting
      await refetch()
    } catch (error) {
      console.error('Refresh error:', error)
    } finally {
      setRefreshing(false)
    }
  }, [refetch])

  // Flatten all keywords from all pages
  const allKeywords = useMemo(
    () => keywords?.pages.flatMap((page) => page?.docs || []) || [],
    [keywords],
  )

  // Create data array for FlatList with improved loading state logic
  const generateData = useCallback((): ListItem[] => {
    const items: ListItem[] = []

    // Show loading skeletons during initial load, refresh, or refetch (but not during pagination)
    const isShowingLoadingSkeleton =
      (isGetKeywordsLoading || refreshing || isRefetching || status === 'loading') &&
      !isFetchingNextPage

    if (isShowingLoadingSkeleton) {
      items.push({ type: 'loading_skeleton' })
    } else {
      // Show keyword items
      allKeywords.forEach((keyword) => {
        items.push({ type: 'keyword', item: keyword as Keyword })
      })

      // Add loading indicator if fetching next page
      if (isFetchingNextPage) {
        items.push({ type: 'loading' })
      }
    }

    return items
  }, [allKeywords, isGetKeywordsLoading, refreshing, isRefetching, isFetchingNextPage])

  const data = useMemo(() => generateData(), [generateData])

  const handleLoadMore = useCallback(() => {
    if (hasNextPage && !isFetchingNextPage) {
      fetchNextPage()
    }
  }, [hasNextPage, isFetchingNextPage, fetchNextPage])

  const resetQueries = useCallback(() => {
    queryClient.resetQueries({
      queryKey: overrideKey,
    })
  }, [queryClient, overrideKey])

  return {
    refreshing,
    data,
    isGetKeywordsError,
    handleRefresh,
    handleLoadMore,
    overrideKey,
    resetQueries,
    isGetKeywordsLoading,
    isFetchingNextPage,
    isRefetching,
    refetch,
    totalDocs,
  }
}
