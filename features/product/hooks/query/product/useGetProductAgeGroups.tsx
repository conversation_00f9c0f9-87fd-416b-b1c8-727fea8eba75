import { productService } from '@/features/product/services/product.service'
import { ProductAgeGroup } from '@/features/product/types'
import { PaginatedDocs } from '@/types/global.type'
import { Params } from '@/types/http.type'
import { UseQueryOptions, useQuery } from '@tanstack/react-query'
import { AxiosRequestConfig } from 'axios'
import { productQueryKeys } from './queryKeys'

export const useGetProductAgeGroups = ({
  params = {},
  options = {},
  useQueryOptions,
}: {
  params?: Params
  options?: AxiosRequestConfig
  useQueryOptions?: Omit<
    UseQueryOptions<PaginatedDocs<ProductAgeGroup> | null>,
    'queryKey' | 'queryFn'
  >
} = {}) => {
  const {
    isError: isGetProductAgeGroupsError,
    isPending: isGetProductAgeGroupsLoading,
    data: productAgeGroups,
    ...rest
  } = useQuery({
    queryKey: [productQueryKeys['productAgeGroups'].base(), params],
    queryFn: async () =>
      productService.getProductAgeGroups({
        params: params,
        options: options,
      }),
    ...useQueryOptions,
  })
  return {
    isGetProductAgeGroupsError,
    isGetProductAgeGroupsLoading,
    productAgeGroups,
    ...rest,
  }
}
