import ArrowDownIcon from '@/assets/icons/arrow-down-icon.svg'
import HealthCareIcon from '@/assets/icons/health-care-icon.svg'
import LightBulbIcon from '@/assets/icons/light-bulb-v2-icon.svg'
import { useOpenAuthPromptSheetBox } from '@/components/SheetBox/AuthPromptSheetBox/AuthPromptSheetBox'
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from '@/components/ui/Accordion/Accordion'
import { Text } from '@/components/ui/Text/Text'
import { BLURHASH_CODE } from '@/constants/global.constant'
import { useAuthentication } from '@/hooks/auth/useAuthentication'
import { StyledExpoImage } from '@/libs/styled'
import { APP_ROUTES, AppRoutesEnum } from '@/routes/appRoutes'
import { Faculty } from '@/types/faculty.type'
import { Media } from '@/types/media.type'
import { cn } from '@/utils/cn'
import * as Haptics from 'expo-haptics'
import { Link, usePathname } from 'expo-router'
import { useMemo, useState } from 'react'
import { useTranslation } from 'react-i18next'
import { FlatList, GestureResponderEvent, TouchableOpacity, View } from 'react-native'
import { Hospital } from '../../types'
interface HospitalDetailsExaminationFormsSectionProps {
  hospital: Hospital
}
export const HospitalDetailsExaminationFormsSection = ({
  hospital,
}: HospitalDetailsExaminationFormsSectionProps) => {
  const pathname = usePathname()

  const { t } = useTranslation()
  const { examinationFormNote, faculties } = hospital || {}
  const { note, title } = examinationFormNote
  const [activeAccordion, setActiveAccordion] = useState<string[]>(['1'])

  const groupedFaculties = useMemo(() => {
    const groups: Faculty[][] = []
    if (!faculties) return []
    for (let i = 0; i < faculties.length; i += 4) {
      groups.push(faculties?.slice(i, i + 4) as Faculty[])
    }
    return groups
  }, [faculties])

  const { user } = useAuthentication()
  const { handleOpenAuthPromptSheetBox } = useOpenAuthPromptSheetBox()
  const handlePressFaculty = (
    e: GestureResponderEvent,
    {
      facultyId,
      facultyName,
      hospitalId,
    }: {
      facultyId: string
      facultyName: string
      hospitalId: string
    },
  ) => {
    if (!user) {
      const redirectRoute = `${
        APP_ROUTES.HOSPITAL_PARTNERS.children?.[AppRoutesEnum.HOSPITAL_EXAMINATION_FORM_LIST]?.path
      }?facultyId=${facultyId}&facultyName=${facultyName}&hospitalId=${hospitalId}`
      e.preventDefault()
      handleOpenAuthPromptSheetBox({ redirect: redirectRoute })
    } else {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light)
    }
  }

  if (!faculties || faculties.length === 0) return null
  return (
    <View className="flex flex-col gap-y-3 px-4">
      <View className="flex flex-row items-center justify-between">
        <Text size="body3" variant="default">
          {t('MES-807')}
        </Text>
      </View>
      {examinationFormNote && (
        <View>
          <Accordion
            className="w-full rounded-b-xl shadow-none"
            type="single"
            variant="unfilled"
            defaultValue={['1']}
            onValueChange={(value) => {
              setActiveAccordion(value)
            }}
          >
            <AccordionItem value="1" className={cn('w-full rounded-lg bg-primary-50')}>
              <AccordionTrigger className="w-full py-3">
                <View className="w-full flex-row items-center justify-between ">
                  <View className="flex-1 flex-row items-center gap-x-3">
                    <View className="shrink-0">
                      <LightBulbIcon width={20} height={20} />
                    </View>
                    <View className="flex-1 overflow-hidden">
                      <Text size="body6" variant="primary">
                        {title}
                      </Text>
                    </View>
                  </View>
                  <View
                    className={cn(
                      'shrink-0 transition-all',
                      activeAccordion.includes('1') ? 'rotate-180' : '',
                    )}
                  >
                    <ArrowDownIcon width={18} height={18} />
                  </View>
                </View>
              </AccordionTrigger>
              <AccordionContent className="rounded-b-lg !pt-0" style={{ paddingTop: 0 }}>
                <View className="ml-8">
                  <Text size="body9" variant="default">
                    {note}
                  </Text>
                </View>
              </AccordionContent>
            </AccordionItem>
          </Accordion>
        </View>
      )}
      {faculties && (
        <FlatList
          horizontal
          pagingEnabled
          data={groupedFaculties}
          showsHorizontalScrollIndicator={false}
          decelerationRate={'fast'}
          snapToInterval={232 + 12}
          contentContainerStyle={{
            gap: 12,
          }}
          renderItem={({ item }) => (
            <View className="flex-1">
              <View className="flex-col gap-3">
                {item.map((faculty) => {
                  const iconMedia = faculty?.icon as Media

                  const iconUrl = iconMedia?.url || iconMedia?.thumbnailURL || ''

                  return (
                    <Link
                      href={{
                        pathname:
                          APP_ROUTES.HOSPITAL_PARTNERS.children?.[
                            AppRoutesEnum.HOSPITAL_EXAMINATION_FORM_LIST
                          ]?.path || '',
                        params: {
                          facultyName: faculty.name,
                          hospitalId: hospital.id,
                          facultyId: faculty.id,
                        },
                      }}
                      key={faculty.id}
                      asChild
                    >
                      <TouchableOpacity
                        className="h-[50px] w-[232px] flex-row items-center justify-start gap-3 rounded-lg bg-custom-background-hover px-3 py-2 "
                        onPress={(e) => {
                          handlePressFaculty(e, {
                            facultyId: faculty.id,
                            facultyName: faculty.name,
                            hospitalId: hospital.id,
                          })
                        }}
                      >
                        {iconUrl ? (
                          <StyledExpoImage
                            source={iconUrl}
                            contentFit="cover"
                            transition={1000}
                            className="h-8 w-8"
                            placeholder={BLURHASH_CODE}
                          />
                        ) : (
                          <HealthCareIcon className="h-8 w-8" />
                        )}
                        <Text
                          size="body8"
                          variant="default"
                          className="line-clamp-1 whitespace-pre"
                          numberOfLines={1}
                        >
                          {faculty.name}
                        </Text>
                      </TouchableOpacity>
                    </Link>
                  )
                })}
              </View>
            </View>
          )}
        />
      )}
    </View>
  )
}
