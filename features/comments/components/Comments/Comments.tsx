import EmptyBoxIcon from '@/assets/icons/empty-box.svg'
import { Skeleton } from '@/components/ui/Skeleton/Skeleton'
import { Text } from '@/components/ui/Text/Text'
import { useAuthentication } from '@/hooks/auth/useAuthentication'
import colors from '@/styles/_colors'
import { User } from '@/types/user.type'
import { useQueryClient } from '@tanstack/react-query'
import { memo, useCallback, useEffect, useMemo, useRef, useState } from 'react'
import { useTranslation } from 'react-i18next'
import { ActivityIndicator, FlatList, RefreshControl, View } from 'react-native'
import { KeyboardAwareScrollView } from 'react-native-keyboard-controller'
import { useSafeAreaInsets } from 'react-native-safe-area-context'
import { CommentItem } from '../../components/CommentItem/CommentItem'
import { useCommentManagement } from '../../hooks/common/useCommentManagement'
import { CommentsQueryConfig } from '../../hooks/query/useGetInfiniteComments'
import { Comment } from '../../types'
import { CommentInput } from '../CommentInput'
type ListItem =
  | { type: 'comment'; comment: Comment }
  | { type: 'loading' }
  | { type: 'loading_skeleton' }

interface CommentsProps {
  id: string
  relationTo: Comment['commentedOn']['relationTo']
  queryKeys?: string
  staleTime?: number
  gcTime?: number
  listLimit?: number
  nestedLimit?: number
  commentId?: string
  nestedCommentId?: string
  forceOpenNestedComments?: boolean
  showTotalComments?: boolean
  showInput?: boolean
  totalCommentsQueryKeys?: string
  showLoadmoreNestedComments?: boolean
}
export const Comments = ({
  id,
  relationTo,
  queryKeys,
  staleTime = 0,
  gcTime = 0,
  listLimit,
  nestedLimit,
  commentId,
  nestedCommentId,
  forceOpenNestedComments = false,
  showTotalComments = true,
  showInput = true,
  totalCommentsQueryKeys,
  showLoadmoreNestedComments = true,
}: CommentsProps) => {
  const { user, status } = useAuthentication()
  const { t } = useTranslation()
  const [refreshing, setRefreshing] = useState(false)
  const shouldInvalidateCommentSection = useRef<boolean>(false)
  const queryClient = useQueryClient()
  const insets = useSafeAreaInsets()
  const [commentInputHeight, setCommentInputHeight] = useState(0)

  const {
    allComments,
    totalComments,
    isGetCommentsLoading,
    isGetCommentsError,
    isGetCommentsFetchingNextPage,
    fetchNextPage,
    hasNextPage,
    refetch,
    replyingTo,
    commentInputRef,
    isAddCommentPending,
    handleReplyPress,
    handleCommentSubmit,
    handleCommentCancel,
    handleInputContentChange,
    handleCancelReply,
    handleEditComment,
    handleStartEdit,
    handleCancelEdit,
    handleDeleteComment,
    handleRetryAddComment,
    isEditCommentPending,
    editingCommentId,
    isAddCommentSuccess,
    cacheManager,
    mainParams,
    nestedParams,
  } = useCommentManagement({
    id,
    relationTo,
    customConfig: {
      staleTime,
      gcTime,
    } as CommentsQueryConfig,
    listLimit,
    nestedLimit,
    commentId,
    nestedCommentId,
  })

  const handleRefresh = useCallback(async () => {
    setRefreshing(true)
    try {
      await refetch()
    } finally {
      setRefreshing(false)
    }
  }, [refetch])

  const handleLoadMore = useCallback(() => {
    if (hasNextPage && !isGetCommentsFetchingNextPage) {
      fetchNextPage()
    }
  }, [hasNextPage, isGetCommentsFetchingNextPage, fetchNextPage])

  const data = useMemo((): ListItem[] => {
    const items: ListItem[] = []
    // Show skeleton during initial load, but not during pagination
    const isShowingLoadingSkeleton =
      (isGetCommentsLoading || refreshing) && !isGetCommentsFetchingNextPage

    if (isShowingLoadingSkeleton) {
      items.push({ type: 'loading_skeleton' })
    } else {
      allComments.forEach((comment) => {
        items.push({ type: 'comment', comment })
      })

      // Show loading indicator at bottom during pagination
      if (isGetCommentsFetchingNextPage) {
        items.push({ type: 'loading' })
      }
    }

    return items
  }, [allComments, isGetCommentsFetchingNextPage, isGetCommentsLoading, refreshing])

  const lastCommentId = useMemo(() => {
    return allComments?.[allComments.length - 1]?.id
  }, [allComments])

  const handleMarkAsShouldInvalidateCommentSection = useCallback(() => {
    shouldInvalidateCommentSection.current = true
  }, [shouldInvalidateCommentSection])

  const renderItem = useCallback(
    ({ item }: { item: ListItem }) => {
      switch (item.type) {
        case 'comment': {
          // SAFETY: Validate comment before rendering
          if (!item.comment || !item.comment.id) {
            console.warn('[Comments] Invalid comment in renderItem:', item.comment)
            return null
          }

          return (
            <View className="mb-1 flex w-full">
              <CommentItem
                comment={item.comment}
                relationTo={relationTo}
                onReply={handleReplyPress}
                isLastComment={lastCommentId === item.comment.id}
                onCommentActionSuccess={handleMarkAsShouldInvalidateCommentSection}
                onEditComment={handleEditComment}
                onStartEdit={handleStartEdit}
                onCancelEdit={handleCancelEdit}
                onDeleteComment={handleDeleteComment}
                onRetryAddComment={handleRetryAddComment}
                isEditCommentPending={isEditCommentPending}
                editingCommentId={editingCommentId}
                forceOpenNestedComments={forceOpenNestedComments}
                nestedCommentId={nestedCommentId}
                cacheManager={cacheManager}
                mainParams={mainParams}
                nestedParams={nestedParams}
                showLoadmoreNestedComments={showLoadmoreNestedComments}
              />
            </View>
          )
        }

        case 'loading':
          return (
            <View className="items-center py-4">
              <ActivityIndicator size="small" />
            </View>
          )

        case 'loading_skeleton':
          return (
            <View className="flex flex-col gap-y-4">
              {new Array(6).fill(0).map((_, index) => (
                <View key={index} className="flex flex-row gap-x-3">
                  <Skeleton className="h-12 w-12 rounded-full" />
                  <View className="flex flex-1 flex-col gap-y-2">
                    <Skeleton className="h-8 w-[120px]" />
                    <Skeleton className="h-12 w-full" />
                  </View>
                </View>
              ))}
            </View>
          )

        default:
          return null
      }
    },
    [handleReplyPress, lastCommentId, relationTo, mainParams, nestedParams, nestedCommentId],
  )

  const renderEmptyComponent = useCallback(() => {
    if (isGetCommentsError) {
      return (
        <View className="items-center  py-8">
          <Text size="body6" className="text-red-500">
            {t('MES-197')}
          </Text>
        </View>
      )
    }
    if (isGetCommentsLoading || isGetCommentsFetchingNextPage) {
      return null
    }

    return (
      <View className="items-center  py-8">
        <EmptyBoxIcon />
        <Text size="body6" variant="default">
          {t('MES-875')}
        </Text>
      </View>
    )
  }, [isGetCommentsError, isGetCommentsLoading, isGetCommentsFetchingNextPage])

  // separator handled by memoized component

  // Header rendered as element to avoid repeated re-creations
  const listHeader = useMemo(
    () => <CommentsHeader total={totalComments} show={showTotalComments} />,
    [totalComments, showTotalComments],
  )

  const keyExtractor = useCallback((item: ListItem, index: number) => {
    if (item.type === 'comment') {
      return `comment-${item.comment.id}`
    }
    return `${item.type}-${index}`
  }, [])

  const handleInvalidateCommentSection = useCallback(() => {
    if (shouldInvalidateCommentSection?.current !== true) {
      return
    }
    // Invalidate parent component queries when comments are modified
    if (queryKeys) {
      queryClient.invalidateQueries({
        queryKey: JSON.parse(queryKeys) as unknown as readonly unknown[],
        type: 'active',
        refetchType: 'active',
        exact: false,
      })
    }
    if (totalCommentsQueryKeys) {
      queryClient.invalidateQueries({
        queryKey: JSON.parse(totalCommentsQueryKeys) as unknown as readonly unknown[],
        type: 'active',
        refetchType: 'active',
        exact: false,
      })
    }
  }, [queryKeys, totalCommentsQueryKeys, shouldInvalidateCommentSection?.current])

  useEffect(() => {
    return () => {
      handleInvalidateCommentSection()
    }
  }, [shouldInvalidateCommentSection])

  useEffect(() => {
    return () => {
      if (shouldInvalidateCommentSection) {
        shouldInvalidateCommentSection.current = true
      }
    }
  }, [isAddCommentSuccess])

  return (
    <View className="flex-1 flex-col bg-white">
      <View className="flex-1 px-4">
        <FlatList
          renderScrollComponent={(props) => (
            <KeyboardAwareScrollView bottomOffset={64} {...props} />
          )}
          // Virtualization/perf tuning
          windowSize={21}
          maxToRenderPerBatch={20}
          updateCellsBatchingPeriod={50}
          initialNumToRender={15}
          removeClippedSubviews
          ListHeaderComponent={listHeader}
          keyboardShouldPersistTaps="handled"
          showsVerticalScrollIndicator={false}
          data={data}
          renderItem={renderItem}
          keyExtractor={keyExtractor}
          contentContainerStyle={{
            paddingBottom: insets.bottom + commentInputHeight, // Space for fixed input (always reserve space)
          }}
          ItemSeparatorComponent={Separator}
          ListEmptyComponent={renderEmptyComponent}
          onEndReached={handleLoadMore}
          onEndReachedThreshold={0.2}
          refreshControl={
            <RefreshControl
              refreshing={refreshing}
              onRefresh={handleRefresh}
              colors={[colors.primary['500']]}
              tintColor={colors.primary['500']}
              progressBackgroundColor="#FFFFFF"
            />
          }
        />
      </View>
      {user && status === 'success' && !editingCommentId && (
        <CommentInput
          ref={commentInputRef}
          onSubmit={handleCommentSubmit}
          onCancel={handleCommentCancel}
          isLoading={isAddCommentPending}
          placeholder={t('MES-887')}
          replyingTo={replyingTo ? (replyingTo.author as User)?.name : undefined}
          instanceId="screen"
          onContentChange={handleInputContentChange}
          onCancelReply={handleCancelReply}
          onLayout={(event) => {
            setCommentInputHeight(event.nativeEvent.layout.height)
          }}
        />
      )}
    </View>
  )
}

// Header and Separator components

const CommentsHeader = memo(({ total, show }: { total: number; show: boolean }) => {
  const { t } = useTranslation()

  if (!show) return null
  return (
    <View className=" flex flex-row items-center py-3">
      <Text size="body3">
        {t('MES-845')} ({total})
      </Text>
    </View>
  )
})

const Separator = memo(() => {
  return <View className="h-2" />
})

CommentsHeader.displayName = 'CommentsHeader'
Separator.displayName = 'Separator'
