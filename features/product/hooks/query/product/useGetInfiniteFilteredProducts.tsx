import { productService } from '@/features/product/services/product.service'
import { Product } from '@/features/product/types'
import { PaginatedDocs } from '@/types/global.type'
import { Params } from '@/types/http.type'
import { InfiniteData, UseInfiniteQueryOptions, useInfiniteQuery } from '@tanstack/react-query'
import { AxiosRequestConfig } from 'axios'
import { productQueryKeys } from './queryKeys'

type FilteredProductsQueryConfig = Partial<
  Omit<
    UseInfiniteQueryOptions<
      PaginatedDocs<Product> | null,
      Error,
      InfiniteData<PaginatedDocs<Product> | null>
    >,
    'queryFn' | 'queryKey' | 'getNextPageParam' | 'initialPageParam'
  >
>

interface UseGetInfiniteFilteredProductsProps {
  params?: Params
  options?: AxiosRequestConfig
  key?: string | number
  config?: FilteredProductsQueryConfig
  overrideKey?: string[]
}

export const useGetInfiniteFilteredProducts = ({
  params = {},
  options = {},
  config = {},
  overrideKey,
}: UseGetInfiniteFilteredProductsProps = {}) => {
  const {
    isError: isGetFilteredProductsError,
    isLoading: isGetFilteredProductsLoading,
    isFetching: isGetFilteredProductsFetching,
    isFetchingNextPage: isGetFilteredProductsFetchingNextPage,
    data: filteredProducts,
    fetchNextPage,
    hasNextPage,
    ...rest
  } = useInfiniteQuery({
    queryKey: overrideKey ? overrideKey : [productQueryKeys['filteredProducts'].base(), params],
    queryFn: async ({ pageParam = 1 }) => {
      const result = await productService.getFilteredProducts({
        params: {
          ...params,
          page: pageParam,
        },
        options,
      })
      return result
    },
    getNextPageParam: (lastPage) => lastPage?.nextPage,
    getPreviousPageParam: (lastPage) => lastPage?.prevPage,
    initialPageParam: 1,
    ...config,
  })

  return {
    isGetFilteredProductsError,
    isGetFilteredProductsLoading,
    isGetFilteredProductsFetching,
    isGetFilteredProductsFetchingNextPage,
    filteredProducts,
    fetchNextPage,
    hasNextPage,
    ...rest,
  }
}
