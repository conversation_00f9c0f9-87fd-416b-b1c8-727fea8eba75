// eslint.config.js (CommonJS)

const { defineConfig } = require('eslint/config')
const expoConfig = require('eslint-config-expo/flat')

// Plugins
const tseslintPlugin = require('@typescript-eslint/eslint-plugin')
const tseslintParser = require('@typescript-eslint/parser')
const unusedImports = require('eslint-plugin-unused-imports')

module.exports = defineConfig([
  expoConfig,
  {
    ignores: ['dist/*'],

    // ✅ Add parser (required for TypeScript rules)
    languageOptions: {
      parser: tseslintParser,
    },

    // ✅ Declare plugins here (no duplicates!)
    plugins: {
      '@typescript-eslint': tseslintPlugin,
      'unused-imports': unusedImports,
    },

    rules: {
      // ✅ Typescript ESLint rules now work correctly
      '@typescript-eslint/ban-ts-comment': 'warn',
      '@typescript-eslint/no-unused-vars': 'warn',
      '@typescript-eslint/no-explicit-any': 'off',

      // ✅ Cleanup imports automatically
      'unused-imports/no-unused-imports': 'warn',
      'unused-imports/no-unused-vars': [
        'warn',
        {
          args: 'after-used',
          argsIgnorePattern: '^_',
          varsIgnorePattern: '^_',
        },
      ],
    },
  },
])
