import { versionService } from '@/services/version/version.service'
import { Params } from '@/types/http.type'
import { Version } from '@/types/version.type'
import { UseQueryOptions, useQuery } from '@tanstack/react-query'
import { AxiosRequestConfig } from 'axios'
import { versionQueryKeys } from './queryKeys'

export const useGetVersion = ({
  id,
  params = {},
  options = {},
  useQueryOptions,
}: {
  id: string
  params?: Params
  options?: AxiosRequestConfig
  useQueryOptions?: Omit<UseQueryOptions<Version | null>, 'queryKey' | 'queryFn'>
}) => {
  const {
    isError: isGetVersionError,
    isPending: isGetVersionLoading,
    data: version,
    ...rest
  } = useQuery({
    queryKey: [versionQueryKeys['version-details'].base()],
    queryFn: async () =>
      versionService.getAppVersion({
        id: id,
        params: params,
        options: options,
      }),
    ...useQueryOptions,
  })
  return {
    isGetVersionError,
    isGetVersionLoading,
    version,
    ...rest,
  }
}
