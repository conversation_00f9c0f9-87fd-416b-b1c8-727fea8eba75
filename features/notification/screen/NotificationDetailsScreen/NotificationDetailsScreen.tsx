import { Text } from '@/components/ui/Text/Text'
import { useLocalSearchParams } from 'expo-router'
import { View } from 'react-native'
import { SafeAreaView } from 'react-native-safe-area-context'

export default function NotificationDetailsScreen() {
  const { title, description } = useLocalSearchParams()
  return (
    <SafeAreaView className="flex-1 bg-white" edges={['bottom', 'left', 'right']}>
      <View className=" p-4">
        <Text>{title}</Text>
        <Text>{description}</Text>
      </View>
    </SafeAreaView>
  )
}
