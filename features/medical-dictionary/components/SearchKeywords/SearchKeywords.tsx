import { useMemo } from 'react'
import { Keyboard, TouchableWithoutFeedback, View } from 'react-native'
import { useShallow } from 'zustand/react/shallow'
import { useMedicalDictionaryStore } from '../../stores/MedicalDictionaryStore'
import { SearchKeywordsHeader } from './SearchKeywordsHeader'
import SearchKeywordsList from './SearchKeywordsList'

export const SearchKeywords = () => {
  const { searchTextValue, hasActiveFilters } = useMedicalDictionaryStore(
    useShallow((state) => ({
      searchTextValue: state.searchText,
      hasActiveFilters: state.hasActiveFilters,
    })),
  )

  const shouldShowKeywordList = useMemo(() => {
    return searchTextValue || hasActiveFilters
  }, [searchTextValue, hasActiveFilters])

  const dismissKeyboard = () => {
    Keyboard.dismiss()
  }

  return (
    <TouchableWithoutFeedback
      onPress={dismissKeyboard}
      accessible={false}
      disabled={Boolean(shouldShowKeywordList)}
    >
      <View className="flex flex-1 flex-col gap-y-3 bg-white p-4">
        <SearchKeywordsHeader showTipsBox={!shouldShowKeywordList} />
        {shouldShowKeywordList && <SearchKeywordsList />}
      </View>
    </TouchableWithoutFeedback>
  )
}
