import { API_ENDPOINTS } from '@/constants/endpoint.constant'
import { httpService } from '@/services/http/http.service'
import { PaginatedDocs } from '@/types/global.type'
import { Params } from '@/types/http.type'

import { AxiosRequestConfig } from 'axios'
import {
  AddCommentPayload,
  AddCommentResponse,
  Comment,
  DeleteCommentResponse,
  EditCommentPayload,
  EditCommentResponse,
  LikeCommentPayload,
  ReactionCommentResponse,
  ReportCommentPayload,
  ReportCommentResponse,
  TotalCommentsResponse,
  UnlikeCommentPayload,
} from '../types'

// SERVER / CLIENT
class CommentService {
  private static instance: CommentService

  private constructor() {}

  public static getInstance(): CommentService {
    if (!CommentService.instance) {
      CommentService.instance = new CommentService()
    }
    return CommentService.instance
  }

  public async getComments({
    id,
    params = {},
    options = {},
  }: {
    id: string
    params?: Params
    options?: AxiosRequestConfig
  }): Promise<PaginatedDocs<Comment>> {
    const data = await httpService.get<PaginatedDocs<Comment>>(
      `/${API_ENDPOINTS.comments_api}/list/${id}`,
      {
        params,
        ...options,
      },
    )
    return data
  }

  public async getNestedComments({
    parentId,
    params = {},
    options = {},
  }: {
    parentId: string
    params?: Params
    options?: AxiosRequestConfig
  }): Promise<PaginatedDocs<Comment>> {
    const data = await httpService.get<PaginatedDocs<Comment>>(
      `/${API_ENDPOINTS.comments_api}/nested/${parentId}`,
      {
        params,
        ...options,
      },
    )
    return data
  }

  async addComment(
    documentId: string,
    relationTo: string,
    payload: AddCommentPayload,
    options?: AxiosRequestConfig,
  ): Promise<AddCommentResponse> {
    return await httpService.post<AddCommentResponse>(
      `/${API_ENDPOINTS.comments_api}/create/${documentId}/${relationTo}`,
      payload,
      options,
    )
  }

  async likeComment(
    payload: LikeCommentPayload,
    options?: AxiosRequestConfig,
  ): Promise<ReactionCommentResponse> {
    return await httpService.post(`/${API_ENDPOINTS.comment_likes_api}/like`, payload, options)
  }

  async unlikeComment(
    payload: UnlikeCommentPayload,
    options?: AxiosRequestConfig,
  ): Promise<ReactionCommentResponse> {
    return await httpService.delete(`/${API_ENDPOINTS.comment_likes_api}/unlike`, payload, options)
  }

  async deleteComment(
    commentId: string,
    options?: AxiosRequestConfig,
  ): Promise<DeleteCommentResponse> {
    return await httpService.delete(`/${API_ENDPOINTS.comments_api}/delete/${commentId}`, options)
  }
  async editComment(
    commentId: string,
    payload: EditCommentPayload,
    options?: AxiosRequestConfig,
  ): Promise<EditCommentResponse> {
    return await httpService.put(
      `/${API_ENDPOINTS.comments_api}/edit/${commentId}`,
      payload,
      options,
    )
  }

  async getTotalComments({
    documentId,
    relationTo,
    params = {},
    options = {},
  }: {
    documentId: string
    relationTo: string
    params?: Params
    options?: AxiosRequestConfig
  }): Promise<TotalCommentsResponse> {
    const data = await httpService.get<TotalCommentsResponse>(
      `/${API_ENDPOINTS.comments_api}/total/${documentId}/${relationTo}`,
      {
        params,
        ...options,
      },
    )
    return data
  }

  async reportComment(
    commentId: string,
    payload: ReportCommentPayload,
    options?: AxiosRequestConfig,
    locale?: string,
  ): Promise<ReportCommentResponse> {
    return await httpService.post(
      `/${API_ENDPOINTS.comment_reports_api}/report/${commentId}`,
      payload,
      options,
    )
  }
}

export const commentService = CommentService.getInstance()
