import ExportIcon from '@/assets/icons/export-icon.svg'
import CompanyLogo from '@/assets/icons/logo-wap-symbol-icon.svg'
import { Text } from '@/components/ui/Text/Text'
import { PLATFORM_TYPE } from '@/enums/version.enum'
import { useGetNewestVersion } from '@/hooks/query/version/useGetNewestVersion'
import { openBrowserAsync } from 'expo-web-browser'
import React from 'react'
import { useTranslation } from 'react-i18next'
import { TouchableOpacity, View } from 'react-native'

export const CompanyInfo = () => {
  const { t } = useTranslation()
  const { newestVersion } = useGetNewestVersion({
    options: {
      params: {
        type: PLATFORM_TYPE.APP,
      },
    },
    useQueryOptions: {
      staleTime: Infinity,
    },
  })
  const handleCompanyPress = async () => {
    try {
      await openBrowserAsync('https://www.wap-inc.com/about/')
    } catch (error) {
      console.error('Failed to open browser:', error)
    }
  }

  return (
    <View className="mt-auto items-center gap-1 rounded-md bg-custom-background-hover py-2">
      <TouchableOpacity
        className="flex-row items-center gap-3 px-3 py-3"
        onPress={handleCompanyPress}
      >
        <CompanyLogo />
        <View className="flex-1">
          <Text size="body9" variant="subdued">
            {t('MES-630')}
          </Text>
          <Text size="body6" variant="primary">
            WAP - Japan Medical Gate
          </Text>
        </View>
        <ExportIcon width={18} height={18} />
      </TouchableOpacity>
      <Text size="body9" variant="subdued">
        {t('MES-41')} v{newestVersion?.version}
      </Text>
    </View>
  )
}
