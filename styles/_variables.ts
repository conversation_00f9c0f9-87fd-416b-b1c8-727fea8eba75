import { isTablet } from '@/utils/device'

// Layout and sizing variables converted from CSS
export const variables = {
  // Layout dimensions
  layout: {
    tabBarHeight: 52,
    tabBarHeightMobile: 52,
    tabBarHeightTablet: 52,
  },

  // Spacing
  spacing: {
    xs: 4,
    sm: 8,
    md: 16,
    lg: 24,
    xl: 32,
    xxl: 48,
  },

  // Border radius
  borderRadius: {
    xs: 4,
    sm: 8,
    md: 12,
    lg: 16,
    xl: 24,
    round: 999,
  },

  // Font sizes
  fontSize: {
    xs: 12,
    sm: 14,
    md: 16,
    lg: 18,
    xl: 20,
    xxl: 24,
    xxxl: 28,
  },

  // Font weights
  fontWeight: {
    light: '300',
    normal: '400',
    medium: '500',
    semibold: '600',
    bold: '700',
  },

  // Line heights
  lineHeight: {
    tight: 1.2,
    normal: 1.4,
    relaxed: 1.6,
    loose: 1.8,
  },

  // Shadow and elevation
  shadow: {
    sm: {
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 1 },
      shadowOpacity: 0.1,
      shadowRadius: 2,
      elevation: 2,
    },
    md: {
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.15,
      shadowRadius: 4,
      elevation: 4,
    },
    lg: {
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 4 },
      shadowOpacity: 0.2,
      shadowRadius: 8,
      elevation: 8,
    },
  },
} as const

// Type definitions for better TypeScript support
export type Variables = typeof variables
export type VariableCategory = keyof Variables
export type VariableKey<T extends VariableCategory> = keyof Variables[T]

// Utility functions for easier access
export const getVariable = <T extends VariableCategory>(category: T, key: VariableKey<T>) => {
  return variables[category][key]
}

// Specific utility functions for common use cases
export const getLayoutSize = (key: keyof typeof variables.layout): number => {
  return variables.layout[key]
}

// Dynamic layout functions
export const getDynamicTabBarHeight = (): number => {
  // Import here to avoid circular dependency
  const height = isTablet()
    ? variables.layout.tabBarHeightTablet
    : variables.layout.tabBarHeightMobile

  return height
}

/**
 * Calculate exact bottom spacing for center button label to align with other tab labels
 * Based on tested values:
 * - Height 50: spacing 6
 * - Height 52: spacing 8
 * - Height 54: needs adjustment for perfect alignment
 * @param tabBarHeight - Current tab bar height
 * @param platform - Platform ('ios' | 'android') for platform-specific adjustments
 */
export const getCenterButtonLabelBottomSpacing = (
  tabBarHeight: number,
  platform?: 'ios' | 'android',
): number => {
  // Base values from testing
  const baseHeight = 50
  const baseSpacing = 6

  if (tabBarHeight <= baseHeight) {
    // Platform-specific base spacing for Android
    if (platform === 'android') {
      return baseSpacing + 6 // Add more spacing on Android to prevent touching circle button
    }
    return baseSpacing
  }

  // Linear relationship: for every 1px height increase, spacing increases by 1px
  // But at height 54, scale(10) doesn't align perfectly, so we need fine-tuning
  const heightDiff = tabBarHeight - baseHeight
  const calculatedSpacing = baseSpacing + heightDiff

  // Fine-tune for height 54: if calculated is 10 but doesn't align, adjust slightly
  // Test shows 54 needs different value - try 9.5 or adjust multiplier
  if (tabBarHeight === 54) {
    // At 54, scale(10) is too low, try 9.5 or adjust
    if (platform === 'android') {
      return 9.5 + 6 // Add Android adjustment
    }
    return 9.5 // Slightly less to raise the label
  }

  // Platform-specific adjustment: Android needs significantly more spacing to prevent text from touching circle button
  // Circle button has bottom: 20, and Android scaling may position it differently
  if (platform === 'android') {
    // Add more spacing for Android to push text further down from circle button
    // Account for circle button bottom position (20px) + extra spacing for Android
    return calculatedSpacing + 6
  }

  return calculatedSpacing
}

export const getSpacing = (key: keyof typeof variables.spacing): number => {
  return variables.spacing[key]
}

export const getBorderRadius = (key: keyof typeof variables.borderRadius): number => {
  return variables.borderRadius[key]
}

export const getFontSize = (key: keyof typeof variables.fontSize): number => {
  return variables.fontSize[key]
}

export const getFontWeight = (key: keyof typeof variables.fontWeight): string => {
  return variables.fontWeight[key]
}

export const getLineHeight = (key: keyof typeof variables.lineHeight): number => {
  return variables.lineHeight[key]
}

export const getShadow = (key: keyof typeof variables.shadow) => {
  return variables.shadow[key]
}

// Export individual categories for convenience
export const { layout, spacing, borderRadius, fontSize, fontWeight, lineHeight, shadow } = variables

// Default export
export default variables
