import { API_ENDPOINTS } from '@/constants/endpoint.constant'

import { httpService } from '@/services/http/http.service'
import { PaginatedDocs } from '@/types/global.type'
import { Params } from '@/types/http.type'
import { AxiosRequestConfig } from 'axios'
import { MedicalDocument, MedicalDocumentCategory } from '../types/index'

// SERVER / CLIENT
class MedicalDocumentService {
  private static instance: MedicalDocumentService

  private constructor() {}

  public static getInstance(): MedicalDocumentService {
    if (!MedicalDocumentService.instance) {
      MedicalDocumentService.instance = new MedicalDocumentService()
    }
    return MedicalDocumentService.instance
  }

  async getMedicalDocuments({
    params = {},
    options = {},
  }: {
    params?: Params
    options?: AxiosRequestConfig
  } = {}): Promise<PaginatedDocs<MedicalDocument>> {
    const data = await httpService.get<PaginatedDocs<MedicalDocument>>(
      `/${API_ENDPOINTS.medical_document_api}`,
      {
        params,
        ...options,
      },
    )
    return data
  }

  async getMedicalDocumentCategories({
    params = {},
    options = {},
  }: {
    params?: Params
    options?: AxiosRequestConfig
  } = {}): Promise<PaginatedDocs<MedicalDocumentCategory>> {
    const data = await httpService.get<PaginatedDocs<MedicalDocumentCategory>>(
      `/${API_ENDPOINTS.medical_document_categories_api}`,
      {
        params,
        ...options,
      },
    )
    return data
  }

  async getMedicalDocumentCategory({
    id,
    params = {},
    options = {},
  }: {
    id: string
    params?: Params
    options?: AxiosRequestConfig
  }): Promise<MedicalDocumentCategory> {
    const data = await httpService.get<MedicalDocumentCategory>(
      `/${API_ENDPOINTS.medical_document_categories_api}/${id}`,
      {
        params,
        ...options,
      },
    )
    return data
  }
}

export const medicalDocumentService = MedicalDocumentService.getInstance()
