import { MutationOptions, useMutation } from '@tanstack/react-query'
import { AxiosRequestConfig } from 'axios'
import { useRef } from 'react'
import { commentService } from '../../services/comment.service'
import { ReportCommentPayload } from '../../types'
import { commentMutationKeys } from './queryKeys'

type ReportCommentVariables = {
  commentId: string
  payload: ReportCommentPayload
  options?: AxiosRequestConfig
}

export const useReportComment = (
  options?: Omit<MutationOptions | undefined, 'mutationFn' | 'mutationKey'>,
) => {
  // Define an AbortController to cancel previous requests
  const abortControllerRef = useRef<AbortController | null>(null)

  const {
    isError: isReportCommentError,
    isPending: isReportCommentPending,
    mutate: reportCommentMutation,
    isSuccess: isReportCommentSuccess,
    ...rest
  } = useMutation({
    mutationKey: commentMutationKeys['reportComment'].base(),
    mutationFn: async ({ commentId, payload, options }: ReportCommentVariables) => {
      // Abort any ongoing request before initiating a new one
      if (abortControllerRef.current) {
        abortControllerRef.current.abort()
      }

      // Create a new AbortController for the new request
      const abortController = new AbortController()
      abortControllerRef.current = abortController

      // Pass the signal from AbortController to the service method
      return commentService.reportComment(commentId, payload, {
        signal: abortControllerRef.current.signal,
        ...options,
      })
    },
    ...options,
  })

  return {
    isReportCommentError,
    isReportCommentPending,
    reportCommentMutation,
    isReportCommentSuccess,
    ...rest,
  }
}
