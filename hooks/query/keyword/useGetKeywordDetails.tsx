import { keywordService } from '@/services/handbook/keyword.service'
import { Params } from '@/types/http.type'
import { Keyword } from '@/types/keyword.type'
import { UseQueryOptions, useQuery } from '@tanstack/react-query'
import { AxiosRequestConfig } from 'axios'
import { keywordQueryKeys } from './queryKeys'

export const useGetKeywordDetails = ({
  id,
  params = {},
  options = {},
  useQueryOptions,
}: {
  id: string
  params?: Params
  options?: AxiosRequestConfig
  useQueryOptions?: Omit<UseQueryOptions<Keyword | null>, 'queryKey' | 'queryFn'>
}) => {
  const {
    isError: isGetKeywordDetailsError,
    isPending: isGetKeywordDetailsLoading,
    data: keywordDetails,
    ...rest
  } = useQuery({
    queryKey: [keywordQueryKeys['keyword-details'].base(), id, params],
    queryFn: async () =>
      keywordService.getKeywordDetails(id, {
        params: params,
        ...options,
      }),
    ...useQueryOptions,
  })
  return {
    isGetKeywordDetailsError,
    isGetKeywordDetailsLoading,
    keywordDetails,
    ...rest,
  }
}
