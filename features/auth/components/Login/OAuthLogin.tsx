import AppleIcon from '@/assets/auth/apple-icon.svg'
import GoogleIcon from '@/assets/auth/google-icon.svg'
import { Text } from '@/components/ui/Text/Text'
import { useAuthentication } from '@/hooks/auth/useAuthentication'
import { useLoadingScreen } from '@/hooks/common/useLoadingScreen'
import { APP_ROUTES } from '@/routes/appRoutes'
import * as AppleAuthentication from 'expo-apple-authentication'
import { RelativePathString, useLocalSearchParams, useRouter } from 'expo-router'
import React from 'react'
import { useTranslation } from 'react-i18next'
import { Platform, TouchableOpacity, View } from 'react-native'
import Toast from 'react-native-toast-message'
export const OAuthLogin = () => {
  const router = useRouter()
  const { redirect, forceBack } = useLocalSearchParams()
  const { t } = useTranslation()
  const { googleLogin, appleLogin } = useAuthentication()
  const { showLoading, hideLoading } = useLoadingScreen()
  const googleSignIn = async () => {
    try {
      // Check if we're in production
      const isProd = process.env.EXPO_PUBLIC_APP_ENVIROMENT === 'production'
      if (!isProd) {
        Toast.show({
          type: 'error',
          text1: 'Google Sign-In is disabled in development',
        })
        return
      }

      // Get Google Sign-In components
      const {
        GoogleSignin,
        isSuccessResponse,
      } = require('@react-native-google-signin/google-signin')

      await GoogleSignin.hasPlayServices()
      await GoogleSignin.signOut()
      const response = await GoogleSignin.signIn()

      if (isSuccessResponse(response)) {
        const idToken = response?.data?.idToken

        if (idToken) {
          try {
            showLoading()

            await googleLogin(idToken)
            if (forceBack) {
              router.back()
            } else {
              router.replace({
                pathname: (redirect || APP_ROUTES.HOME.path) as RelativePathString,
              })
            }
            setTimeout(() => {
              Toast.show({
                type: 'success',
                text1: t('MES-187'),
                text2: t('MES-159') + '👋',
              })
            }, 300)
          } catch (error) {
            console.log('signIn error', error)
          } finally {
            hideLoading()
          }
          return
        }
      }

      Toast.show({
        type: 'error',
        text1: t('MES-241'),
      })
    } catch (error) {
      console.log('signIn error', error)
      Toast.show({
        type: 'error',
        text1: t('MES-241'),
      })
    }
  }

  const appleSignIn = async () => {
    if (Platform.OS !== 'ios') {
      return
    }
    showLoading()
    try {
      const credential = await AppleAuthentication.signInAsync({
        requestedScopes: [
          AppleAuthentication.AppleAuthenticationScope.FULL_NAME,
          AppleAuthentication.AppleAuthenticationScope.EMAIL,
        ],
      })
      if (credential) {
        try {
          const username =
            [credential.fullName?.givenName, credential.fullName?.familyName]
              .filter(Boolean)
              .join(' ') || undefined
          const result = await appleLogin(credential.identityToken ?? '', username)
          if (result) {
            if (forceBack) {
              router.back()
            } else {
              router.replace({
                pathname: (redirect || APP_ROUTES.HOME.path) as RelativePathString,
              })
            }
            setTimeout(() => {
              Toast.show({
                type: 'success',
                text1: t('MES-187'),
                text2: t('MES-159') + '👋',
              })
            }, 300)
          }
        } catch (error) {
          console.log('appleSignIn error', error)
          Toast.show({
            type: 'error',
            text1: t('MES-241'),
          })
          hideLoading()
        }
      }
    } catch (e: any) {
      if (e.code === 'ERR_REQUEST_CANCELED') {
        Toast.show({
          type: 'error',
          text1: t('MES-241'),
        })
      } else {
        Toast.show({
          type: 'error',
          text1: t('MES-241'),
        })
      }
    } finally {
      hideLoading()
    }
  }

  return (
    <View className="w-full items-center gap-2 py-3">
      <View className="w-[358px] flex-row items-center justify-center gap-2">
        <View className="h-px w-[70px] bg-custom-divider" />
        <Text size="body9" variant="subdued">
          {t('MES-664')}
        </Text>
        <View className="h-px w-[70px] bg-custom-divider" />
      </View>

      <View className="ư-full flex flex-row gap-3 p-0">
        {Platform.OS === 'ios' && (
          <TouchableOpacity
            className="flex-1 flex-row items-center justify-center gap-2 rounded-lg border border-custom-neutral-80 bg-white px-3 py-2"
            onPress={() => {
              appleSignIn()
            }}
          >
            <AppleIcon width={24} height={24} />
            <Text size="body7" variant={'subdued'}>
              Apple
            </Text>
          </TouchableOpacity>
        )}

        <TouchableOpacity
          className="flex-1 flex-row items-center justify-center gap-2 rounded-lg border border-custom-neutral-80 bg-white px-3 py-2"
          onPress={() => {
            googleSignIn()
          }}
        >
          <GoogleIcon width={24} height={24} />
          <Text size="body7" variant={'subdued'}>
            Google
          </Text>
        </TouchableOpacity>
      </View>
    </View>
  )
}
