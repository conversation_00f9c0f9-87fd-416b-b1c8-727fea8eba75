import { create } from 'zustand'
import { devtools } from 'zustand/middleware'

export interface HospitalState {
  searchHospitalFilters: SearchHospitalFilter
  tempSearchFilters: TempSearchHospitalFilter
  hasActiveFilters: boolean
}

export enum SearchHospitalFilterType {
  FACULTY_TYPE = 'faculty_type',
  SEARCH_TEXT = 'search_text',
}

export interface SearchHospitalFilterItem {
  id: string
  label: string
  type: SearchHospitalFilterType
}

export interface SearchHospitalFilter {
  [SearchHospitalFilterType.FACULTY_TYPE]: SearchHospitalFilterItem[] | null
  [SearchHospitalFilterType.SEARCH_TEXT]: string | null
}

export interface TempSearchHospitalFilter {
  [SearchHospitalFilterType.FACULTY_TYPE]: SearchHospitalFilterItem[] | null
}

export interface HospitalActions {
  toggleSearchHospitalFilter: (
    filterType: SearchHospitalFilterType,
    filter: SearchHospitalFilterItem,
  ) => void
  toggleTempSearchHospitalFilter: (
    filterType: SearchHospitalFilterType,
    filter: SearchHospitalFilterItem,
  ) => void
  setSearchTextValue: (searchTextValue: string) => void
  // Temporary filter actions
  toggleTempSearchFilter: (
    filterType: SearchHospitalFilterType,
    filter: SearchHospitalFilterItem,
  ) => void
  applyTempSearchFilters: () => void
  resetTempSearchFilters: () => void
  initTempSearchFilters: () => void
  clearAllSearchHospitalFilters: () => void
  clearAllTempSearchFilters: () => void
  clearAllSearchHospitalFiltersAndSearchText: () => void
}

// Initial state for the hospital store
const initialState: HospitalState = {
  hasActiveFilters: false,
  searchHospitalFilters: {
    [SearchHospitalFilterType.FACULTY_TYPE]: null,
    [SearchHospitalFilterType.SEARCH_TEXT]: null,
  },
  tempSearchFilters: {
    [SearchHospitalFilterType.FACULTY_TYPE]: null,
  },
}

// Helper function to check if search filters are active
const hasActiveSearchFilters = (filters: SearchHospitalFilter): boolean => {
  return Object.entries(filters).some(([key, value]) => {
    if (key === SearchHospitalFilterType.SEARCH_TEXT) {
      return false
    }
    return value && Array.isArray(value) && value.length > 0
  })
}

// Create the hospital store with Zustand
export const useHospitalStore = create<HospitalState & HospitalActions>()(
  devtools(
    (set, get) => ({
      ...initialState,
      toggleSearchHospitalFilter: (
        filterType: SearchHospitalFilterType,
        filter: SearchHospitalFilterItem,
      ) => {
        set((state) => {
          // Handle different filter types
          if (filterType === SearchHospitalFilterType.SEARCH_TEXT) {
            return state
          }

          const currentFilters =
            (state.searchHospitalFilters[filterType] as SearchHospitalFilterItem[] | null) || []
          const isSelected = currentFilters.some(
            (f: SearchHospitalFilterItem) => f.id === filter.id,
          )

          const newSearchFilters = {
            ...state.searchHospitalFilters,
            [filterType]: isSelected
              ? currentFilters.filter((f: SearchHospitalFilterItem) => f.id !== filter.id)
              : [...currentFilters, filter],
          }

          return {
            searchHospitalFilters: newSearchFilters,
            hasActiveFilters: hasActiveSearchFilters(newSearchFilters),
          }
        })
      },
      toggleTempSearchHospitalFilter: (
        filterType: SearchHospitalFilterType,
        filter: SearchHospitalFilterItem,
      ) => {
        set((state) => {
          // Handle different filter types
          if (filterType === SearchHospitalFilterType.SEARCH_TEXT) {
            return state
          }

          const currentFilters =
            (state.tempSearchFilters[filterType] as SearchHospitalFilterItem[] | null) || []
          const isSelected = currentFilters.some(
            (f: SearchHospitalFilterItem) => f.id === filter.id,
          )

          const newSearchFilters = {
            ...state.tempSearchFilters,
            [filterType]: isSelected
              ? currentFilters.filter((f: SearchHospitalFilterItem) => f.id !== filter.id)
              : [...currentFilters, filter],
          }

          return {
            tempSearchFilters: newSearchFilters,
          }
        })
      },
      setSearchTextValue: (searchTextValue: string) => {
        set((state) => {
          const newSearchFilters = {
            ...state.searchHospitalFilters,
            [SearchHospitalFilterType.SEARCH_TEXT]: searchTextValue,
          }

          return {
            searchHospitalFilters: newSearchFilters,
            hasActiveFilters: hasActiveSearchFilters(newSearchFilters),
          }
        })
      },
      // Temporary search filter actions
      toggleTempSearchFilter: (
        filterType: SearchHospitalFilterType,
        filter: SearchHospitalFilterItem,
      ) => {
        set((state) => {
          // Only handle filter types that are arrays, not strings like SEARCH_TEXT
          if (filterType === SearchHospitalFilterType.SEARCH_TEXT) {
            return state
          }

          const currentTempFilters =
            (state.tempSearchFilters[filterType] as SearchHospitalFilterItem[] | null) || []
          const isSelected = currentTempFilters.some(
            (f: SearchHospitalFilterItem) => f.id === filter.id,
          )

          const newTempFilters = {
            ...state.tempSearchFilters,
            [filterType]: isSelected
              ? currentTempFilters.filter((f: SearchHospitalFilterItem) => f.id !== filter.id)
              : [...currentTempFilters, filter],
          }

          return {
            tempSearchFilters: newTempFilters,
          }
        })
      },
      applyTempSearchFilters: () => {
        set((state) => {
          const newSearchFilters = {
            ...state.searchHospitalFilters,
            [SearchHospitalFilterType.FACULTY_TYPE]:
              state.tempSearchFilters[SearchHospitalFilterType.FACULTY_TYPE],
            [SearchHospitalFilterType.SEARCH_TEXT]:
              state.searchHospitalFilters[SearchHospitalFilterType.SEARCH_TEXT], // Preserve search text
          }

          return {
            searchHospitalFilters: newSearchFilters,
            hasActiveFilters: hasActiveSearchFilters(newSearchFilters),
          }
        })
      },
      resetTempSearchFilters: () => {
        set((state) => ({
          tempSearchFilters: {
            [SearchHospitalFilterType.FACULTY_TYPE]:
              state.searchHospitalFilters[SearchHospitalFilterType.FACULTY_TYPE],
          },
        }))
      },
      initTempSearchFilters: () => {
        set((state) => ({
          tempSearchFilters: {
            [SearchHospitalFilterType.FACULTY_TYPE]:
              state.searchHospitalFilters[SearchHospitalFilterType.FACULTY_TYPE],
          },
        }))
      },
      clearAllSearchHospitalFilters: () => {
        set((state) => ({
          searchHospitalFilters: {
            ...initialState.searchHospitalFilters,
            [SearchHospitalFilterType.SEARCH_TEXT]:
              state.searchHospitalFilters[SearchHospitalFilterType.SEARCH_TEXT],
          },
          tempSearchFilters: {
            ...state.tempSearchFilters,
          },
          hasActiveFilters: false,
        }))
      },
      clearAllTempSearchFilters: () => {
        set({
          tempSearchFilters: initialState.tempSearchFilters,
        })
      },
      clearAllSearchHospitalFiltersAndSearchText: () => {
        set((state) => ({
          searchHospitalFilters: {
            ...initialState.searchHospitalFilters,
          },
          hasActiveFilters: false,
          searchTextValue: '',
        }))
      },
    }),
    {
      name: 'hospital-store',
    },
  ),
)
