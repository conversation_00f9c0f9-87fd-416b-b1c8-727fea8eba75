import { useAuthentication } from '@/hooks/auth/useAuthentication'
import { PaginatedDocs } from '@/types/global.type'
import { InfiniteData, MutationOptions, useMutation, useQueryClient } from '@tanstack/react-query'
import { useRef } from 'react'
import { PushNotificationMessagesTabEnum } from '../../enums/push-notification.enum'
import { unifiedNotificationService } from '../../services/unified-notification.service'
import { MarkNotificationAsViewedPayload, UserPushNotification } from '../../types'
import { useAddNotificationToCache } from '../common/useAddNotificationToCache'
import { unifiedNotificationMutationKeys } from './queryKeys'

export const useMarkNotificationAsViewed = ({
  options,
}: {
  options?: Omit<MutationOptions | undefined, 'mutationFn' | 'mutationKey'>
} = {}) => {
  // Define an AbortController to cancel previous requests
  const abortControllerRef = useRef<AbortController | null>(null)
  const queryClient = useQueryClient()
  const { handleDecrementUnviewedCount } = useAddNotificationToCache()
  const { user } = useAuthentication()
  const {
    isError: isMarkNotificationAsViewedError,
    isPending: isMarkNotificationAsViewedPending,
    mutate: markNotificationAsViewedMutation,
    ...rest
  } = useMutation({
    mutationKey: unifiedNotificationMutationKeys['mark-notification-as-viewed'].base(),
    mutationFn: async ({ trackingId }: MarkNotificationAsViewedPayload) => {
      // Don't mark as viewed if no user is logged in
      if (!user) {
        console.log('🔥 useMarkNotificationAsViewed: No user logged in, skipping mark as viewed')
        return Promise.resolve()
      }

      // Abort any ongoing request before initiating a new one
      if (abortControllerRef.current) {
        abortControllerRef.current.abort()
      }

      // Create a new AbortController for the new request
      const abortController = new AbortController()
      abortControllerRef.current = abortController

      // Pass the signal from AbortController to the service method
      if (trackingId) {
        return unifiedNotificationService.markNotificationAsViewed(
          { trackingId },
          {
            signal: abortControllerRef.current.signal,
          },
        )
      }

      return Promise.resolve()
    },
    onSuccess: (data, variables) => {
      const { trackingId } = variables

      // Find the notification from any tab to get its full data
      let targetNotification: UserPushNotification | null = null

      // Try to find the notification from 'all' or 'not-viewed' tabs
      const searchTabs = [
        PushNotificationMessagesTabEnum.ALL,
        PushNotificationMessagesTabEnum.NOT_VIEWED,
      ]

      for (const tab of searchTabs) {
        const queryKey = ['user-push-notifications', tab]
        const data =
          queryClient.getQueryData<InfiniteData<PaginatedDocs<UserPushNotification>>>(queryKey)

        if (data) {
          for (const page of data.pages) {
            const found = page.docs.find((noti) => noti.trackingId === trackingId)
            if (found) {
              targetNotification = { ...found, viewed: true, viewedAt: new Date().toISOString() }
              break
            }
          }
        }

        if (targetNotification) break
      }

      // Update all three tabs: 'all', 'viewed', 'not-viewed'
      const tabTypes = [
        PushNotificationMessagesTabEnum.ALL,
        PushNotificationMessagesTabEnum.VIEWED,
        PushNotificationMessagesTabEnum.NOT_VIEWED,
      ] as const

      tabTypes.forEach((tabType) => {
        const queryKey = ['user-push-notifications', tabType]

        queryClient.setQueryData<InfiniteData<PaginatedDocs<UserPushNotification>>>(
          queryKey,
          (oldData) => {
            if (!oldData) return oldData

            if (tabType === PushNotificationMessagesTabEnum.NOT_VIEWED) {
              // For 'not-viewed' tab: Remove the notification from the list
              return {
                ...oldData,
                pages: oldData.pages.map((page) => {
                  const filteredDocs = page.docs.filter((noti) => noti.trackingId !== trackingId)
                  return {
                    ...page,
                    docs: filteredDocs,
                    totalDocs: Math.max(0, (page.totalDocs || 0) - 1),
                  }
                }),
              }
            } else if (tabType === PushNotificationMessagesTabEnum.VIEWED) {
              // For 'viewed' tab: Remove if exists, then add to the first position
              const updatedPages = oldData.pages.map((page) => ({
                ...page,
                docs: page.docs.filter((noti) => noti.trackingId !== trackingId),
              }))

              // Add the notification to the first page, first position
              if (targetNotification && updatedPages.length > 0) {
                updatedPages[0] = {
                  ...updatedPages[0],
                  docs: [targetNotification, ...updatedPages[0].docs],
                  totalDocs: (updatedPages[0].totalDocs || 0) + 1,
                }
              }

              return {
                ...oldData,
                pages: updatedPages,
              }
            } else {
              // For 'all' tab: Update the notification to viewed
              return {
                ...oldData,
                pages: oldData.pages.map((page) => ({
                  ...page,
                  docs: page.docs.map((noti) =>
                    noti.trackingId === trackingId
                      ? { ...noti, viewed: true, viewedAt: new Date().toISOString() }
                      : noti,
                  ),
                })),
              }
            }
          },
        )
      })

      // Decrement the unviewed notification count
      handleDecrementUnviewedCount(1)
    },

    ...options,
  })

  return {
    isMarkNotificationAsViewedError,
    isMarkNotificationAsViewedPending,
    markNotificationAsViewedMutation,
    ...rest,
  }
}
