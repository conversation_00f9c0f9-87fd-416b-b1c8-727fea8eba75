import { API_ENDPOINTS } from '@/constants/endpoint.constant'
import { httpService } from '@/services/http/http.service'
import { PaginatedDocs } from '@/types/global.type'
import { Params } from '@/types/http.type'

import { AxiosRequestConfig } from 'axios'
import { Product, ProductAgeGroup, ProductCategory, ProductMedicineType } from '../types'

// SERVER / CLIENT
class ProductService {
  private static instance: ProductService

  private constructor() {}

  public static getInstance(): ProductService {
    if (!ProductService.instance) {
      ProductService.instance = new ProductService()
    }
    return ProductService.instance
  }

  /**
   * Fetch products using the HTTP service
   *
   *
   * @param params - Optional query parameters for filtering or paginating edicines.
   * @param options - Optional fetch options such as headers, method, etc.
   * @returns A promise resolving to PaginatedDocs<Product> or null in case of an error.
   */
  public async getFilteredProducts({
    params = {},
    options = {},
  }: {
    params?: Params
    options?: AxiosRequestConfig
  } = {}): Promise<PaginatedDocs<Product> | null> {
    const data = await httpService.getWithMethodOverride<PaginatedDocs<Product>>(
      `/${API_ENDPOINTS.products_api}/filtered`,
      {
        params,
        ...options,
      },
    )
    return data
  }

  /**
   * Fetch product categories using the HTTP service
   *
   * @param params - Optional query parameters for filtering or paginating product categories.
   * @param options - Optional fetch options such as headers, method, etc.
   * @returns A promise resolving to PaginatedDocs<ProductCategory> or null in case of an error.
   */
  public async getProductCategories({
    params = {},
    options = {},
  }: {
    params?: Params
    options?: AxiosRequestConfig
  } = {}): Promise<PaginatedDocs<ProductCategory> | null> {
    const data = await httpService.get<PaginatedDocs<ProductCategory>>(
      `/${API_ENDPOINTS.product_categories_api}`,
      {
        params,
        ...options,
      },
    )
    return data
  }

  /**
   * Fetch product nested categories using the HTTP service
   *
   * @param params - Optional query parameters for filtering or paginating product nested categories.
   * @param options - Optional fetch options such as headers, method, etc.
   * @returns A promise resolving to PaginatedDocs<ProductCategory> or null in case of an error.
   */
  public async getProductNestedCategories({
    categoryId,
    params = {},
    options = {},
  }: {
    categoryId: string
    params?: Params
    options?: AxiosRequestConfig
  }): Promise<PaginatedDocs<ProductCategory> | null> {
    const data = await httpService.get<PaginatedDocs<ProductCategory>>(
      `/${API_ENDPOINTS.product_categories_api}/nested-categories/${categoryId}`,
      {
        params,
        ...options,
      },
    )
    return data
  }

  /**
   * Fetch product age groups using   the HTTP service
   *
   * @param params - Optional query parameters for filtering or paginating product age groups.
   * @param options - Optional fetch options such as headers, method, etc.
   * @returns A promise resolving to PaginatedDocs<ProductAgeGroup> or null in case of an error.
   */
  public async getProductAgeGroups({
    params = {},
    options = {},
  }: {
    params?: Params
    options?: AxiosRequestConfig
  }): Promise<PaginatedDocs<ProductAgeGroup> | null> {
    const data = await httpService.get<PaginatedDocs<ProductAgeGroup>>(
      `/${API_ENDPOINTS.product_age_groups_api}`,
      {
        params,
        ...options,
      },
    )
    return data
  }

  /**
   * Fetch product medicine types using   the HTTP service
   *
   * @param params - Optional query parameters for filtering or paginating product medicine types.
   * @param options - Optional fetch options such as headers, method, etc.
   * @returns A promise resolving to PaginatedDocs<ProductMedicineType> or null in case of an error.
   */
  public async getProductMedicineTypes({
    params = {},
    options = {},
  }: {
    params?: Params
    options?: AxiosRequestConfig
  }): Promise<PaginatedDocs<ProductMedicineType> | null> {
    const data = await httpService.get<PaginatedDocs<ProductMedicineType>>(
      `/${API_ENDPOINTS.medicine_types_api}`,
      {
        params,
        ...options,
      },
    )
    return data
  }
}

export const productService = ProductService.getInstance()
