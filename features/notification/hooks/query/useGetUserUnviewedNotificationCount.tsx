import { Params } from '@/types/http.type'
import { UseQueryOptions, useQuery } from '@tanstack/react-query'
import { AxiosRequestConfig } from 'axios'
import { GetUserUnviewedNotificationCountResponse } from '../../types'

import { unifiedNotificationService } from '../../services/unified-notification.service'
import { unifiedNotificationQueryKeys } from './queryKeys'

export const useGetUserUnviewedNotificationCount = ({
  options = {},
  useQueryOptions,
  overrideKey,
}: {
  options?: AxiosRequestConfig
  useQueryOptions?: Omit<
    UseQueryOptions<GetUserUnviewedNotificationCountResponse>,
    'queryKey' | 'queryFn'
  >
  overrideKey?: (string | Params)[]
} = {}) => {
  const {
    isError: isGetUserUnviewedNotificationCountError,
    isPending: isGetUserUnviewedNotificationCountLoading,
    data: userUnviewedNotificationCount,
    ...rest
  } = useQuery({
    queryKey: overrideKey
      ? overrideKey
      : [unifiedNotificationQueryKeys['user-unviewed-notification-count'].base()],
    queryFn: async () =>
      unifiedNotificationService.getUserUnviewedNotificationCount({
        options: options,
      }),
    ...useQueryOptions,
  })
  return {
    isGetUserUnviewedNotificationCountError,
    isGetUserUnviewedNotificationCountLoading,
    userUnviewedNotificationCount,
    ...rest,
  }
}
