import { useAudioPlayer } from 'expo-audio'
import * as Linking from 'expo-linking'
import { useCallback } from 'react'
import { Alert } from 'react-native'
import { WebViewMessageEvent } from 'react-native-webview'

type MessageType = 'PLAY_AUDIO' | 'NAVIGATE' | 'ALERT'

interface WebViewMessage {
  type: MessageType
  payload: any
}

/**
 * Hook to handle messages sent from WebView
 */
export const useWebViewMessage = () => {
  const player = useAudioPlayer()

  const handleWebViewMessage = useCallback(
    async (event: WebViewMessageEvent) => {
      try {
        const message: WebViewMessage = JSON.parse(event.nativeEvent.data)

        switch (message.type) {
          case 'PLAY_AUDIO':
            // Stop any current playback
            player.replace(
              'http://192.168.102.9:3000/api/media/file/keyword-audio-68342d1686fd5986d873a1db-ja-1749549073835',
            )
            // Set new audio source from the payload URL
            // const {url} = message.payload;
            // setAudioSource({uri: url});
            player.seekTo(0)
            // Play the audio
            player.play()
            break

          case 'NAVIGATE':
            // Navigate to external URL
            const { url: linkUrl } = message.payload
            if (linkUrl) {
              await Linking.openURL(linkUrl)
            }
            break

          case 'ALERT':
            // Show alert with message
            const { title, message: alertMessage } = message.payload
            Alert.alert(title || 'Message', alertMessage)
            break

          default:
            console.warn('Unknown message type:', message.type)
        }
      } catch (error) {
        console.error('Error handling WebView message:', error)
      }
    },
    [player],
  )

  return { handleWebViewMessage }
}
