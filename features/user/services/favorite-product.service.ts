import { API_ENDPOINTS } from '@/constants/endpoint.constant'

import { httpService } from '@/services/http/http.service'
import { PaginatedDocs } from '@/types/global.type'
import { Params } from '@/types/http.type'
import { AxiosRequestConfig } from 'axios'
import { FavoriteProduct } from '../types'

// SERVER / CLIENT
class FavoriteProductService {
  private static instance: FavoriteProductService

  private constructor() {}

  public static getInstance(): FavoriteProductService {
    if (!FavoriteProductService.instance) {
      FavoriteProductService.instance = new FavoriteProductService()
    }
    return FavoriteProductService.instance
  }

  async getFavoriteProductsByUser({
    params = {},
    options = {},
  }: {
    params?: Params
    options?: AxiosRequestConfig
  } = {}): Promise<PaginatedDocs<FavoriteProduct> | null> {
    const data = await httpService.get<PaginatedDocs<FavoriteProduct>>(
      `/${API_ENDPOINTS.favorite_products_api}/user`,
      {
        params,
        ...options,
      },
    )
    return data
  }

  async addFavoriteProducts(id: string, options?: AxiosRequestConfig): Promise<void> {
    await httpService.post(`/${API_ENDPOINTS.favorite_products_api}/${id}`, undefined, options)
  }

  async deleteFavoriteProducts(id: string, options?: AxiosRequestConfig): Promise<void> {
    await httpService.delete(
      `/${API_ENDPOINTS.favorite_products_api}/user/${id}`,
      undefined,
      options,
    )
  }
}

export const favoriteProductService = FavoriteProductService.getInstance()
