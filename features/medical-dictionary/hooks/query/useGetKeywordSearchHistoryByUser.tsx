import { PaginatedDocs } from '@/types/global.type'
import { Params } from '@/types/http.type'
import { UseQueryOptions, useQuery } from '@tanstack/react-query'
import { AxiosRequestConfig } from 'axios'
import { KeywordSearchHistory } from '../../types'

import { keywordSearchHistoryService } from '../../services/keyword-search-history.service'
import { keywordSearchHistoryQueryKeys } from './queryKeys'

export const useGetKeywordSearchHistoryByUser = ({
  params = {},
  options = {},
  useQueryOptions,
  overrideKey,
}: {
  params?: Params
  options?: AxiosRequestConfig
  useQueryOptions?: Omit<
    UseQueryOptions<PaginatedDocs<KeywordSearchHistory> | null>,
    'queryKey' | 'queryFn'
  >
  overrideKey?: (string | Params)[]
} = {}) => {
  const {
    isError: isGetKeywordSearchHistoryByUserError,
    isPending: isGetKeywordSearchHistoryByUserLoading,
    data: keywordSearchHistoryByUser,
    ...rest
  } = useQuery({
    queryKey: overrideKey
      ? overrideKey
      : [keywordSearchHistoryQueryKeys['keyword-search-history-by-user'].base(), params],
    queryFn: async () =>
      keywordSearchHistoryService.getKeywordSearchHistoryByUser({
        params: params,
        options: options,
      }),
    ...useQueryOptions,
  })
  return {
    isGetKeywordSearchHistoryByUserError,
    isGetKeywordSearchHistoryByUserLoading,
    keywordSearchHistoryByUser,
    ...rest,
  }
}
