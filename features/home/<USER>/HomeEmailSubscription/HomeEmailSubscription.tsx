import SubscribeEmailIcon from '@/assets/icons/email-subscribe-icon.svg'
import { Button } from '@/components/ui/Button/Button'
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormMessage,
} from '@/components/ui/FormField/FormField'
import { Text } from '@/components/ui/Text/Text'
import { TextInput } from '@/components/ui/TextInput/TextInput'
import { zodResolver } from '@hookform/resolvers/zod'
import { LinearGradient } from 'expo-linear-gradient'
import React from 'react'
import { useForm } from 'react-hook-form'
import { useTranslation } from 'react-i18next'
import { ActivityIndicator, View } from 'react-native'
import Toast from 'react-native-toast-message'
import { z } from 'zod'
import { useSubmitEmailSubscription } from '../../hooks/query/email-subscription/useSubmitEmailSubscription'
export const HomeEmailSubscription = () => {
  const { t } = useTranslation()
  const formSchema = z.object({
    email: z.email({ message: t('MES-225') }).min(1, { message: t('MES-523') }),
  })

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      email: '',
    },
    mode: 'onSubmit',
  })
  const { submitEmailSubscriptionMutation, isSubmitEmailSubscriptionPending } =
    useSubmitEmailSubscription()
  const handleSubmitEmailSubscription = (data: z.infer<typeof formSchema>) => {
    submitEmailSubscriptionMutation(data.email, {
      onSuccess: () => {
        form.reset({
          email: '',
        })
        Toast.show({
          type: 'success',
          text1: t('MES-190'),
          text2: t('MES-191'),
        })
      },
      onError: () => {
        Toast.show({
          type: 'error',
          text1: t('MES-192'),
          text2: t('MES-193'),
        })
      },
    })
  }

  return (
    <View className="overflow-hidden rounded-lg px-4">
      <LinearGradient
        colors={['#C1E1F5', '#BCF0FE', '#72BDFB']}
        start={{ x: 0.1, y: 1 }}
        end={{ x: 0.9, y: 0 }}
        style={{
          padding: 24,
          borderRadius: 8,
          overflow: 'hidden',
          borderBottomLeftRadius: 0,
          borderBottomRightRadius: 0,
          display: 'flex',
          flexDirection: 'row',
          justifyContent: 'space-between',
          alignItems: 'center',
          gap: 16,
        }}
      >
        <View className="flex flex-1 flex-col items-center justify-center gap-y-2">
          <View className="mx-auto self-start rounded-[4px] bg-custom-blue-400 px-2 py-1">
            <Text size="body8" variant="white">
              {t('MES-26')}
            </Text>
          </View>
          <Text size="body10" className="text-center">
            {t('MES-545')}
          </Text>
        </View>
        <View className="shrink-0">
          <SubscribeEmailIcon width={88} height={64} />
        </View>
      </LinearGradient>
      <Form {...form}>
        <View className="flex flex-col gap-y-2 rounded-b-lg bg-custom-background-titleTable p-4">
          <FormField
            control={form.control}
            name="email"
            render={({ field }) => (
              <FormItem className="w-full">
                <View className="flex w-full flex-col gap-y-1.5">
                  <FormControl>
                    <View className="relative w-full">
                      <TextInput
                        onSubmitEditing={form.handleSubmit(handleSubmitEmailSubscription)}
                        className="py-4"
                        placeholder={t('MES-120')}
                        autoCapitalize="none"
                        keyboardType="email-address"
                        onChangeText={field.onChange}
                        onBlur={field.onBlur}
                        value={field.value}

                        // isError={!!form.formState.errors.password}
                      />
                    </View>
                  </FormControl>
                  <FormMessage />
                </View>
              </FormItem>
            )}
          />

          <Button
            accessibilityRole="button"
            className={` w-full flex-row items-center justify-center gap-2 rounded-lg px-6 py-2 `}
            onPress={form.handleSubmit(handleSubmitEmailSubscription)}
            disabled={isSubmitEmailSubscriptionPending || form.formState.isSubmitting}
          >
            {isSubmitEmailSubscriptionPending ? (
              <ActivityIndicator size="small" color={'#fff'} />
            ) : (
              <Text size="button4" variant={'white'}>
                {t('MES-09')}
              </Text>
            )}
          </Button>
        </View>
      </Form>
    </View>
  )
}
