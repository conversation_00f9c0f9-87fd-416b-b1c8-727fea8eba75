import { AndroidImportance } from 'expo-notifications'

export interface ChannelConfig {
  id: string
  name: string
  description: string
  importance: AndroidImportance
  sound: boolean
  vibration: boolean
  vibrationPattern?: number[]
  lightColor?: string
  enableLights?: boolean
  enableVibration?: boolean
  showBadge?: boolean
}

export const APP_CHANNEL: Record<string, ChannelConfig> = {
  DEFAULT: {
    id: 'default',
    name: 'Default Channel',
    description: 'Default channel for notifications',
    importance: AndroidImportance.DEFAULT,
    sound: true,
    vibration: true,
    vibrationPattern: [0, 250, 250, 250],
    lightColor: '#FF231F7C',
    enableLights: true,
    enableVibration: true,
    showBadge: true,
  },
  REMINDERS: {
    id: 'reminders',
    name: 'Reminders',
    description: 'Channel for reminder notifications',
    importance: AndroidImportance.HIGH,
    sound: true,
    vibration: true,
    vibrationPattern: [0, 500, 200, 500],
    lightColor: '#4CAF50',
    enableLights: true,
    enableVibration: true,
    showBadge: true,
  },
  ALERTS: {
    id: 'alerts',
    name: 'Alerts',
    description: 'Channel for important alerts',
    importance: AndroidImportance.MAX,
    sound: true,
    vibration: true,
    vibrationPattern: [0, 1000, 500, 1000],
    lightColor: '#F44336',
    enableLights: true,
    enableVibration: true,
    showBadge: true,
  },
  UPDATES: {
    id: 'updates',
    name: 'Updates',
    description: 'Channel for app updates and news',
    importance: AndroidImportance.LOW,
    sound: false,
    vibration: false,
    lightColor: '#2196F3',
    enableLights: false,
    enableVibration: false,
    showBadge: false,
  },
  MEDICINE: {
    id: 'medicine',
    name: 'Medicine Reminders',
    description: 'Channel for medicine and health reminders',
    importance: AndroidImportance.HIGH,
    sound: true,
    vibration: true,
    vibrationPattern: [0, 300, 100, 300, 100, 300],
    lightColor: '#FF9800',
    enableLights: true,
    enableVibration: true,
    showBadge: true,
  },
} as const

export type NotificationChannelId = (typeof APP_CHANNEL)[keyof typeof APP_CHANNEL]['id']
