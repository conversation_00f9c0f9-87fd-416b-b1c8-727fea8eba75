import React from 'react'

import { SafeAreaView } from 'react-native-safe-area-context'
import { MainHeader } from './MainHeader/MainHeader'
interface MainLayoutProps {
  children: React.ReactNode
  withHeader?: boolean
}
export const MainLayout = ({ children, withHeader = true }: MainLayoutProps) => {
  return (
    <SafeAreaView style={{ flex: 1 }} edges={['left', 'right']}>
      {withHeader && <MainHeader></MainHeader>}
      {children}
    </SafeAreaView>
  )
}
