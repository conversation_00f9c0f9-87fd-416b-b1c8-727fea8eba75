import DoubleCheckTickIcon from '@/assets/icons/double-check-tick-icon.svg'
import EmptyBoxIcon from '@/assets/icons/empty-noti-box-icon.svg'
import FilterIcon from '@/assets/icons/filter-icon.svg'
import { NotificationRequestBox } from '@/components/NotificationRequestBox/NotificationRequestBox'
import { Skeleton } from '@/components/ui/Skeleton/Skeleton'
import { Text } from '@/components/ui/Text/Text'
import { useAuthentication } from '@/hooks/auth/useAuthentication'
import { useAppLanguage } from '@/hooks/common/useAppLanguage'
import { useLoadingScreen } from '@/hooks/common/useLoadingScreen'
import { primary } from '@/styles/_colors'
import { Params } from '@/types/http.type'
import { useQueryClient } from '@tanstack/react-query'
import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react'
import { useTranslation } from 'react-i18next'
import {
  ActivityIndicator,
  FlatList,
  Keyboard,
  RefreshControl,
  TouchableOpacity,
  View,
} from 'react-native'
import { useShallow } from 'zustand/react/shallow'
import { PushNotificationMessagesTabEnum } from '../../enums/push-notification.enum'
import { unifiedNotificationQueryKeys } from '../../hooks/query/queryKeys'
import { useGetInfiniteUserPushNotifications } from '../../hooks/query/useGetInfinteUserPushNotifications'
import { useMarkAllNotificationsAsViewed } from '../../hooks/query/useMarkAllNotificationsAsViewed'
import {
  NotificationFilterType,
  useUnifiedNotificationStore,
} from '../../stores/UnifiedNotificationStore'
import { UserPushNotification } from '../../types'
import { useOpenFilterNotificationBox } from '../FilterNotificationBox/FilterNotificationBox'
import { NotificationItem } from '../NotificationItem/NotificationItem'
type NotificationTabType = 'all' | 'viewed' | 'not-viewed'

interface UserPushNotificationTabContentProps {
  tabType: NotificationTabType
}

type ListItem =
  | { type: 'notification'; notification: UserPushNotification }
  | { type: 'loading' }
  | { type: 'loading_skeleton' }

export const UserPushNotificationTabContent = ({
  tabType,
}: UserPushNotificationTabContentProps) => {
  const { primaryLanguage } = useAppLanguage()
  const { t } = useTranslation()
  const [refreshing, setRefreshing] = useState(false)
  const { user } = useAuthentication()
  const { showLoading, hideLoading } = useLoadingScreen()
  const queryClient = useQueryClient()
  const { permissionGranted, hasActiveFilters, filters } = useUnifiedNotificationStore(
    useShallow((state) => ({
      permissionGranted: state.permissionGranted,
      filters: state.filters,
      hasActiveFilters: state.hasActiveFilters,
    })),
  )

  // Mark all as viewed mutation
  const { markAllNotificationsAsViewedMutation, isMarkAllNotificationsAsViewedPending } =
    useMarkAllNotificationsAsViewed()
  // Build query params based on tab type
  const queryParams = useMemo(() => {
    const whereConditions: Record<string, unknown>[] = [
      {
        viewed: {
          equals:
            tabType === PushNotificationMessagesTabEnum.ALL
              ? undefined
              : tabType === PushNotificationMessagesTabEnum.VIEWED
                ? true
                : false,
        },
      },
    ]

    // Add type filter if any types are selected
    const selectedTypes = filters[NotificationFilterType.TYPE]
    if (selectedTypes && selectedTypes.length > 0) {
      const typeValues = selectedTypes.map((filter) => filter.value)
      whereConditions.push({
        type: {
          in: typeValues,
        },
      })
    }

    const params: Params = {
      locale: primaryLanguage,
      limit: 16,
      where: {
        and: whereConditions,
      },
      select: {
        id: true,
        title: true,
        description: true,
        viewed: true,
        sendAt: true,
        type: true,
        data: true,
        viewedAt: true,
        createdAt: true,
        trackingId: true,
      },
      // Sort by viewedAt for 'viewed' tab, createdAt for 'all' and 'not-viewed' tabs
      sort: tabType === PushNotificationMessagesTabEnum.VIEWED ? '-viewedAt' : '-createdAt',
    }

    return params
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [primaryLanguage, tabType, user, filters])

  const {
    userPushNotifications,
    isGetUserPushNotificationsLoading,
    isGetUserPushNotificationsError,
    fetchNextPage,
    hasNextPage,
    isFetchingNextPage,
    isRefetching,
    refetch,
  } = useGetInfiniteUserPushNotifications({
    overrideKey: [...unifiedNotificationQueryKeys['user-push-notifications'].base(), tabType],
    config: {
      staleTime: 5 * 60 * 1000, // 5 minutes
      enabled: !!user,
    },
    params: queryParams,
  })

  //  Get sorted filter IDs as string for comparison
  const getFilterIds = useCallback(
    (filterArray: (typeof filters)[NotificationFilterType.TYPE] | null): string => {
      return (
        filterArray
          ?.map((f) => f.id)
          .sort()
          .join(',') || ''
      )
    },
    [],
  )

  // Helper: Check if filters actually changed
  const hasFiltersChanged = useCallback(
    (
      prev: (typeof filters)[NotificationFilterType.TYPE] | null,
      current: (typeof filters)[NotificationFilterType.TYPE] | null,
    ): boolean => {
      // Different null state = changed
      if (!!prev !== !!current) return true
      // Both null = no change
      if (!prev && !current) return false
      // Both arrays - compare IDs
      return prev !== current && getFilterIds(prev) !== getFilterIds(current)
    },
    [getFilterIds],
  )

  const resetQueryCurrentTab = useCallback(() => {
    queryClient.resetQueries({
      queryKey: [...unifiedNotificationQueryKeys['user-push-notifications'].base(), tabType],
      type: 'active',

      exact: false,
    })
  }, [tabType, queryClient])

  //  Clean up queries for other tabs
  const cleanupOtherTabs = useCallback(() => {
    const allTabTypes = [
      PushNotificationMessagesTabEnum.ALL,
      PushNotificationMessagesTabEnum.VIEWED,
      PushNotificationMessagesTabEnum.NOT_VIEWED,
    ]

    allTabTypes.forEach((otherTabType) => {
      if (otherTabType !== tabType) {
        queryClient.removeQueries({
          queryKey: [
            ...unifiedNotificationQueryKeys['user-push-notifications'].base(),
            otherTabType,
          ],
          exact: false,
        })
      }
    })
  }, [tabType, queryClient])

  // Handle filter changes: refetch current tab, remove queries for other tabs
  const prevFiltersRef = useRef<(typeof filters)[NotificationFilterType.TYPE] | null>(null)
  const isInitialMountRef = useRef(true)

  useEffect(() => {
    const currentFilters = filters[NotificationFilterType.TYPE]

    // Skip on initial mount
    if (isInitialMountRef.current) {
      isInitialMountRef.current = false
      prevFiltersRef.current = currentFilters
      return
    }

    // Check if filters changed and reset query current tab
    if (hasFiltersChanged(prevFiltersRef.current, currentFilters)) {
      resetQueryCurrentTab()
      cleanupOtherTabs()
      prevFiltersRef.current = currentFilters
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [filters, tabType, hasFiltersChanged, cleanupOtherTabs])

  // Handle pull-to-refresh
  const handleRefresh = useCallback(async () => {
    setRefreshing(true)
    try {
      await refetch()
    } finally {
      setRefreshing(false)
    }
  }, [refetch])

  // Flatten all notifications from all pages
  const allNotifications = useMemo(() => {
    if (!userPushNotifications?.pages) return []
    return userPushNotifications.pages.flatMap((page) => page?.docs || [])
  }, [userPushNotifications])

  // Create data array for FlatList
  const data = useMemo((): ListItem[] => {
    const items: ListItem[] = []

    // Show loading skeletons if initial loading or refreshing
    // Show loading skeletons during initial load or refresh (but not during pagination)
    const isShowingLoadingSkeleton =
      (isGetUserPushNotificationsLoading || refreshing) && !isFetchingNextPage

    if (isShowingLoadingSkeleton) {
      items.push({ type: 'loading_skeleton' })
    } else {
      // Add each hospital as a separate item
      allNotifications.forEach((notification) => {
        items.push({ type: 'notification', notification })
      })

      // Loading indicator for pagination
      if (isFetchingNextPage) {
        items.push({ type: 'loading' })
      }
    }

    return items
    //eslint-disable-next-line react-hooks/exhaustive-deps
  }, [
    allNotifications,
    isFetchingNextPage,
    isGetUserPushNotificationsLoading,
    isRefetching,
    refreshing,
  ])

  const handleLoadMore = useCallback(() => {
    if (hasNextPage && !isFetchingNextPage) {
      fetchNextPage()
    }
  }, [hasNextPage, isFetchingNextPage, fetchNextPage])

  const renderItem = useCallback(({ item }: { item: ListItem }) => {
    switch (item.type) {
      case 'notification':
        return <NotificationItem notification={item.notification} />

      case 'loading':
        return (
          <View className="items-center py-4">
            <ActivityIndicator size="small" />
          </View>
        )

      case 'loading_skeleton':
        return (
          <View className="flex flex-col gap-3">
            {[...Array.from({ length: 8 }).fill(1)].map((_, index) => (
              <View key={index} className="rounded-lg border border-gray-200 bg-white p-4">
                <Skeleton className="mb-2 h-4 w-3/4 rounded-lg bg-gray-200" />
                <Skeleton className="mb-2 h-3 w-1/2 rounded-lg bg-gray-200" />
                <Skeleton className="h-3 w-1/4 rounded-lg bg-gray-200" />
              </View>
            ))}
          </View>
        )

      default:
        return null
    }
  }, [])

  const renderEmptyComponent = useCallback(() => {
    if (isGetUserPushNotificationsError) {
      return (
        <View className="items-center py-8">
          <Text size="body6" className="text-red-500">
            {t('MES-197')}
          </Text>
        </View>
      )
    }

    return (
      <View className="flex flex-col items-center justify-center gap-y-4 py-8">
        <EmptyBoxIcon />
        <Text size="body7" variant="default" className="text-center">
          {t('MES-837')}
        </Text>
      </View>
    )
  }, [isGetUserPushNotificationsError, t])

  const renderSeparator = useCallback(() => {
    return <View className="h-3" />
  }, [])

  const keyExtractor = useCallback(
    (item: ListItem, index: number) => {
      if (item.type === 'notification') {
        return `notification-${item.notification.id}-${index}-${tabType}`
      }
      return `${item.type}-${index}-${tabType}`
    },
    [tabType],
  )

  const hasUnviewedNotifications = useMemo(() => {
    if (tabType === PushNotificationMessagesTabEnum.VIEWED) return false
    return allNotifications.some((notification) => !notification.viewed)
  }, [tabType, allNotifications])

  const handleMarkAllAsViewed = useCallback(() => {
    showLoading()
    markAllNotificationsAsViewedMutation(undefined, {
      onSettled: () => {
        hideLoading()
      },
    })
    //eslint-disable-next-line react-hooks/exhaustive-deps
  }, [markAllNotificationsAsViewedMutation])

  const { handleOpenFilterNotificationBox } = useOpenFilterNotificationBox()
  const renderHeaderComponent = useCallback(() => {
    return (
      <View className="mb-4 flex flex-col gap-y-2">
        {!permissionGranted && <NotificationRequestBox />}

        {/* Filter Button */}
        <View className="my-3 flex w-full flex-row items-center justify-between gap-4">
          {/* Mark All as Viewed Button - Only show in 'all' and 'not-viewed' tabs when there are unviewed notifications */}
          {tabType !== PushNotificationMessagesTabEnum.VIEWED && hasUnviewedNotifications ? (
            <View className=" ">
              <TouchableOpacity
                onPress={handleMarkAllAsViewed}
                disabled={isMarkAllNotificationsAsViewedPending}
                style={{
                  opacity: isMarkAllNotificationsAsViewedPending ? 0.6 : 1,
                }}
                className="flex flex-row items-center gap-x-2"
                hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
              >
                <DoubleCheckTickIcon></DoubleCheckTickIcon>
                <Text size="body6" variant="primary">
                  {t('MES-834')}
                </Text>
              </TouchableOpacity>
            </View>
          ) : (
            // Fake box to keep the layout consistent
            <View className="size-6"></View>
          )}
          {/* Filter Button */}
          <TouchableOpacity
            hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
            onPress={() => {
              Keyboard.dismiss()
              handleOpenFilterNotificationBox()
            }}
            className="relative flex flex-row items-center gap-x-2 pr-1"
          >
            <Text size="body6" variant="primary">
              {t('MES-481')}
            </Text>

            <FilterIcon width={16} height={16} />
            {hasActiveFilters && (
              <View className="absolute  -top-0.5 right-[0.5px] size-2 rounded-full bg-custom-danger-600/80 opacity-80" />
            )}
          </TouchableOpacity>
        </View>
      </View>
    )
    //eslint-disable-next-line react-hooks/exhaustive-deps
  }, [permissionGranted, tabType, hasUnviewedNotifications, hasActiveFilters])
  return (
    <View className="h-full flex-1">
      <FlatList
        data={data}
        renderItem={renderItem}
        keyExtractor={keyExtractor}
        onEndReached={handleLoadMore}
        onEndReachedThreshold={0.1}
        ListHeaderComponent={renderHeaderComponent}
        ListEmptyComponent={renderEmptyComponent}
        showsVerticalScrollIndicator={false}
        removeClippedSubviews={false}
        maxToRenderPerBatch={16}
        windowSize={16}
        initialNumToRender={10}
        contentContainerStyle={{ paddingBottom: 20, paddingHorizontal: 0 }}
        ItemSeparatorComponent={renderSeparator}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={handleRefresh}
            colors={[primary['500']]}
            tintColor={primary['500']}
            progressBackgroundColor="#FFFFFF"
          />
        }
      />
    </View>
  )
}
