import type {
  BottomTabNavigationEventMap,
  BottomTabNavigationOptions,
  BottomTabNavigationProp,
} from '@react-navigation/bottom-tabs'
import type {
  DefaultNavigatorOptions,
  ParamListBase,
  TabNavigationState,
  TabRouterOptions,
} from '@react-navigation/native'
import React from 'react'
import type { StyleProp, ViewStyle } from 'react-native'

type Enumerate<N extends number, Acc extends number[] = []> = Acc['length'] extends N
  ? Acc[number]
  : Enumerate<N, [...Acc, Acc['length']]>

type Range<F extends number, T extends number> = Exclude<Enumerate<T>, Enumerate<F>>

export interface ICurvedBottomBarRef {
  setVisible: (visible: boolean) => void
}

interface Props {
  ref?:
    | React.RefObject<ICurvedBottomBarRef>
    | React.RefObject<ICurvedBottomBarRef>
    | null
    | undefined
  type?: 'DOWN' | 'UP'
  circlePosition?: 'CENTER' | 'LEFT' | 'RIGHT'
  style?: StyleProp<ViewStyle>
  shadowStyle?: StyleProp<ViewStyle> // Do not use this Prop in Expo
  width?: number
  height?: Range<50, 91>
  borderTopLeftRight?: boolean
  circleWidth?: Range<50, 61>
  bgColor?: string
  borderColor?: string
  borderWidth?: number
  initialRouteName: string
  defaultScreenOptions?: unknown
  renderCircle: ({
    routeName,
    selectedTab,
    navigate,
  }: {
    routeName: string
    selectedTab: string
    navigate: (selectedTab: string) => void
  }) => React.ReactElement
  tabBar?: ({
    routeName,
    selectedTab,
    navigate,
  }: {
    routeName: string
    selectedTab: string
    navigate: (selectedTab: string) => void
  }) => React.ReactElement
}

export type NavigatorBottomBarProps = DefaultNavigatorOptions<
  ParamListBase, // ParamList
  undefined, // NavigatorID (most common)
  TabNavigationState<ParamListBase>, // State
  BottomTabNavigationOptions, // ScreenOptions
  BottomTabNavigationEventMap, // EventMap
  BottomTabNavigationProp<ParamListBase, string> // Navigation (important)
> &
  Props &
  TabRouterOptions
