import { TouchableOpacity, View } from 'react-native'

import { Text } from '@/components/ui/Text/Text'
import { APP_ROUTES, AppRoutesEnum } from '@/routes/appRoutes'
import { Link, LinkProps } from 'expo-router'
import { useMemo } from 'react'
import { useTranslation } from 'react-i18next'
import { SafeAreaView } from 'react-native-safe-area-context'
import { Comments } from '../../components/Comments/Comments'
import { Comment } from '../../types'
interface CommentsScreenProps {
  id: string
  relationTo: Comment['commentedOn']['relationTo']
  queryKeys?: string
  staleTime?: number
  gcTime?: number
  listLimit?: number
  nestedLimit?: number
  commentId?: string
  nestedCommentId?: string
  notiTitle?: string
  docSlug?: string
  documentId?: string
}
export const CommentDetailsScreen = ({
  id,
  relationTo,
  queryKeys,
  staleTime,
  gcTime,
  listLimit,
  nestedLimit,
  commentId,
  nestedCommentId,
  notiTitle,
  docSlug,
  documentId,
}: CommentsScreenProps) => {
  const { t } = useTranslation()
  const getRouting = useMemo(() => {
    switch (relationTo) {
      case 'posts':
        return {
          pathname: APP_ROUTES.POSTS.path + '/[slug]',
          params: { slug: docSlug, id: documentId },
        } as LinkProps['href']
      case 'products':
        return {
          pathname:
            APP_ROUTES.PRODUCTS.children?.[AppRoutesEnum.PRODUCTS_DETAIL_V2].path + '/[slug]',
          params: {
            slug: docSlug,
            id: documentId,
          },
        } as LinkProps['href']
      case 'keywords':
        return {
          pathname:
            APP_ROUTES.MEDICAL_DICTIONARY?.children?.[
              AppRoutesEnum.MEDICAL_DICTIONARY_KEYWORD_DETAILS
            ]?.path,
          params: {
            id: documentId,
          },
        } as LinkProps['href']
      default:
        return null
    }
  }, [relationTo, docSlug, documentId])

  const getLinkText = useMemo(() => {
    switch (relationTo) {
      case 'posts':
        return t('MES-883')
      case 'products':
        return t('MES-884')
      case 'keywords':
        return t('MES-885')
      default:
        return ''
    }
  }, [relationTo, t])

  return (
    <SafeAreaView className="flex-1 bg-white" edges={['left', 'right']}>
      <View className="flex flex-1 flex-col gap-y-4 pt-4">
        <View
          className="flex flex-row items-center  px-4"
          style={{
            gap: 12,
          }}
        >
          <View className="flex-1">
            <Text size="body6" variant="default" numberOfLines={2}>
              {notiTitle}
            </Text>
          </View>
          {getRouting && (
            <Link href={getRouting} className="rounded-lg  bg-primary-500 px-4 py-3" asChild>
              <TouchableOpacity>
                <Text size="button5" variant="white">
                  {getLinkText}
                </Text>
              </TouchableOpacity>
            </Link>
          )}
        </View>
        <Comments
          id={id as string}
          relationTo={relationTo as Comment['commentedOn']['relationTo']}
          queryKeys={queryKeys as string}
          staleTime={Boolean(staleTime) ? Number(staleTime) : undefined}
          gcTime={Boolean(gcTime) ? Number(gcTime) : undefined}
          listLimit={Boolean(listLimit) ? Number(listLimit) : undefined}
          nestedLimit={Boolean(nestedLimit) ? Number(nestedLimit) : undefined}
          commentId={commentId as string}
          nestedCommentId={nestedCommentId as string}
          forceOpenNestedComments={true}
          showTotalComments={false}
          showInput={true}
          showLoadmoreNestedComments={false}
        />
      </View>
    </SafeAreaView>
  )
}
