import { API_ENDPOINTS } from '@/constants/endpoint.constant'
import { httpService } from '@/services/http/http.service'
import { PaginatedDocs } from '@/types/global.type'
import { Params } from '@/types/http.type'
import { SearchTip } from '@/types/search-tip.type'
import { AxiosRequestConfig } from 'axios'

class SearchTipService {
  private static instance: SearchTipService

  private constructor() {}

  public static getInstance(): SearchTipService {
    if (!SearchTipService.instance) {
      SearchTipService.instance = new SearchTipService()
    }
    return SearchTipService.instance
  }

  /**
   * Fetch random search tips using the HTTP service.
   *
   * @param params - Optional query parameters for filtering or paginating search tips.
   * @param options - Optional fetch options such as headers, method, etc.
   * @returns A promise resolving to PaginatedDocs<SearchTip> or null in case of an error.
   */
  public async getRandomSearchTips({
    params = {},
    options = {},
  }: {
    params?: Params
    options?: AxiosRequestConfig
  } = {}): Promise<PaginatedDocs<SearchTip> | null> {
    const data = await httpService.get<PaginatedDocs<SearchTip>>(
      `/${API_ENDPOINTS.search_tips_api}/random-search-tips`,
      { params, ...options },
    )
    return data
  }
}

export const searchTipService = SearchTipService.getInstance()
