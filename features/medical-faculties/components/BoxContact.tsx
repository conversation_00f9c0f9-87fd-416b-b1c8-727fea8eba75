import { Text } from "@/components/ui/Text/Text"
import { <PERSON> } from "expo-router"
import { useTranslation } from "react-i18next"
import { View } from "react-native"

import PhoneIcon from '@/assets/icons/phone-calling.svg'

export const BoxContact = () => {

    const { t } = useTranslation()

    return <View style={{
        backgroundColor: "#518EF0"
    }} className="flex flex-row gap-3 p-4 justify-between items-center rounded-lg">
        <Text size="body6" variant="white">
            {t('MES-633')}
        </Text>

        <Link href="https://www.facebook.com/share/16b1uqLbD6/?mibextid=wwXIfr" rel="noreferrer" target="_blank">
            <View className="flex flex-row gap-2 items-center px-3 py-2 bg-white rounded-lg">
                <PhoneIcon className="size-4" />

                <Text size="button5" variant="primary">
                    {t('MES-634')}
                </Text>
            </View>
        </Link>
    </View>
}