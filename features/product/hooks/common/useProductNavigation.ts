import { useEffect } from 'react'
import { BackHandler } from 'react-native'
import { useShallow } from 'zustand/react/shallow'
import { ProductV2TypeEnum } from '../../enums'
import { useProductStore } from '../../stores/ProductStore'
import { ProductCategory } from '../../types'

export const useProductNavigation = (productType: ProductV2TypeEnum) => {
  const {
    productTabState,
    setSelectedCategory,
    goBackToPreviousCategory,
    goBackToRoot,
    navigateToCategoryInHistory,
    setNestedCategories,
  } = useProductStore(
    useShallow((state) => ({
      productTabState: state.productTabState,
      setSelectedCategory: state.setSelectedCategory,
      goBackToPreviousCategory: state.goBackToPreviousCategory,
      goBackToRoot: state.goBackToRoot,
      navigateToCategoryInHistory: state.navigateToCategoryInHistory,
      setNestedCategories: state.setNestedCategories,
    })),
  )

  const currentState = productTabState[productType]

  // Handle hardware back button
  useEffect(() => {
    const backHandler = BackHandler.addEventListener('hardwareBackPress', () => {
      if (currentState.categoryHistory.length > 1) {
        goBackToPreviousCategory(productType)
        return true // Prevent default back behavior
      } else if (currentState.categoryHistory.length === 1) {
        goBackToRoot(productType)
        return true // Prevent default back behavior
      }
      return false // Allow default back behavior
    })

    return () => backHandler.remove()
  }, [currentState.categoryHistory.length, productType])

  return {
    // Current state
    selectedCategory: currentState.selectedCategory,
    selectedCategoryData: currentState.selectedCategoryData,
    categoryHistory: currentState.categoryHistory,
    nestedCategories: currentState.nestedCategories,
    isRoot: currentState.isRoot,

    // Navigation methods
    selectCategory: (category: ProductCategory) => setSelectedCategory(productType, category),
    goBack: () => goBackToPreviousCategory(productType),
    goToRoot: () => goBackToRoot(productType),
    navigateToCategory: (categoryId: string) =>
      navigateToCategoryInHistory(productType, categoryId),

    // Nested categories management
    setNestedCategories: (categories: ProductCategory[] | null) =>
      setNestedCategories(productType, categories),

    // Utility methods
    canGoBack: currentState.categoryHistory.length > 1,
    canGoToRoot: currentState.categoryHistory.length > 0,
    getCurrentCategoryTitle: () => currentState.selectedCategoryData?.title || '',
    getBreadcrumbPath: () => currentState.categoryHistory.map((cat) => cat.title).join(' > '),

    // Advanced navigation
    goBackToLevel: (level: number) => {
      if (level >= 0 && level < currentState.categoryHistory.length) {
        const targetCategory = currentState.categoryHistory[level]
        navigateToCategoryInHistory(productType, targetCategory.id)
      }
    },

    // Check if we're at a specific level
    isAtLevel: (level: number) => currentState.categoryHistory.length === level + 1,

    // Get category at specific level
    getCategoryAtLevel: (level: number) => {
      if (level >= 0 && level < currentState.categoryHistory.length) {
        return currentState.categoryHistory[level]
      }
      return null
    },
  }
}
