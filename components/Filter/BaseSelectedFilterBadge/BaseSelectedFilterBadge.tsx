import CloseIcon from '@/assets/icons/close-icon.svg'
import { Text } from '@/components/ui/Text/Text'
import { TouchableOpacity } from 'react-native'

interface BaseSelectedFilterBadgeProps<T> {
  filterItem: T
  onClearFilter?: (item: T) => void
  labelKey: keyof T
}

export const BaseSelectedFilterBadge = <T,>({
  filterItem,
  onClearFilter,
  labelKey,
}: BaseSelectedFilterBadgeProps<T>) => {
  const getFilterLabel = (item: T): string => {
    return item[labelKey] as string
  }

  return (
    <TouchableOpacity
      onPress={() => onClearFilter?.(filterItem)}
      className="flex flex-row items-center gap-x-1 self-start rounded border border-primary p-1"
    >
      <Text size="body7" variant="primary">
        {getFilterLabel(filterItem)}
      </Text>
      <CloseIcon width={18} height={18} />
    </TouchableOpacity>
  )
}
