import { API_ENDPOINTS } from '@/constants/endpoint.constant'
import { httpService } from '@/services/http/http.service'
import { PaginatedDocs } from '@/types/global.type'
import { Params } from '@/types/http.type'
import { DietarySupplementCategory } from '@/types/product.type'
import { AxiosRequestConfig } from 'axios'

// SERVER / CLIENT
class DietarySupplementService {
  private static instance: DietarySupplementService

  private constructor() {}

  public static getInstance(): DietarySupplementService {
    if (!DietarySupplementService.instance) {
      DietarySupplementService.instance = new DietarySupplementService()
    }
    return DietarySupplementService.instance
  }

  /**
   * Get supplement categories using the HTTP service
   *
   * @param params - Optional query parameters for filtering or paginating dietary supplements.
   * @param options - Optional fetch options such as headers, method, etc.
   * @returns A promise resolving to PaginatedDocs<DietarySupplementCategory> or null in case of an error.
   */
  public async getDietarySupplementCategories({
    params = {},
    options = {},
  }: {
    params?: Params
    options?: AxiosRequestConfig
  } = {}): Promise<PaginatedDocs<DietarySupplementCategory> | null> {
    const data = await httpService.get<PaginatedDocs<DietarySupplementCategory>>(
      `/${API_ENDPOINTS.supplement_category_api}`,
      {
        params,
        ...options,
      },
    )
    return data
  }

  /**
   * Get supplement category using the HTTP service
   *
   * @param id - Supplement category id.
   * @param params - Optional query parameters for filtering or paginating dietary supplements.
   * @param options - Optional fetch options such as headers, method, etc.
   * @returns A promise resolving to SupplementCategory or null in case of an error.
   */

  public async getDietarySupplementCategory({
    id,
    params = {},
    options = {},
  }: {
    id: string
    params?: Params
    options?: AxiosRequestConfig
  }): Promise<DietarySupplementCategory | null> {
    const data = await httpService.get<DietarySupplementCategory>(
      `/${API_ENDPOINTS.supplement_category_api}/${id}`,
      {
        params,
        ...options,
      },
    )
    return data
  }
}

export const dietarySupplementService = DietarySupplementService.getInstance()
