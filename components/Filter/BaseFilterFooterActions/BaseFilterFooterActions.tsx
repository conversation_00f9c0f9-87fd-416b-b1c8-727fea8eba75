import { Button } from '@/components/ui/Button/Button'
import { Text } from '@/components/ui/Text/Text'
import { useTranslation } from 'react-i18next'
import { View } from 'react-native'

interface BaseFilterFooterActionsProps {
  onApply: () => void
  onReset: () => void
  applyText?: string
  resetText?: string
  applyButtonProps?: {
    variant?: 'solid' | 'outline' | 'link'
    size?: 'sm' | 'md' | 'lg'
    className?: string
  }
  resetButtonProps?: {
    variant?: 'solid' | 'outline' | 'link'
    size?: 'sm' | 'md' | 'lg'
    className?: string
  }
  containerClassName?: string
}

export const BaseFilterFooterActions = ({
  onApply,
  onReset,
  applyText,
  resetText,
  applyButtonProps = { variant: 'solid', size: 'md' },
  resetButtonProps = { variant: 'outline', size: 'md' },
  containerClassName = 'flex flex-row items-center justify-between gap-x-2',
}: BaseFilterFooterActionsProps) => {
  const { t } = useTranslation()

  return (
    <View className={containerClassName}>
      <Button
        size={resetButtonProps.size}
        variant={resetButtonProps.variant}
        className={`!h-10 w-full flex-1 border-custom-text-disabled ${resetButtonProps.className || ''}`}
        onPress={onReset}
      >
        <Text size="button3" variant="primary">
          {resetText || t('MES-105')}
        </Text>
      </Button>
      <Button
        size={applyButtonProps.size}
        variant={applyButtonProps.variant}
        className={`!h-10 w-full flex-1 ${applyButtonProps.className || ''}`}
        onPress={onApply}
      >
        <Text size="button3" variant="white">
          {applyText || t('MES-281')}
        </Text>
      </Button>
    </View>
  )
}
