import { Text } from '@/components/ui/Text/Text'
import { useSheet } from '@/contexts/SheetContext/SheetContext'
import { useTranslation } from 'react-i18next'
import { TouchableOpacity, View } from 'react-native'
import { useSafeAreaInsets } from 'react-native-safe-area-context'
import Svg, { Path } from 'react-native-svg'
interface CommentActionSheetProps {
  onPressEdit?: () => void
  onPressDelete?: () => void
  close?: () => void
}
export const CommentActionSheet = ({ onPressEdit, onPressDelete }: CommentActionSheetProps) => {
  const { t } = useTranslation()
  const insets = useSafeAreaInsets()
  return (
    <View
      className="flex flex-col items-center justify-center  "
      style={{ paddingBottom: insets.bottom }}
    >
      <View className="flex w-full items-center justify-center border-b border-custom-divider ">
        <TouchableOpacity
          onPress={onPressEdit}
          hitSlop={10}
          className="flex w-full flex-row items-center justify-center gap-x-2 px-3 py-3"
        >
          <Svg width={18} height={18} viewBox="0 0 18 18" fill="none">
            <Path
              d="M9.75 2.25H6.75C3 2.25 1.5 3.75 1.5 7.5V11.25C1.5 15 3 16.5 6.75 16.5H10.5C14.25 16.5 15.75 15 15.75 11.25V8.25"
              stroke="#8B8C99"
              strokeWidth={1.5}
              strokeLinecap="round"
              strokeLinejoin="round"
            />
            <Path
              d="M11.5275 3.3825L6.1425 8.7675C5.955 8.955 5.7675 9.3225 5.73 9.585L5.4225 11.655C5.31 12.4275 5.865 12.9825 6.6375 12.87L8.7075 12.5625C8.97 12.5325 9.3375 12.345 9.525 12.1575L14.91 6.7725C15.75 5.9325 16.1475 4.965 14.91 3.7275C13.6725 2.49 12.3675 2.5425 11.5275 3.3825Z"
              stroke="#8B8C99"
              strokeWidth={1.5}
              strokeMiterlimit={10}
              strokeLinecap="round"
              strokeLinejoin="round"
            />
            <Path
              d="M10.8984 4.01172C11.3434 5.61672 12.5859 6.85922 14.1984 7.31172"
              stroke="#8B8C99"
              strokeWidth={1.5}
              strokeMiterlimit={10}
              strokeLinecap="round"
              strokeLinejoin="round"
            />
          </Svg>
          <Text size="body6" variant="default">
            {t('MES-877')}
          </Text>
        </TouchableOpacity>
      </View>
      <View className="flex w-full items-center justify-center  ">
        <TouchableOpacity
          onPress={onPressDelete}
          hitSlop={10}
          className="flex w-full flex-row items-center justify-center gap-x-2 px-3 py-3"
        >
          <Svg width={18} height={18} viewBox="0 0 18 18" fill="none">
            <Path
              d="M15.75 4.48438C13.2525 4.23688 10.74 4.10938 8.235 4.10938C6.75 4.10938 5.265 4.18438 3.78 4.33438L2.25 4.48438"
              stroke="#EF4444"
              strokeWidth={1.5}
              strokeLinecap="round"
              strokeLinejoin="round"
            />
            <Path
              d="M6.375 3.7275L6.54 2.745C6.66 2.0325 6.75 1.5 8.0175 1.5H9.9825C11.25 1.5 11.3475 2.0625 11.46 2.7525L11.625 3.7275"
              stroke="#EF4444"
              strokeWidth={1.5}
              strokeLinecap="round"
              strokeLinejoin="round"
            />
            <Path
              d="M14.1375 6.85498L13.65 14.4075C13.5675 15.585 13.5 16.5 11.4075 16.5H6.5925C4.5 16.5 4.4325 15.585 4.35 14.4075L3.8625 6.85498"
              stroke="#EF4444"
              strokeWidth={1.5}
              strokeLinecap="round"
              strokeLinejoin="round"
            />
            <Path
              d="M7.74219 12.375H10.2497"
              stroke="#EF4444"
              strokeWidth={1.5}
              strokeLinecap="round"
              strokeLinejoin="round"
            />
            <Path
              d="M7.125 9.375H10.875"
              stroke="#EF4444"
              strokeWidth={1.5}
              strokeLinecap="round"
              strokeLinejoin="round"
            />
          </Svg>
          <Text size="body6" variant="default">
            {t('MES-878')}
          </Text>
        </TouchableOpacity>
      </View>
    </View>
  )
}
export const useOpenCommentActionSheet = () => {
  const { openSheet, closeSheet } = useSheet()
  const handleOpenCommentActionSheet = ({
    onPressEdit,
    onPressDelete,
  }: {
    onPressEdit?: () => void
    onPressDelete?: () => void
  }) => {
    openSheet({
      children: ({ close }) => (
        <CommentActionSheet onPressEdit={onPressEdit} onPressDelete={onPressDelete} close={close} />
      ),
      options: {
        type: 'basic',
        enableDynamicSizing: true,
        // snapPoints: ['25%'],
        enableOverDrag: false,
        enableContentPanningGesture: false,
        enableHandlePanningGesture: false,
      },
    })
  }
  const handleCloseCommentActionSheet = () => {
    closeSheet()
  }
  return { handleOpenCommentActionSheet, handleCloseCommentActionSheet }
}
