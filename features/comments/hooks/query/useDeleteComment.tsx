import { MutationOptions, useMutation } from '@tanstack/react-query'
import { useRef } from 'react'
import { commentService } from '../../services/comment.service'
import { commentMutationKeys } from './queryKeys'

type DeleteCommentVariables = { commentId: string }

export const useDeleteComment = ({
  options,
}: {
  options?: Omit<MutationOptions | undefined, 'mutationFn' | 'mutationKey'>
} = {}) => {
  // Define an AbortController to cancel previous requests
  const abortControllerRef = useRef<AbortController | null>(null)

  const {
    isError: isDeleteCommentError,
    isPending: isDeleteCommentPending,
    mutate: deleteCommentMutation,
    isSuccess: isDeleteCommentSuccess,
    ...rest
  } = useMutation({
    mutationKey: commentMutationKeys['deleteComment'].base(),
    mutationFn: async ({ commentId }: DeleteCommentVariables) => {
      // Abort any ongoing request before initiating a new one
      if (abortControllerRef.current) {
        abortControllerRef.current.abort()
      }

      // Create a new AbortController for the new request
      const abortController = new AbortController()
      abortControllerRef.current = abortController

      // Pass the signal from AbortController to the service method
      return commentService.deleteComment(commentId, {
        signal: abortControllerRef.current.signal,
      })
    },
    ...options,
  })

  return {
    isDeleteCommentError,
    isDeleteCommentPending,
    deleteCommentMutation,
    isDeleteCommentSuccess,
    ...rest,
  }
}
