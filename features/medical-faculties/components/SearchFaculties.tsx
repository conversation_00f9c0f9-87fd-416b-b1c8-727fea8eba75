import SearchInputIcon from '@/assets/icons/search-input-icon.svg'
import { Text } from "@/components/ui/Text/Text"
import { useTranslation } from "react-i18next"
import { TouchableHighlight, View } from "react-native"

export const SearchFaculties = () => {

    const { t } = useTranslation()

    const onPress = () => {

    }

    return <TouchableHighlight onPress={onPress}
        underlayColor="transparent"
        accessibilityRole="button">

        <View className="mt-4 flex flex-row border border-custom-divider rounded-lg p-3 gap-3 items-center">
            <SearchInputIcon width={18} height={18} />
            <View className="flex-1 overflow-hidden">
                <Text className="line-clamp-1" size="field1" variant="subdued">
                    {t('MES-66')}
                </Text>
            </View>
        </View>
    </TouchableHighlight>
}