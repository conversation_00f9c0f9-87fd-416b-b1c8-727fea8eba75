import { PaginatedDocs } from '@/types/global.type'
import { Params } from '@/types/http.type'
import { InfiniteData, UseInfiniteQueryOptions, useInfiniteQuery } from '@tanstack/react-query'
import { AxiosRequestConfig } from 'axios'
import { commentService } from '../../services/comment.service'
import { Comment } from '../../types'
import { commentQueryKeys } from './queryKeys'

export type CommentsQueryConfig = Partial<
  Omit<
    UseInfiniteQueryOptions<PaginatedDocs<Comment>, Error, InfiniteData<PaginatedDocs<Comment>>>,
    'queryFn' | 'queryKey' | 'getNextPageParam' | 'initialPageParam'
  >
>

interface UseGetInfiniteCommentsProps {
  id: string
  params?: Params
  options?: AxiosRequestConfig
  key?: string | number
  config?: CommentsQueryConfig
  overrideKey?: (string | Params)[]
}

export const useGetInfiniteComments = ({
  id,
  params = {},
  options = {},
  config = {},
  overrideKey,
}: UseGetInfiniteCommentsProps) => {
  const {
    isError: isGetCommentsError,
    isLoading: isGetCommentsLoading,
    isFetching: isGetCommentsFetching,
    isFetchingNextPage: isGetCommentsFetchingNextPage,
    data: comments,
    fetchNextPage,
    hasNextPage,
    ...rest
  } = useInfiniteQuery({
    queryKey: overrideKey ? overrideKey : [commentQueryKeys['comments'].base(), id, params],
    queryFn: async ({ pageParam }) => {
      const result = await commentService.getComments({
        id,
        params: {
          ...params,
          cursor: pageParam, // use cursor instead of page
        },
        options,
      })
      return result
    },
    getNextPageParam: (lastPage) => lastPage?.nextCursor || undefined,
    getPreviousPageParam: () => undefined,
    initialPageParam: undefined,

    ...config,
  })

  return {
    isGetCommentsError,
    isGetCommentsLoading,
    isGetCommentsFetching,
    isGetCommentsFetchingNextPage,
    comments,
    fetchNextPage,
    hasNextPage,
    ...rest,
  }
}
