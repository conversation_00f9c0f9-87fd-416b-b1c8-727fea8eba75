import { Post } from '@/features/post/types'
import { Product } from '@/features/product/types'
import { Keyword } from '@/types/keyword.type'

import { User } from '@/types/user.type'
import { CommentReportReasonsEnum } from '../enums'

export interface Comment {
  id: string
  commentedOn:
    | {
        relationTo: 'posts'
        value: string | Post
      }
    | {
        relationTo: 'products'
        value: string | Product
      }
    | {
        relationTo: 'keywords'
        value: string | Keyword
      }
  author: string | User
  content: string
  totalLikes?: number | null
  parent?: (string | null) | Comment
  replies?: {
    docs?: (string | Comment)[]
    hasNextPage?: boolean
    totalDocs?: number
  }
  likes?: {
    docs?: (string | CommentLike)[]
    hasNextPage?: boolean
    totalDocs?: number
  }
  updatedAt: string
  createdAt: string
  isLiked?: boolean | null
  // Optimistic update states
  isOptimistic?: boolean
  isLoading?: boolean
  hasError?: boolean
  isAdmin?: boolean
}

export interface CommentLike {
  id: string
  comment: string | Comment
  commentedOn:
    | {
        relationTo: 'posts'
        value: string | Post
      }
    | {
        relationTo: 'products'
        value: string | Product
      }
    | {
        relationTo: 'keywords'
        value: string | Keyword
      }
  user: string | User
  updatedAt: string
  createdAt: string
}

export interface AddCommentPayload {
  content: string
  parentId?: string
}

export interface AddCommentResponse {
  message: string
  comment: Comment
}

export interface LikeCommentPayload {
  commentId: string
}

export interface UnlikeCommentPayload {
  commentId: string
}

export interface ReactionCommentResponse {
  message: string
}

export interface DeleteCommentResponse {
  message: string
  commentId: string
  totalCommentsDeleted: number
  // nestedRepliesDeleted: number
}
export interface EditCommentPayload {
  content: string
}
export interface EditCommentResponse {
  message: string
  comment: Comment
}

export interface TotalCommentsResponse {
  totalComments: number
}

export interface ReportCommentPayload {
  reason: CommentReportReasonsEnum
  description?: string | null
}

export interface ReportCommentResponse {
  message: string
}
