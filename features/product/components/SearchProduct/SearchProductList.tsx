import CloseIconDanger from '@/assets/icons/close-icon-danger.svg'
import EmptyBoxIcon from '@/assets/icons/empty-box.svg'
import { Skeleton } from '@/components/ui/Skeleton/Skeleton'
import { Text } from '@/components/ui/Text/Text'
import { Product } from '@/features/product/types'
import { useAppLanguage } from '@/hooks/common/useAppLanguage'
import colors from '@/styles/_colors'
import React, { useCallback, useEffect, useMemo, useState } from 'react'
import { useTranslation } from 'react-i18next'
import { ActivityIndicator, FlatList, RefreshControl, TouchableOpacity, View } from 'react-native'
import { useShallow } from 'zustand/react/shallow'
import { ProductV2TypeEnum } from '../../enums'
import { useGetInfiniteFilteredProducts } from '../../hooks/query/product/useGetInfiniteFilteredProducts'
import {
  SearchProductFilterItem,
  SearchProductFilterType,
  useProductStore,
} from '../../stores/ProductStore'
import { SelectedFilterBadge } from '../FilterProductBox/SelectedFilterBadge'
import { ProductItem } from '../ProductList/ProductItem'
type ListItem =
  | { type: 'product_row'; products: Product[] }
  | { type: 'loading' }
  | { type: 'loading_skeleton' }

export const SearchProductList = () => {
  const { primaryLanguage } = useAppLanguage()
  const { t } = useTranslation()
  const [_, setHeaderHeight] = useState(0)
  const [refreshing, setRefreshing] = useState(false)
  const {
    searchTextValue,
    searchProductFilters,
    toggleSearchProductFilter,
    clearAllSearchFiltersMedicineType,
    hasActiveFilters,
    clearAllSearchProductFilters,
  } = useProductStore(
    useShallow((state) => ({
      searchTextValue: state.searchProductFilters['search_text'],
      searchProductFilters: state.searchProductFilters,
      toggleSearchProductFilter: state.toggleSearchProductFilter,
      clearAllSearchFiltersMedicineType: state.clearAllSearchFiltersMedicineType,
      hasActiveFilters: state.hasActiveFilters,
      clearAllSearchProductFilters: state.clearAllSearchProductFilters,
    })),
  )

  const shouldApplyMedicineTypeFilter = useMemo(() => {
    const isFilterProductTypeContainMedicineType = searchProductFilters?.[
      SearchProductFilterType.PRODUCT_TYPE
    ]?.some((filter) => filter.id === ProductV2TypeEnum.MEDICINE)
    const isFilterProductTypeEmpty =
      !searchProductFilters?.[SearchProductFilterType.PRODUCT_TYPE] ||
      searchProductFilters?.[SearchProductFilterType.PRODUCT_TYPE]?.length === 0
    const isContainMedicineType = searchProductFilters?.[
      SearchProductFilterType.MEDICINE_TYPE
    ]?.some((filter) => filter.id === ProductV2TypeEnum.MEDICINE)
    return (
      isFilterProductTypeContainMedicineType || isFilterProductTypeEmpty || isContainMedicineType
    )
  }, [searchProductFilters])

  const {
    filteredProducts,
    isGetFilteredProductsLoading,
    isGetFilteredProductsError,
    fetchNextPage,
    hasNextPage,
    isGetFilteredProductsFetchingNextPage,
    isRefetching,
    refetch,
  } = useGetInfiniteFilteredProducts({
    config: {
      staleTime: 5 * 60 * 1000,
      enabled: Boolean(hasActiveFilters || (searchTextValue && searchTextValue.length > 0)),
    },
    params: {
      locale: primaryLanguage,
      limit: 12,
      categories:
        searchProductFilters[SearchProductFilterType.CATEGORY]?.map((filter) => filter.id) ||
        undefined,
      where: {
        and: [
          {
            or: [
              {
                title: {
                  like: searchTextValue ? searchTextValue : undefined,
                },
              },
              {
                'keywords.name': {
                  like: searchTextValue ? searchTextValue : undefined,
                },
              },
              {
                jaTitle: {
                  like: searchTextValue ? searchTextValue : undefined,
                },
              },
            ],
          },
          shouldApplyMedicineTypeFilter
            ? {
                or: [
                  // Medicine products with medicine type filter
                  {
                    and: [
                      {
                        type: {
                          in: [ProductV2TypeEnum.MEDICINE],
                        },
                      },
                      {
                        ageGroups: {
                          in:
                            searchProductFilters[SearchProductFilterType.AGE_GROUP]?.map(
                              (filter) => filter.id,
                            ) || undefined,
                        },
                      },
                      {
                        medicineType: {
                          in:
                            searchProductFilters[SearchProductFilterType.MEDICINE_TYPE]?.map(
                              (filter) => filter.id,
                            ) || undefined,
                        },
                      },
                    ],
                  },
                  // Other product types without medicine type filter
                  {
                    and: [
                      {
                        ageGroups: {
                          in:
                            searchProductFilters[SearchProductFilterType.AGE_GROUP]?.map(
                              (filter) => filter.id,
                            ) || undefined,
                        },
                      },
                      {
                        type: {
                          in:
                            searchProductFilters[SearchProductFilterType.PRODUCT_TYPE] &&
                            searchProductFilters[SearchProductFilterType.PRODUCT_TYPE].length > 0
                              ? searchProductFilters[SearchProductFilterType.PRODUCT_TYPE]
                                  ?.filter((filter) => filter.id !== ProductV2TypeEnum.MEDICINE)
                                  ?.map((filter) => filter.id)
                              : [
                                  ProductV2TypeEnum.DIETARY_SUPPLEMENT,
                                  ProductV2TypeEnum.MEDICAL_INSTRUMENT,
                                ],
                        },
                      },
                    ],
                  },
                ],
              }
            : {
                and: [
                  {
                    ageGroups: {
                      in:
                        searchProductFilters[SearchProductFilterType.AGE_GROUP]?.map(
                          (filter) => filter.id,
                        ) || undefined,
                    },
                  },
                  {
                    type: {
                      in: searchProductFilters[SearchProductFilterType.PRODUCT_TYPE]
                        ? searchProductFilters[SearchProductFilterType.PRODUCT_TYPE]?.map(
                            (filter) => filter.id,
                          ) || undefined
                        : undefined,
                    },
                  },
                ],
              },
        ],
      },
    },
  })

  // Handle pull-to-refresh
  const handleRefresh = useCallback(async () => {
    setRefreshing(true)
    try {
      await refetch()
    } finally {
      setRefreshing(false)
    }
  }, [refetch])

  // Flatten all products from all pages
  const allProducts = useMemo(() => {
    if (!filteredProducts?.pages) return []
    return filteredProducts.pages.flatMap((page) => page?.docs || [])
  }, [filteredProducts])

  // Group products into pairs for 2-column layout
  const productRows = useMemo(() => {
    const rows: Product[][] = []
    for (let i = 0; i < allProducts.length; i += 2) {
      rows.push(allProducts.slice(i, i + 2))
    }
    return rows
  }, [allProducts])

  const data = useMemo((): ListItem[] => {
    const items: ListItem[] = []

    // Show loading skeletons if initial loading or refreshing
    if (isGetFilteredProductsLoading || isRefetching || refreshing) {
      items.push({ type: 'loading_skeleton' })
    } else {
      // Products in rows (2 columns)
      productRows.forEach((row) => {
        items.push({ type: 'product_row', products: row })
      })

      // Loading indicator for pagination
      if (isGetFilteredProductsFetchingNextPage) {
        items.push({ type: 'loading' })
      }
    }

    return items
  }, [
    allProducts,
    productRows,
    isGetFilteredProductsFetchingNextPage,
    isGetFilteredProductsLoading,
    isRefetching,
    refreshing,
  ])

  const handleLoadMore = useCallback(() => {
    if (hasNextPage && !isGetFilteredProductsFetchingNextPage) {
      fetchNextPage()
    }
  }, [hasNextPage, isGetFilteredProductsFetchingNextPage, fetchNextPage])

  const handleHeaderLayout = useCallback((event: any) => {
    const { height } = event.nativeEvent.layout
    setHeaderHeight(height)
  }, [])

  const totalProducts = useMemo(() => {
    if (isGetFilteredProductsLoading) {
      return 0
    }
    const total = filteredProducts?.pages[0]?.totalDocs || 0

    return total > 99 ? '99+' : total
  }, [filteredProducts, isGetFilteredProductsLoading])

  const renderItem = useCallback(({ item }: { item: ListItem }) => {
    switch (item.type) {
      case 'product_row':
        return (
          <View className="mb-3 flex w-full">
            <View className="flex flex-row gap-3">
              {item.products.map((product) => (
                <ProductItem key={product.id} product={product} />
              ))}
              {/* Fill empty space if odd number of products */}
              {item.products.length === 1 && <View className="flex-1" />}
            </View>
          </View>
        )

      case 'loading':
        return (
          <View className="items-center py-2">
            <ActivityIndicator size="small" />
          </View>
        )

      case 'loading_skeleton':
        return (
          <View className="flex flex-1 flex-col gap-3" style={{ gap: 20 }}>
            <View className="flex flex-row gap-3 ">
              {[...Array.from({ length: 2 }).fill(1)].map((_, index) => (
                <View key={index} className="flex aspect-square  w-1/2 flex-1 flex-col gap-y-2 ">
                  <Skeleton className=" flex-1 rounded-lg bg-gray-200" />
                  <Skeleton className="h-4 w-2/3 rounded-lg bg-gray-200" />
                </View>
              ))}
            </View>
            <View className="flex flex-row gap-3 ">
              {[...Array.from({ length: 2 }).fill(1)].map((_, index) => (
                <View key={index} className="flex aspect-square  w-1/2 flex-1 flex-col gap-y-2 ">
                  <Skeleton className=" flex-1 rounded-lg bg-gray-200" />
                  <Skeleton className="h-4 w-2/3 rounded-lg bg-gray-200" />
                </View>
              ))}
            </View>
            <View className="flex flex-row gap-3 ">
              {[...Array.from({ length: 2 }).fill(1)].map((_, index) => (
                <View key={index} className="flex aspect-square  w-1/2 flex-1 flex-col gap-y-2 ">
                  <Skeleton className=" flex-1 rounded-lg bg-gray-200" />
                  <Skeleton className="h-4 w-2/3 rounded-lg bg-gray-200" />
                </View>
              ))}
            </View>
          </View>
        )

      default:
        return null
    }
  }, [])

  const renderHeaderComponent = useCallback(() => {
    const appliedFilters = Object.entries(searchProductFilters)
      .filter(
        ([key, value]) =>
          key !== SearchProductFilterType.SEARCH_TEXT &&
          value &&
          Array.isArray(value) &&
          value.length > 0,
      )
      .flatMap(([_, value]) => value as SearchProductFilterItem[])

    return (
      <View onLayout={handleHeaderLayout} className="mb-3 gap-y-3">
        {/* Products header */}
        <View className="flex flex-row items-center justify-between">
          <Text size="body3" className="font-medium">
            {t('MES-71')}{' '}
            {!isGetFilteredProductsLoading &&
              !isGetFilteredProductsFetchingNextPage &&
              !isRefetching &&
              `(${totalProducts})`}
          </Text>
        </View>

        {/* Active Filters - Only show real applied filters */}
        {appliedFilters.length > 0 && (
          <View className="flex flex-col gap-y-2">
            <Text size="body7" variant="subdued">
              {t('MES-706')} ({appliedFilters.length}):
            </Text>
            <View className="flex flex-row flex-wrap gap-2">
              {appliedFilters.map((filter: SearchProductFilterItem) => (
                <SelectedFilterBadge
                  key={`${filter.type}-${filter.id}`}
                  filterItem={filter}
                  onClearFilter={(filterItem) => {
                    toggleSearchProductFilter(
                      ProductV2TypeEnum.MEDICINE,
                      filterItem.type as SearchProductFilterType,
                      filterItem as SearchProductFilterItem,
                    )
                  }}
                />
              ))}
              <TouchableOpacity
                onPress={() => clearAllSearchProductFilters()}
                className="flex flex-row items-center gap-x-1"
              >
                <Text size="body8" variant="error">
                  {t('MES-709')}
                </Text>
                <View className="-ml-1">
                  <CloseIconDanger width={20} height={20} />
                </View>
              </TouchableOpacity>
            </View>
          </View>
        )}
      </View>
    )
  }, [
    handleHeaderLayout,
    refreshing,
    t,
    totalProducts,
    isGetFilteredProductsLoading,

    searchProductFilters,
    shouldApplyMedicineTypeFilter,
  ])

  const renderEmptyComponent = useCallback(() => {
    if (isGetFilteredProductsError) {
      return (
        <View className="items-center  py-8">
          <Text size="body6" className="text-red-500">
            {t('MES-197')}
          </Text>
        </View>
      )
    }

    return (
      <View className="flex flex-col items-center justify-center gap-y-2  py-8">
        <EmptyBoxIcon />
        <Text size="body6" variant="default">
          {t('MES-717')}
        </Text>
      </View>
    )
  }, [isGetFilteredProductsError])

  const keyExtractor = useCallback((item: ListItem, index: number) => {
    if (item.type === 'product_row') {
      return `product-row-${item.products.map((p) => p.id).join('-')}-${index}`
    }
    return `${item.type}-${index}`
  }, [])

  useEffect(() => {
    if (!shouldApplyMedicineTypeFilter) {
      clearAllSearchFiltersMedicineType()
    }
  }, [shouldApplyMedicineTypeFilter])

  return (
    <FlatList
      data={data}
      renderItem={renderItem}
      keyExtractor={keyExtractor}
      ListHeaderComponent={renderHeaderComponent}
      onEndReached={handleLoadMore}
      onEndReachedThreshold={0.3}
      ListEmptyComponent={renderEmptyComponent}
      showsVerticalScrollIndicator={false}
      removeClippedSubviews={false}
      maxToRenderPerBatch={10}
      windowSize={10}
      initialNumToRender={5}
      contentContainerStyle={{ paddingBottom: 60 }}
      refreshControl={
        <RefreshControl
          refreshing={refreshing}
          onRefresh={handleRefresh}
          colors={[colors.primary['500']]}
          tintColor={colors.primary['500']}
          progressBackgroundColor="#FFFFFF"
        />
      }
    />
  )
}
