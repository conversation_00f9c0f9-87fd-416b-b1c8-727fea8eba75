import WarningIcon from '@/assets/icons/warning-2.svg'
import { Button } from '@/components/ui/Button/Button'
import { Text } from '@/components/ui/Text/Text'
import { useDialog } from '@/hooks/dialog/useDialog'

import { View } from 'react-native'

interface BaseWarningPopupProps {
  close?: () => void
  title: string
  description: string
  cancelText?: string
  confirmText?: string
  onPressCancel?: (close?: () => void) => void
  onPressConfirm?: (close?: () => void) => void
  confirmVariant?: 'solid' | 'outline'
  confirmAction?: 'negative' | 'positive' | 'primary'
}

export const BaseWarningPopup = ({
  close,
  title,
  description,
  cancelText = 'Cancel',
  confirmText = 'Confirm',
  onPressCancel,
  onPressConfirm,
  confirmVariant = 'solid',
  confirmAction = 'negative',
}: BaseWarningPopupProps) => {
  return (
    <View className="flex flex-col items-center justify-center gap-y-3 rounded-xl bg-white">
      <WarningIcon width={30} height={30} />
      <View className="flex flex-col items-center justify-center gap-y-2">
        <Text size="body3" className="text-center">
          {title}
        </Text>
        <Text size="body7" className="text-center">
          {description}
        </Text>
      </View>
      <View className="mt-6 flex w-full flex-col gap-y-2">
        <Button
          className="w-full flex-1 rounded-lg"
          variant={confirmVariant}
          action={confirmAction}
          onPress={() => onPressConfirm?.(close)}
        >
          <Text size="button3" variant="white">
            {confirmText}
          </Text>
        </Button>
        <Button
          variant="outline"
          className="w-full flex-1 rounded-lg border-custom-text-disabled"
          onPress={() => onPressCancel?.(close) || close?.()}
        >
          <Text size="button3" variant="primary">
            {cancelText}
          </Text>
        </Button>
      </View>
    </View>
  )
}

export const useOpenBaseWarningPopup = () => {
  const { openDialog } = useDialog()

  const openBaseWarningPopup = ({
    title,
    description,
    cancelText,
    confirmText,
    onPressCancel,
    onPressConfirm,
    confirmVariant = 'solid',
    confirmAction = 'negative',
  }: Omit<BaseWarningPopupProps, 'close'>) => {
    openDialog({
      children: ({ close }: { close: () => void }) => {
        return (
          <BaseWarningPopup
            close={close}
            title={title}
            description={description}
            cancelText={cancelText}
            confirmText={confirmText}
            onPressCancel={onPressCancel}
            onPressConfirm={onPressConfirm}
            confirmVariant={confirmVariant}
            confirmAction={confirmAction}
          />
        )
      },
      variant: 'blank',
      contentSize: 'md',
      bodyClassName: 'p-0 m-0',
    })
  }

  return { openBaseWarningPopup }
}
