import EmptyBoxIcon from '@/assets/icons/empty-box.svg'
import SearchInputIcon from '@/assets/icons/search-input-icon.svg'
import { Skeleton } from '@/components/ui/Skeleton/Skeleton'
import { Text } from '@/components/ui/Text/Text'
import { useAppLanguage } from '@/hooks/common/useAppLanguage'
import { APP_ROUTES, AppRoutesEnum } from '@/routes/appRoutes'
import colors from '@/styles/_colors'
import { Link, LinkProps } from 'expo-router'
import { useCallback, useMemo, useState } from 'react'
import { useTranslation } from 'react-i18next'
import { ActivityIndicator, FlatList, RefreshControl, TouchableOpacity, View } from 'react-native'
import { useGetInfiniteHospitalPartners } from '../../hooks/query/useGetInfinteHospitaPartners'
import { Hospital } from '../../types'
import { HospitalPartnersItem } from './HospitalPartnersItem'
type ListItem =
  | { type: 'hospital_partner'; hospital: Hospital }
  | { type: 'loading' }
  | { type: 'loading_skeleton' }

export const HospitalPartners = () => {
  const { t } = useTranslation()
  const { primaryLanguage } = useAppLanguage()

  const [searchQuery, setSearchQuery] = useState('')
  const [refreshing, setRefreshing] = useState(false)

  const params = useMemo(() => {
    return {
      limit: 10,
      locale: primaryLanguage,
      select: {
        id: true,
        name: true,
        logo: true,
        japaneseName: true,
        address: true,
        bannerImage: true,
      },
    }
  }, [primaryLanguage, searchQuery])

  const {
    hospitalPartners,
    isGetHospitalPartnersLoading,
    isGetHospitalPartnersError,
    isFetchingNextPage: isGetHospitalPartnersFetchingNextPage,
    fetchNextPage,
    hasNextPage,
    refetch,
  } = useGetInfiniteHospitalPartners({
    params,
    config: {
      staleTime: 0,
    },
  })

  const allHospitalPartners = useMemo(() => {
    if (!hospitalPartners?.pages) return []
    return hospitalPartners.pages.flatMap((page) => page?.docs || [])
  }, [hospitalPartners])

  // Handle pull-to-refresh
  const handleRefresh = useCallback(async () => {
    setRefreshing(true)
    try {
      await refetch()
    } finally {
      setRefreshing(false)
    }
  }, [refetch])

  // Handle load more
  const handleLoadMore = useCallback(() => {
    if (hasNextPage && !isGetHospitalPartnersFetchingNextPage) {
      fetchNextPage()
    }
  }, [hasNextPage, isGetHospitalPartnersFetchingNextPage, fetchNextPage])

  // Build data array with different ListItem types
  const data = useMemo((): ListItem[] => {
    const items: ListItem[] = []

    // Show loading skeletons during initial load or refresh (but not during pagination)
    const isShowingLoadingSkeleton =
      (isGetHospitalPartnersLoading || refreshing) && !isGetHospitalPartnersFetchingNextPage

    if (isShowingLoadingSkeleton) {
      items.push({ type: 'loading_skeleton' })
    } else {
      // Add each hospital as a separate item
      allHospitalPartners.forEach((hospital) => {
        items.push({ type: 'hospital_partner', hospital })
      })

      // Loading indicator for pagination
      if (isGetHospitalPartnersFetchingNextPage) {
        items.push({ type: 'loading' })
      }
    }

    return items
  }, [
    allHospitalPartners,
    isGetHospitalPartnersFetchingNextPage,
    isGetHospitalPartnersLoading,
    refreshing,
  ])

  // Render item based on ListItem type
  const renderItem = useCallback(({ item }: { item: ListItem }) => {
    switch (item.type) {
      case 'hospital_partner':
        return (
          <View className="mb-1 flex w-full">
            <HospitalPartnersItem hospital={item.hospital} />
          </View>
        )

      case 'loading':
        return (
          <View className="items-center py-4">
            <ActivityIndicator size="small" />
          </View>
        )

      case 'loading_skeleton':
        return (
          <View className="flex flex-col gap-y-4">
            {new Array(6).fill(0).map((_, rowIndex) => (
              <View key={rowIndex} className="flex flex-row gap-3">
                <View className="flex-1 flex-col gap-y-3">
                  <Skeleton className="h-48 w-full rounded-lg" />
                  <Skeleton className="h-6 w-[120px] rounded-lg" />
                  <Skeleton className="h-6 w-[240px] rounded-lg" />
                </View>
              </View>
            ))}
          </View>
        )

      default:
        return null
    }
  }, [])

  const renderEmptyComponent = useCallback(() => {
    if (isGetHospitalPartnersError) {
      return (
        <View className="items-center  py-8">
          <Text size="body6" className="text-red-500">
            {t('MES-197')}
          </Text>
        </View>
      )
    }
    if (isGetHospitalPartnersLoading || isGetHospitalPartnersFetchingNextPage) {
      return null
    }

    return (
      <View className="items-center  py-8">
        <EmptyBoxIcon />
        <Text size="body6" variant="default">
          {t('MES-803')}
        </Text>
      </View>
    )
  }, [
    isGetHospitalPartnersError,
    isGetHospitalPartnersLoading,
    isGetHospitalPartnersFetchingNextPage,
  ])

  const renderSeparator = useCallback(() => {
    return <View className="h-2" />
  }, [])

  const keyExtractor = useCallback((item: ListItem, index: number) => {
    if (item.type === 'hospital_partner') {
      return `hospital-partner-${item.hospital.id}`
    }
    return `${item.type}-${index}`
  }, [])

  return (
    <View className="flex-1 flex-col gap-y-6 bg-white ">
      {/* Header */}
      <View className="flex flex-col gap-y-3 px-4">
        <View className="flex  flex-col gap-y-3 bg-white ">
          <View className="flex flex-col gap-y-3">
            <View className="flex flex-row items-center gap-x-2">
              {/* Search Input */}
              <Link
                href={
                  APP_ROUTES.HOSPITAL_PARTNERS?.children?.[AppRoutesEnum.HOSPITAL_PARTNERS_SEARCH]
                    .path as LinkProps['href']
                }
                asChild
              >
                <TouchableOpacity
                  className="flex-1 rounded-lg border border-custom-divider p-2"
                  onPress={() => {
                    // Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light)
                  }}
                  accessibilityRole="button"
                >
                  <View className="flex flex-row gap-x-2">
                    <SearchInputIcon width={18} height={18} />
                    <View className="flex-1 ">
                      <Text className="line-clamp-1" size="field1" variant="subdued">
                        {t('MES-66')}
                      </Text>
                    </View>
                  </View>
                </TouchableOpacity>
              </Link>
            </View>
          </View>
        </View>
      </View>

      {/* List */}
      <View className="flex-1 px-4 ">
        <FlatList
          showsVerticalScrollIndicator={false}
          data={data}
          renderItem={renderItem}
          keyExtractor={keyExtractor}
          contentContainerStyle={{
            paddingBottom: 16,
          }}
          ItemSeparatorComponent={renderSeparator}
          ListEmptyComponent={renderEmptyComponent}
          onEndReached={handleLoadMore}
          onEndReachedThreshold={0.1}
          refreshControl={
            <RefreshControl
              refreshing={refreshing}
              onRefresh={handleRefresh}
              colors={[colors.primary['500']]}
              tintColor={colors.primary['500']}
              progressBackgroundColor="#FFFFFF"
            />
          }
        />
      </View>
    </View>
  )
}
