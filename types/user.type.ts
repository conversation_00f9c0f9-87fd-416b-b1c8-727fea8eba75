import { GenderEnum } from '@/enums/common.enum'
import { ReactNativeFile } from './global.type'
import { Media } from './media.type'
import { Subscription } from './subscription.type'

export interface User {
  id: string
  name: string
  roles?: ('admin' | 'user')[] | null
  subscription?: (string | null) | Subscription
  gender?: ('male' | 'female') | null
  dob?: string | null
  address?: string | null
  settings?: {
    fontSize?: ('small' | 'normal' | 'large') | null
    fontWeight?: ('thin' | 'normal' | 'bold') | null
  }
  lastLogin?: string | null
  avatar?: (string | null) | Media
  connectedTo?: ('google' | 'facebook')[] | null
  oauthAvatar?: string | null
  subscriptionTime?: {
    startDate?: string | null
    endDate?: string | null
  }
  sendExpiredNotificationTime?: number | null
  sendExpiredWarningNotificationCount?: number | null
  hasAppliedInvitationCode?: boolean | null
  updatedAt: string
  createdAt: string
  email: string
  resetPasswordToken?: string | null
  resetPasswordExpiration?: string | null
  salt?: string | null
  hash?: string | null
  _verified?: boolean | null
  _verificationToken?: string | null
  loginAttempts?: number | null
  lockUntil?: string | null
  password?: string | null
}

export interface UpdateUserProfileRequest {
  name: string
  avatar?: ReactNativeFile | string
  gender?: GenderEnum
  dob?: string
  address?: string
}

export interface UpdateUserProfileResponse {
  message: string
  updatedUser: User
}

export interface ChangePasswordRequest {
  currentPassword: string
  password: string
}

export interface ChangePasswordResponse {
  message: string
}
