import { LoadingScreenIndicator } from '@/components/ui/LoadingScreenIndicator/LoadingScreenIndicator'
import React, { createContext, useCallback, useEffect, useMemo, useRef, useState } from 'react'

export const LoadingScreenContext = createContext<{
  showLoading: () => void
  hideLoading: () => void
  isLoading: boolean
}>({
  showLoading: () => {},
  hideLoading: () => {},
  isLoading: false,
})

export const LoadingScreenProvider: React.FC<{ children?: React.ReactNode }> = ({ children }) => {
  const [isLoading, setIsLoading] = useState(false)
  const timeoutRef = useRef<ReturnType<typeof setTimeout> | null>(null)

  const showLoading = useCallback(() => {
    // Clear any pending hide timeout
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current)
      timeoutRef.current = null
    }
    if (!isLoading) setIsLoading(true)
  }, [isLoading])

  const hideLoading = useCallback((delayMs = 200) => {
    // Clear any existing timeout
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current)
    }

    timeoutRef.current = setTimeout(() => {
      setIsLoading(false)
      timeoutRef.current = null
    }, delayMs)
  }, [])

  // Cleanup timeout on unmount
  useEffect(() => {
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current)
      }
    }
  }, [])

  // Memoize the context value to prevent unnecessary re-renders
  const contextValue = useMemo(
    () => ({
      showLoading,
      hideLoading,
      isLoading,
    }),
    [showLoading, hideLoading, isLoading],
  )

  return (
    <LoadingScreenContext.Provider value={contextValue}>
      {children}
      <LoadingScreenIndicator visible={isLoading} />
    </LoadingScreenContext.Provider>
  )
}
