export interface ExaminationForm {
  id: string
  name: string
  description?: string | null
  step: {
    title: string
    questions: (string | ExaminationQuestion)[]
    id?: string | null
  }[]
  updatedAt: string
  createdAt: string
}

export interface ExaminationQuestion {
  id: string
  name: string
  fields?:
    | (
        | {
            label: string
            description?: string | null
            required?: boolean | null
            /**
             * Control which gender this question will be shown to
             */
            genderVisibility?: ('all' | 'male' | 'female') | null
            yesLabel?: string | null
            noLabel?: string | null
            id?: string | null
            blockName?: string | null
            blockType: 'radio-yes-no-question'
          }
        | {
            label: string
            description?: string | null
            required?: boolean | null
            /**
             * Control which gender this question will be shown to
             */
            genderVisibility?: ('all' | 'male' | 'female') | null
            radioOptions: {
              label: string
              type?: ('picker' | 'text') | null
              id?: string | null
            }[]
            id?: string | null
            blockName?: string | null
            blockType: 'radio-group-question'
          }
        | {
            label: string
            description?: string | null
            required?: boolean | null
            /**
             * Control which gender this question will be shown to
             */
            genderVisibility?: ('all' | 'male' | 'female') | null
            yesLabel?: string | null
            noLabel?: string | null
            openCheckboxLabel: string
            checkboxQuestions?:
              | {
                  checkboxLabel: string
                  checkboxQuestionLabel: string
                  checkboxQuestionDescription?: string | null
                  keywordCategory?:
                    | (
                        | 'body_part'
                        | 'medicine'
                        | 'medicine_type'
                        | 'allergy_type'
                        | 'symptom_or_disease'
                        | 'test_vaccine_treatment'
                        | 'lifestyle'
                        | 'medical_science'
                        | 'other'
                      )
                    | null
                  id?: string | null
                }[]
              | null
            noteLabel?: string | null
            id?: string | null
            blockName?: string | null
            blockType: 'radio-yes-no-open-checkbox-question'
          }
        | {
            label: string
            description?: string | null
            required?: boolean | null
            /**
             * Control which gender this question will be shown to
             */
            genderVisibility?: ('all' | 'male' | 'female') | null
            yesLabel?: string | null
            noLabel?: string | null
            openGroupQuestionsLabel?: string | null
            openGroupQuestions: (
              | {
                  label: string
                  required?: boolean | null
                  id?: string | null
                  blockName?: string | null
                  blockType: 'plain-text-question'
                }
              | {
                  label: string
                  description?: string | null
                  required?: boolean | null
                  /**
                   * Control which gender this question will be shown to
                   */
                  genderVisibility?: ('all' | 'male' | 'female') | null
                  type: 'single'
                  /**
                   * Keywords will be filtered by the selected category
                   */
                  keywordCategory?:
                    | (
                        | 'body_part'
                        | 'medicine'
                        | 'medicine_type'
                        | 'allergy_type'
                        | 'symptom_or_disease'
                        | 'test_vaccine_treatment'
                        | 'lifestyle'
                        | 'medical_science'
                        | 'other'
                      )[]
                    | null
                  id?: string | null
                  blockName?: string | null
                  blockType: 'select-keyword-question'
                }
              | {
                  label: string
                  required?: boolean | null
                  selectOptions: {
                    label: string
                    id?: string | null
                  }[]
                  id?: string | null
                  blockName?: string | null
                  blockType: 'select-question'
                }
            )[]
            id?: string | null
            blockName?: string | null
            blockType: 'radio-yes-no-open-multiple-group-question'
          }
        | {
            label: string
            description?: string | null
            required?: boolean | null
            /**
             * Control which gender this question will be shown to
             */
            genderVisibility?: ('all' | 'male' | 'female') | null
            yesLabel?: string | null
            noLabel?: string | null
            /**
             * This group will be shown for a 'Yes' answer.
             */
            additionalRadioGroups: {
              label: string
              required?: boolean | null
              radioOptions: {
                label: string
                id?: string | null
              }[]
              id?: string | null
            }[]
            id?: string | null
            blockName?: string | null
            blockType: 'radio-yes-no-open-additional-group-question'
          }
        | {
            label: string
            description?: string | null
            required?: boolean | null
            /**
             * Control which gender this question will be shown to
             */
            genderVisibility?: ('all' | 'male' | 'female') | null
            keywordCategory?:
              | (
                  | 'body_part'
                  | 'medicine'
                  | 'medicine_type'
                  | 'allergy_type'
                  | 'symptom_or_disease'
                  | 'test_vaccine_treatment'
                  | 'lifestyle'
                  | 'medical_science'
                  | 'other'
                )[]
              | null
            id?: string | null
            blockName?: string | null
            blockType: 'multiple-select-dropdown-question'
          }
        | {
            label: string
            description?: string | null
            required?: boolean | null
            /**
             * Control which gender this question will be shown to
             */
            genderVisibility?: ('all' | 'male' | 'female') | null
            yesLabel?: string | null
            noLabel?: string | null
            /**
             * This note will be shown for a 'Yes' answer.
             */
            additionalNoteLabel: string
            additionalNoteFields?:
              | (
                  | {
                      label: string
                      required?: boolean | null
                      id?: string | null
                      blockName?: string | null
                      blockType: 'plain-text-question'
                    }
                  | {
                      label: string
                      required?: boolean | null
                      /**
                       * Control how the user will input the number
                       */
                      type?: 'typing' | null
                      id?: string | null
                      blockName?: string | null
                      blockType: 'input-number-question'
                    }
                )[]
              | null
            id?: string | null
            blockName?: string | null
            blockType: 'radio-yes-no-open-additional-note-question'
          }
        | {
            group: (
              | {
                  label: string
                  required?: boolean | null
                  /**
                   * Control how the user will input the number
                   */
                  type?: 'typing' | null
                  id?: string | null
                  blockName?: string | null
                  blockType: 'input-number-question'
                }
              | {
                  label: string
                  required?: boolean | null
                  id?: string | null
                  blockName?: string | null
                  blockType: 'plain-text-question'
                }
            )[]
            id?: string | null
            blockName?: string | null
            blockType: 'input-group-question'
          }
        | {
            label: string
            description?: string | null
            required?: boolean | null
            /**
             * Control which gender this question will be shown to
             */
            genderVisibility?: ('all' | 'male' | 'female') | null
            id?: string | null
            blockName?: string | null
            blockType: 'body-part-question'
          }
      )[]
    | null
  updatedAt: string
  createdAt: string
}
