import { Text } from '@/components/ui/Text/Text'
import { useSheet } from '@/contexts/SheetContext/SheetContext'
import { APP_ROUTES } from '@/routes/appRoutes'
import { Link, LinkProps } from 'expo-router'
import { useTranslation } from 'react-i18next'
import { TouchableOpacity, View } from 'react-native'
import Svg, { Path } from 'react-native-svg'
interface AuthPromptSheetBoxProps {
  close: () => void
  redirect?: string
  forceBack?: boolean
}
export const AuthPromptSheetBox = ({ close, redirect, forceBack }: AuthPromptSheetBoxProps) => {
  const { t } = useTranslation()
  return (
    <View className="flex flex-col items-center justify-center gap-y-6 px-6 py-10 pt-2">
      <View className="flex flex-row items-center gap-x-2">
        <Svg width={25} height={24} viewBox="0 0 25 24" fill="none">
          <Path
            d="M12.5 7.75V13"
            stroke="#EF4444"
            strokeWidth={1.5}
            strokeLinecap="round"
            strokeLinejoin="round"
          />
          <Path
            d="M21.5802 8.58003V15.42C21.5802 16.54 20.9802 17.58 20.0102 18.15L14.0702 21.58C13.1002 22.14 11.9002 22.14 10.9202 21.58L4.98016 18.15C4.01016 17.59 3.41016 16.55 3.41016 15.42V8.58003C3.41016 7.46003 4.01016 6.41999 4.98016 5.84999L10.9202 2.42C11.8902 1.86 13.0902 1.86 14.0702 2.42L20.0102 5.84999C20.9802 6.41999 21.5802 7.45003 21.5802 8.58003Z"
            stroke="#EF4444"
            strokeWidth={1.5}
            strokeLinecap="round"
            strokeLinejoin="round"
          />
          <Path
            d="M12.5 16.2V16.2999"
            stroke="#EF4444"
            strokeWidth={2}
            strokeLinecap="round"
            strokeLinejoin="round"
          />
        </Svg>
        <Text size="body7">{t('MES-572')}</Text>
      </View>

      {/* Action */}
      <View className="flex flex-row items-center gap-x-3">
        <Link
          href={
            {
              pathname: APP_ROUTES.REGISTER.path,
              params: { redirect, forceBack },
            } as LinkProps['href']
          }
          asChild
          onPress={close}
        >
          <TouchableOpacity className="max-w-[180px] flex-1 rounded-lg border border-custom-text-disabled px-3 py-2 ">
            <Text size="body6" className="text-center">
              {t('MES-09')}
            </Text>
          </TouchableOpacity>
        </Link>
        <Link
          href={
            {
              pathname: APP_ROUTES.LOGIN.path,
              params: { redirect, forceBack },
            } as LinkProps['href']
          }
          asChild
          onPress={close}
        >
          <TouchableOpacity className="max-w-[180px] flex-1 rounded-lg border border-primary-500 bg-primary px-3 py-2">
            <Text size="body6" className="text-center" variant="white">
              {t('MES-06')}
            </Text>
          </TouchableOpacity>
        </Link>
      </View>
    </View>
  )
}
export const useOpenAuthPromptSheetBox = () => {
  const { openSheet, closeSheet } = useSheet()
  const handleOpenAuthPromptSheetBox = ({
    redirect,
    forceBack,
  }: {
    redirect?: string
    forceBack?: boolean
  } = {}) => {
    openSheet({
      children: ({ close }) => (
        <AuthPromptSheetBox close={close} redirect={redirect} forceBack={forceBack} />
      ),
      options: {
        type: 'basic',
        enableDynamicSizing: true,
        // snapPoints: ['25%'],
        enableOverDrag: false,
        enableContentPanningGesture: false,
        enableHandlePanningGesture: false,
      },
    })
  }
  const handleCloseAuthPromptSheetBox = () => {
    closeSheet()
  }
  return { handleOpenAuthPromptSheetBox, handleCloseAuthPromptSheetBox }
}
