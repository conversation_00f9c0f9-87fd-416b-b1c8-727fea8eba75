import { DynamicDialog } from '@/components/ui/Dialog/DynamicDialog'
import * as Crypto from 'expo-crypto'
import React, {
  ReactNode,
  createContext,
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from 'react'

export interface DialogConfig<T = unknown, K = any> {
  key?: string
  id: string
  title?: string
  data?: T
  children?:
    | (({ close, data }: { close: (params?: K) => void; data: T }) => React.ReactNode)
    | React.ReactNode
  wrapperClassName?: string
  bodyClassName?: string
  isOpen?: boolean
  onClose?: (params?: K) => void
  variant?: 'default' | 'blank'
  modal?: boolean
  useQueue?: boolean
  managedQueue?: boolean
  queueKey?: string
  contentSize?: 'xs' | 'sm' | 'md' | 'lg' | 'full'
}

export interface DialogRef<T = unknown, K = any> {
  id: string
  key?: string
  queueKey?: string
  close: (params?: K) => void
  closeByKey: (params?: K) => void
  data?: T
  onClose: (callback: (params?: K) => void) => void
  onOpen: (callback: () => void) => void
}

export interface DialogContextProps {
  openDialog: <T>(dialogConfig: Omit<DialogConfig<T>, 'id'>) => DialogRef<T>
  closeDialog: (id: string) => void
  closeDialogByKey: (key: string) => void
  openQueueDialog: <T>(dialogConfig: Omit<DialogConfig<T>, 'id' | 'managedQueue'>) => DialogRef<T>
  runQueueDialog: (id?: string) => void
  runAllQueueDialogs: () => void
  runQueueDialogsByKey: (queueKey: string) => void
  getQueuedDialogs: () => DialogConfig[]
}

interface DialogProviderProps {
  children: ReactNode
}

export const DialogContext = createContext<DialogContextProps | undefined>(undefined)

export function DialogProvider({ children }: DialogProviderProps) {
  const [dialogs, setDialogs] = useState<DialogConfig[]>([])
  const [queuedDialogs, setQueuedDialogs] = useState<DialogConfig[]>([])
  const [managedQueueDialogs, setManagedQueueDialogs] = useState<DialogConfig[]>([])
  const timeoutRefs = useRef<Record<string, number>>({})
  const processingQueue = useRef<boolean>(false)

  const hasOpenDialog = useMemo(() => {
    return dialogs.some((dialog) => dialog.isOpen === true)
  }, [dialogs])

  useEffect(() => {
    if (!hasOpenDialog && queuedDialogs.length > 0 && !processingQueue.current) {
      processingQueue.current = true

      setTimeout(() => {
        const nextDialog = queuedDialogs[0]

        setQueuedDialogs((prev) => prev.slice(1))
        setDialogs((prev) => [...prev, { ...nextDialog, isOpen: true }])

        processingQueue.current = false
      }, 0)
    }
  }, [hasOpenDialog, queuedDialogs])

  useEffect(() => {
    const timeoutIds = Object.values(timeoutRefs.current)
    return () => {
      timeoutIds.forEach(clearTimeout)
    }
  }, [])

  const openDialog = useCallback(
    <T, K = any>(dialogConfig: Omit<DialogConfig<T, K>, 'id'>) => {
      const id = Crypto.randomUUID()
      const newDialog: DialogConfig<T, K> = {
        id,
        isOpen: true,
        variant: 'default',
        useQueue: false,
        managedQueue: false,
        ...dialogConfig,
      }

      if (newDialog.useQueue && hasOpenDialog) {
        setQueuedDialogs((prev) => [...prev, newDialog as DialogConfig])
      } else {
        setDialogs((prevDialogs) => [...prevDialogs, newDialog as DialogConfig])
      }

      // Return a reference object with dialog properties and methods
      return {
        id,
        key: newDialog.key,
        queueKey: newDialog.queueKey,
        data: newDialog.data,
        close: (params?: K) => closeDialog(id, params),
        closeByKey: (params?: K) =>
          newDialog.key ? closeDialogByKey(newDialog.key, params) : closeDialog(id, params),
        onClose: (callback: (params?: K) => void) => {
          setDialogs((prevDialogs) =>
            prevDialogs.map((dialog) => {
              if (dialog.id === id) {
                return { ...dialog, onClose: callback }
              }
              return dialog
            }),
          )
          setQueuedDialogs((prevDialogs) =>
            prevDialogs.map((dialog) => {
              if (dialog.id === id) {
                return { ...dialog, onClose: callback }
              }
              return dialog
            }),
          )
        },
        onOpen: (callback: () => void) => {
          setDialogs((prevDialogs) =>
            prevDialogs.map((dialog) => {
              if (dialog.id === id) {
                return { ...dialog, onOpen: callback }
              }
              return dialog
            }),
          )
          setQueuedDialogs((prevDialogs) =>
            prevDialogs.map((dialog) => {
              if (dialog.id === id) {
                return { ...dialog, onOpen: callback }
              }
              return dialog
            }),
          )
        },
      } as DialogRef<T, K>
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [hasOpenDialog],
  )

  const openQueueDialog = useCallback(
    <T, K = any>(dialogConfig: Omit<DialogConfig<T, K>, 'id' | 'managedQueue'>) => {
      const id = Crypto.randomUUID()

      const newDialog: DialogConfig<T, K> = {
        id,
        isOpen: true,
        variant: 'default',
        useQueue: false,
        managedQueue: true,
        ...dialogConfig,
      }

      setManagedQueueDialogs((prev) => [...prev, newDialog as DialogConfig])

      // Return a reference object with dialog properties and methods
      return {
        id,
        key: newDialog.key,
        queueKey: newDialog.queueKey,
        data: newDialog.data,
        close: (params?: K) => closeDialog(id, params),
        closeByKey: (params?: K) =>
          newDialog.key ? closeDialogByKey(newDialog.key, params) : closeDialog(id, params),
        onClose: (callback: (params?: K) => void) => {
          setManagedQueueDialogs((prevDialogs) =>
            prevDialogs.map((dialog) => {
              if (dialog.id === id) {
                return { ...dialog, onClose: callback }
              }
              return dialog
            }),
          )
        },
        onOpen: (callback: () => void) => {
          setManagedQueueDialogs((prevDialogs) =>
            prevDialogs.map((dialog) => {
              if (dialog.id === id) {
                return { ...dialog, onOpen: callback }
              }
              return dialog
            }),
          )
        },
      } as DialogRef<T, K>
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [],
  )

  const runQueueDialog = useCallback(
    (id?: string) => {
      if (!id) {
        if (managedQueueDialogs.length > 0) {
          const nextDialog = managedQueueDialogs[0]

          setManagedQueueDialogs((prev) => prev.slice(1))

          if (!hasOpenDialog) {
            setDialogs((prev) => [...prev, { ...nextDialog, isOpen: true }])
          } else {
            setQueuedDialogs((prev) => [...prev, nextDialog])
          }
        }
        return
      }

      const dialogIndex = managedQueueDialogs.findIndex((dialog) => dialog.id === id)

      if (dialogIndex !== -1) {
        const dialogToRun = managedQueueDialogs[dialogIndex]

        setManagedQueueDialogs((prev) => prev.filter((dialog) => dialog.id !== id))

        if (!hasOpenDialog) {
          setDialogs((prev) => [...prev, { ...dialogToRun, isOpen: true }])
        } else {
          setQueuedDialogs((prev) => [...prev, dialogToRun])
        }
      }
    },
    [managedQueueDialogs, hasOpenDialog],
  )

  const runQueueDialogsByKey = useCallback(
    (queueKey: string) => {
      if (!queueKey) return

      const matchingDialogs = managedQueueDialogs.filter((dialog) => dialog.queueKey === queueKey)

      if (matchingDialogs.length === 0) return

      setManagedQueueDialogs((prev) => prev.filter((dialog) => dialog.queueKey !== queueKey))

      if (!hasOpenDialog && matchingDialogs.length > 0) {
        const firstDialog = matchingDialogs[0]
        const remainingDialogs = matchingDialogs.slice(1)

        setDialogs((prev) => [...prev, { ...firstDialog, isOpen: true }])

        if (remainingDialogs.length > 0) {
          setQueuedDialogs((prev) => [...prev, ...remainingDialogs])
        }
      } else {
        setQueuedDialogs((prev) => [...prev, ...matchingDialogs])
      }
    },
    [managedQueueDialogs, hasOpenDialog],
  )

  const runAllQueueDialogs = useCallback(() => {
    if (managedQueueDialogs.length === 0) return

    if (!hasOpenDialog && managedQueueDialogs.length > 0) {
      const nextDialog = managedQueueDialogs[0]
      const remainingDialogs = managedQueueDialogs.slice(1)

      setManagedQueueDialogs([])
      setDialogs((prev) => [...prev, { ...nextDialog, isOpen: true }])

      if (remainingDialogs.length > 0) {
        setQueuedDialogs((prev) => [...prev, ...remainingDialogs])
      }
    } else {
      setQueuedDialogs((prev) => [...prev, ...managedQueueDialogs])
      setManagedQueueDialogs([])
    }
  }, [managedQueueDialogs, hasOpenDialog])

  const getQueuedDialogs = useCallback(() => {
    return [...managedQueueDialogs]
  }, [managedQueueDialogs])

  const closeDialog = useCallback((id: string, params?: any) => {
    const actualParams = params === id ? undefined : params

    setDialogs((prevDialogs) =>
      prevDialogs.map((dialog) => {
        if (dialog.id === id) {
          if (dialog.onClose) {
            dialog.onClose(actualParams)
          }
          return { ...dialog, isOpen: false }
        }
        return dialog
      }),
    )

    timeoutRefs.current[id] = setTimeout(() => {
      setDialogs((prevDialogs) => prevDialogs.filter((dialog) => dialog.id !== id))
      delete timeoutRefs.current[id]
    }, 300)
  }, [])

  const closeDialogByKey = useCallback((key: string, params?: any) => {
    let dialogId: string | undefined

    setDialogs((prevDialogs) =>
      prevDialogs.map((dialog) => {
        if (dialog.key === key) {
          const actualParams = params === key ? undefined : params
          if (dialog.onClose) {
            dialog.onClose(actualParams)
          }
          dialogId = dialog.id
          return { ...dialog, isOpen: false }
        }
        return dialog
      }),
    )

    if (dialogId) {
      timeoutRefs.current[dialogId] = setTimeout(() => {
        setDialogs((prevDialogs) => prevDialogs.filter((dialog) => dialog.key !== key))
        if (dialogId) delete timeoutRefs.current[dialogId]
      }, 300)
    }

    setQueuedDialogs((prev) => prev.filter((dialog) => dialog.key !== key))
    setManagedQueueDialogs((prev) => prev.filter((dialog) => dialog.key !== key))
  }, [])

  const contextValue = useMemo(
    () => ({
      openDialog,
      closeDialog,
      closeDialogByKey,
      openQueueDialog,
      runQueueDialog,
      runAllQueueDialogs,
      runQueueDialogsByKey,
      getQueuedDialogs,
    }),
    [
      openDialog,
      closeDialog,
      closeDialogByKey,
      openQueueDialog,
      runQueueDialog,
      runAllQueueDialogs,
      runQueueDialogsByKey,
      getQueuedDialogs,
    ],
  )

  return (
    <DialogContext.Provider value={contextValue}>
      {children}
      {dialogs.map((dialog) => {
        const {
          children: dialogChildren,
          id,
          title,
          data,
          wrapperClassName,
          bodyClassName,
          isOpen,
          variant,
          modal,
          onOpen,
          contentSize,
        } = dialog as DialogConfig & { onOpen?: () => void }

        return (
          <DynamicDialog
            isOpen={isOpen}
            key={id}
            id={id}
            title={title}
            data={data}
            closeDialog={(params) => closeDialog(id, params === id ? undefined : params)}
            wrapperClassName={wrapperClassName}
            variant={variant}
            modal={modal}
            onOpen={onOpen}
            bodyClassName={bodyClassName}
            contentSize={contentSize}
          >
            {typeof dialogChildren === 'function'
              ? dialogChildren({
                  close: (params) => closeDialog(id, params),
                  data: data,
                })
              : dialogChildren || <></>}
          </DynamicDialog>
        )
      })}
    </DialogContext.Provider>
  )
}
