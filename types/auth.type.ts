import { User } from './user.type'

export interface RegisterRequest {
  name: string
  email: string
  password: string
}
export interface RegisterResponse {
  doc: User
  message: string
}

export interface LoginResponse {
  message: string
  user: User
  sessionToken: string
  token?: string | null
  exp: number
}

export interface RefreshTokenResponse {
  exp?: number
  message?: string
  strategy?: string
  user?: User
  refreshedToken?: string
}
export interface ResendVerifyEmailRequest {
  email: string
  language?: string
}
export interface ResendVerifyEmailResponse {
  success: boolean
  message: string
}

export interface SendResetPasswordEmailRequest {
  email: string
  language?: string
}
export interface SendResetPasswordEmailResponse {
  success: boolean
  message: string
}

export interface OAuthLoginRequest {
  idToken: string
}
export interface OAuthLoginResponse {
  token: string
  sessionToken: string
  exp: number
  user: {
    email: string
    name: string
    id: string
  }
}

export interface GoogleLoginRequest {
  token: string
}
export interface GoogleLoginResponse extends OAuthLoginResponse {}

export interface AppleLoginRequest {
  idToken: string
}
export interface AppleLoginResponse extends OAuthLoginResponse {}

export interface DeactivateAccountResponse {
  success: boolean
  message: string
}
