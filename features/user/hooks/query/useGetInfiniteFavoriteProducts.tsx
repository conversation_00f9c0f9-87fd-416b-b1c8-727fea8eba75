import { PaginatedDocs } from '@/types/global.type'
import { Params } from '@/types/http.type'
import { InfiniteData, UseInfiniteQueryOptions, useInfiniteQuery } from '@tanstack/react-query'
import { AxiosRequestConfig } from 'axios'
import { favoriteProductService } from '../../services/favorite-product.service'
import { FavoriteProduct } from '../../types'
import { favoriteProductQueryKeys } from './queryKeys'

type FilteredProductsQueryConfig = Partial<
  Omit<
    UseInfiniteQueryOptions<
      PaginatedDocs<FavoriteProduct> | null,
      Error,
      InfiniteData<PaginatedDocs<FavoriteProduct> | null>
    >,
    'queryFn' | 'queryKey' | 'getNextPageParam' | 'initialPageParam'
  >
>

interface UseGetInfiniteFavoriteProductsProps {
  params?: Params
  options?: AxiosRequestConfig
  key?: string | number
  config?: FilteredProductsQueryConfig
  overrideKey?: string[]
}

export const useGetInfiniteFavoriteProducts = ({
  params = {},
  options = {},
  config = {},
  overrideKey,
}: UseGetInfiniteFavoriteProductsProps = {}) => {
  const {
    isError: isGetFavoriteProductsError,
    isLoading: isGetFavoriteProductsLoading,
    isFetching: isGetFavoriteProductsFetching,
    isFetchingNextPage: isGetFavoriteProductsFetchingNextPage,
    data: favoriteProducts,
    fetchNextPage,
    hasNextPage,
    ...rest
  } = useInfiniteQuery({
    queryKey: overrideKey
      ? overrideKey
      : [favoriteProductQueryKeys['favoriteProducts'].base(), params],
    queryFn: async ({ pageParam = 1 }) => {
      const result = await favoriteProductService.getFavoriteProductsByUser({
        params: {
          ...params,
          page: pageParam,
        },
        options,
      })
      return result
    },
    getNextPageParam: (lastPage) => lastPage?.nextPage,
    getPreviousPageParam: (lastPage) => lastPage?.prevPage,
    initialPageParam: 1,
    ...config,
  })

  return {
    isGetFavoriteProductsError,
    isGetFavoriteProductsLoading,
    isGetFavoriteProductsFetching,
    isGetFavoriteProductsFetchingNextPage,
    favoriteProducts,
    fetchNextPage,
    hasNextPage,
    ...rest,
  }
}
