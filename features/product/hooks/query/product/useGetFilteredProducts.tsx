import { productService } from '@/features/product/services/product.service'
import { Product } from '@/features/product/types'
import { PaginatedDocs } from '@/types/global.type'
import { Params } from '@/types/http.type'
import { UseQueryOptions, useQuery } from '@tanstack/react-query'
import { AxiosRequestConfig } from 'axios'
import { productQueryKeys } from './queryKeys'

export const useGetFilteredProducts = ({
  params = {},
  options = {},
  useQueryOptions,
}: {
  params?: Params
  options?: AxiosRequestConfig
  useQueryOptions?: Omit<UseQueryOptions<PaginatedDocs<Product> | null>, 'queryKey' | 'queryFn'>
} = {}) => {
  const {
    isError: isGetFilteredProductsError,
    isPending: isGetFilteredProductsLoading,
    data: filteredProducts,
    ...rest
  } = useQuery({
    queryKey: [productQueryKeys['filteredProducts'].base(), params],
    queryFn: async () =>
      productService.getFilteredProducts({
        params: params,
        options: options,
      }),
    ...useQueryOptions,
  })
  return {
    isGetFilteredProductsError,
    isGetFilteredProductsLoading,
    filteredProducts,
    ...rest,
  }
}
