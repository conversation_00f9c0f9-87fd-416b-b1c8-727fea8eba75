import FilterIcon from '@/assets/icons/filter-icon.svg'
import { SearchInput, SearchInputRef } from '@/components/ui/SearchInput/SearchInput'
import { useEffect, useRef, useState } from 'react'
import { useTranslation } from 'react-i18next'
import { Keyboard, TouchableOpacity, View } from 'react-native'
import { useShallow } from 'zustand/react/shallow'
import { useHospitalStore } from '../../stores/HospitalStore'
import { useOpenFilterHospitalBox } from '../FilterHospitalBox/FilterHospitalBox'
import { SearchTipsBox } from './SearchTipsBox'

interface SearchHospitalHeaderProps {
  showTipsBox?: boolean
}

export const SearchHospitalHeader = ({ showTipsBox }: SearchHospitalHeaderProps) => {
  const { t } = useTranslation()
  const { hasActiveFilters } = useHospitalStore(
    useShallow((state) => ({
      hasActiveFilters: state.hasActiveFilters,
    })),
  )
  const [searchInputValue, setSearchInputValue] = useState('')
  const searchInputRef = useRef<SearchInputRef>(null)

  const handleSearchInputChange = (text: string) => {
    setSearchInputValue(text)
  }

  const handleClearSearchInput = () => {
    setSearchInputValue('')
    setSearchTextValue('')
  }

  const { setSearchTextValue } = useHospitalStore(
    useShallow((state) => ({
      setSearchTextValue: state.setSearchTextValue,
    })),
  )

  const { handleOpenFilterHospitalBox } = useOpenFilterHospitalBox()

  useEffect(() => {
    setTimeout(() => {
      searchInputRef.current?.focus()
    }, 500)
  }, [])

  return (
    <View className="flex flex-col gap-y-2 px-4">
      <View className="flex flex-row items-center gap-x-2">
        <SearchInput
          ref={searchInputRef}
          placeholder={t('MES-66')}
          value={searchInputValue}
          onChangeText={handleSearchInputChange}
          onClear={handleClearSearchInput}
          onSubmitEditing={() => {
            setSearchTextValue(searchInputValue)
          }}
        />
        <TouchableOpacity
          hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
          onPress={() => {
            Keyboard.dismiss()
            handleOpenFilterHospitalBox()
          }}
          className="relative flex h-full flex-row items-center gap-x-2 rounded-lg border border-custom-divider-border p-3"
        >
          <FilterIcon width={18} height={18} />
          {hasActiveFilters && (
            <View className="absolute -right-1 -top-1 size-4 rounded-full bg-custom-danger-600/80" />
          )}
        </TouchableOpacity>
      </View>
      {showTipsBox && <SearchTipsBox />}
    </View>
  )
}
