import { BaseWebView } from '@/components/BaseWebView/BaseWebView'
import { StackScreenBase } from '@/components/StackBase/StackScreenBase'
import { WEBVIEW_APP_ROUTES } from '@/routes/webviewRoutes'
import { useLocalSearchParams } from 'expo-router'
import React from 'react'
import { useTranslation } from 'react-i18next'
export default function ProductDetailScreen() {
  const { t } = useTranslation()
  const { slug } = useLocalSearchParams()

  return (
    <>
      <StackScreenBase
        options={{
          headerTitle: t('MES-661'),
          headerBackTitle: '',
        }}
      />
      <BaseWebView
        source={{
          uri: WEBVIEW_APP_ROUTES.PRODUCTS_V2?.path + `/${slug}`,
        }}
      />
    </>
  )
}
