import { create } from 'zustand'
import { devtools } from 'zustand/middleware'

export interface MedicalDictionaryState {
  searchText: string | null
  filters: MedicalDictionaryFilter
  tempFilters: MedicalDictionaryFilter
  hasActiveFilters: boolean
}

export enum MedicalDictionaryFilterType {
  KEYWORD_TYPE = 'keyword_type',
}

export interface MedicalDictionaryFilterItem {
  id: string
  label: string
  type: MedicalDictionaryFilterType
}

export interface MedicalDictionaryFilter {
  [MedicalDictionaryFilterType.KEYWORD_TYPE]: MedicalDictionaryFilterItem[] | null
}

export interface MedicalDictionaryActions {
  setSearchText: (searchText: string | null) => void
  toggleFilter: (
    filterType: MedicalDictionaryFilterType,
    filter: MedicalDictionaryFilterItem,
  ) => void
  clearFilters: (filterType?: MedicalDictionaryFilterType) => void
  clearAllFilters: () => void
  // Temporary filter actions
  toggleTempFilter: (
    filterType: MedicalDictionaryFilterType,
    filter: MedicalDictionaryFilterItem,
  ) => void
  applyTempFilters: () => void
  resetTempFilters: () => void
  initTempFilters: () => void
  clearAllTempFilters: () => void
  // Clear all filters and search text
  clearAllFiltersAndSearchText: () => void
  resetMedicalDictionaryStore: () => void
}

// Initial state for the medical dictionary store
const initialState: MedicalDictionaryState = {
  searchText: null,
  filters: {
    [MedicalDictionaryFilterType.KEYWORD_TYPE]: null,
  },
  tempFilters: {
    [MedicalDictionaryFilterType.KEYWORD_TYPE]: null,
  },
  hasActiveFilters: false,
}

// Helper function to check if filters are active
const hasActiveFilters = (filters: MedicalDictionaryFilter): boolean => {
  return Object.values(filters).some((filterArray) => filterArray && filterArray.length > 0)
}

// Create the medical dictionary store with Zustand
export const useMedicalDictionaryStore = create<
  MedicalDictionaryState & MedicalDictionaryActions
>()(
  devtools(
    (set, get) => ({
      ...initialState,
      setSearchText: (searchText: string | null) => {
        set({ searchText })
      },
      toggleFilter: (
        filterType: MedicalDictionaryFilterType,
        filter: MedicalDictionaryFilterItem,
      ) => {
        set((state) => {
          const currentFilters = state.filters[filterType] || []
          const isSelected = currentFilters.some((f) => f.id === filter.id)

          const newFilters = {
            ...state.filters,
            [filterType]: isSelected
              ? currentFilters.filter((f) => f.id !== filter.id)
              : [...currentFilters, filter],
          }

          return {
            filters: newFilters,
            hasActiveFilters: hasActiveFilters(newFilters),
          }
        })
      },
      clearFilters: (filterType?: MedicalDictionaryFilterType) => {
        set((state) => {
          const newFilters = filterType
            ? {
                ...state.filters,
                [filterType]: null,
              }
            : {
                [MedicalDictionaryFilterType.KEYWORD_TYPE]: null,
              }

          return {
            filters: newFilters,
            hasActiveFilters: hasActiveFilters(newFilters),
          }
        })
      },
      clearAllFilters: () => {
        set((state) => {
          const newFilters = {
            [MedicalDictionaryFilterType.KEYWORD_TYPE]: null,
          }

          return {
            filters: newFilters,
            hasActiveFilters: false,
          }
        })
      },
      // Temporary filter actions
      toggleTempFilter: (
        filterType: MedicalDictionaryFilterType,
        filter: MedicalDictionaryFilterItem,
      ) => {
        set((state) => {
          const currentTempFilters = state.tempFilters[filterType] || []
          const isSelected = currentTempFilters.some((f) => f.id === filter.id)

          return {
            tempFilters: {
              ...state.tempFilters,
              [filterType]: isSelected
                ? currentTempFilters.filter((f) => f.id !== filter.id)
                : [...currentTempFilters, filter],
            },
          }
        })
      },
      applyTempFilters: () => {
        set((state) => {
          const newFilters = { ...state.tempFilters }

          return {
            filters: newFilters,
            hasActiveFilters: hasActiveFilters(newFilters),
          }
        })
      },
      resetTempFilters: () => {
        set((state) => ({
          tempFilters: {
            [MedicalDictionaryFilterType.KEYWORD_TYPE]:
              state.filters[MedicalDictionaryFilterType.KEYWORD_TYPE],
          },
        }))
      },
      initTempFilters: () => {
        set((state) => ({
          tempFilters: {
            [MedicalDictionaryFilterType.KEYWORD_TYPE]:
              state.filters[MedicalDictionaryFilterType.KEYWORD_TYPE],
          },
        }))
      },
      clearAllTempFilters: () => {
        set({
          tempFilters: {
            [MedicalDictionaryFilterType.KEYWORD_TYPE]: null,
          },
        })
      },
      clearAllFiltersAndSearchText: () => {
        set({
          searchText: null,
          filters: initialState.filters,
          tempFilters: initialState.tempFilters,
          hasActiveFilters: false,
        })
      },
      resetMedicalDictionaryStore: () => {
        set(initialState)
      },
    }),
    {
      name: 'medical-dictionary-store',
    },
  ),
)
