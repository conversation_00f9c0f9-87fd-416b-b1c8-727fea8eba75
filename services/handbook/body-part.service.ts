import { API_ENDPOINTS } from '@/constants/endpoint.constant'
import { Params } from '@/types/http.type'

import { httpService } from '@/services/http/http.service'
import { BodyPart } from '@/types/body-part.type'
import { PaginatedDocs } from '@/types/global.type'
import { AxiosRequestConfig } from 'axios'

// SERVER / CLIENT
class BodyPartService {
  private static instance: BodyPartService

  private constructor() {}

  public static getInstance(): BodyPartService {
    if (!BodyPartService.instance) {
      BodyPartService.instance = new BodyPartService()
    }
    return BodyPartService.instance
  }

  /**
   * Fetch body parts using the HTTP service
   *
   * @param params - Optional query parameters for filtering or paginating body parts.
   * @param options - Optional fetch options such as headers, method, etc.
   * @returns A promise resolving to PaginatedDocs<BodyPart> or null in case of an error.
   */
  public async getBodyParts({
    params = {},
    options = {},
  }: {
    params?: Params
    options?: AxiosRequestConfig
  } = {}): Promise<PaginatedDocs<BodyPart> | null> {
    const data = await httpService.get<PaginatedDocs<BodyPart>>(
      `/${API_ENDPOINTS.body_parts_api}`,
      {
        params,
        ...options,
      },
    )
    return data
  }
}

export const bodyPartService = BodyPartService.getInstance()
