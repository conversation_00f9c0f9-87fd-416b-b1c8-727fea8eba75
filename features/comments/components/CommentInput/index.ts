export interface BaseCommentInputRef {
  focus: () => void
  blur: () => void
  clear: () => void
}

export { CommentInput } from './CommentInput'
export type { CommentInputProps, CommentInputRef } from './CommentInput'

// Keep platform-specific exports for backward compatibility
export { CommentInputIOS } from './CommentInputIOS'
export type { CommentInputIOSProps, CommentInputIOSRef } from './CommentInputIOS'

export { CommentInputAndroid } from './CommentInputAndroid'
export type { CommentInputAndroidProps, CommentInputAndroidRef } from './CommentInputAndroid'