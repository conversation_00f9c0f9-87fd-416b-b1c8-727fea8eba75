import { StackScreenBase } from '@/components/StackBase/StackScreenBase'
import MedicalHandbookScreen from '@/features/medical-handbook/screens/MedicalHandbookScreen/MedicalHandbookScreen'
import { withAuthentication } from '@/hoc/withAuthentication'

import { useTranslation } from 'react-i18next'
import { SafeAreaView } from 'react-native-safe-area-context'

function MedicalHandbookAppScreen() {
  const { t } = useTranslation()
  return (
    <SafeAreaView className="bg-white" style={{ flex: 1 }} edges={['left', 'right', 'bottom']}>
      <StackScreenBase
        options={{
          headerTitle: t('MES-33'),
          headerShown: true,
        }}
      />
      <MedicalHandbookScreen />
    </SafeAreaView>
  )
}

export default withAuthentication(MedicalHandbookAppScreen)
