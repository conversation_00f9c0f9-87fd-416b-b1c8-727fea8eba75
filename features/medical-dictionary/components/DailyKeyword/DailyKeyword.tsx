import { Skeleton } from '@/components/ui/Skeleton/Skeleton'
import { Text } from '@/components/ui/Text/Text'
import { LocaleEnum } from '@/enums/locale.enum'
import { useGetRandomKeywords } from '@/hooks/query/keyword/useGetRandomKeywords'
import { APP_ROUTES, AppRoutesEnum } from '@/routes/appRoutes'
import { LocalizeField } from '@/types/global.type'
import { KeywordV2 } from '@/types/keyword.type'
import { LinearGradient } from 'expo-linear-gradient'
import { Link } from 'expo-router'
import React, { useEffect } from 'react'
import { useTranslation } from 'react-i18next'
import { TouchableOpacity, View } from 'react-native'
interface DailyKeywordProps {
  isRefreshing?: boolean
}
export const DailyKeyword = ({ isRefreshing }: DailyKeywordProps) => {
  const { t } = useTranslation()

  const { randomKeywords, isGetRandomKeywordsLoading, refetch } = useGetRandomKeywords({
    params: {
      locale: 'all',
      limit: 1,
      depth: 5,
      // convertDescriptionHTML: true,
      select: {
        name: true,
        id: true,
        hiragana: true,
        // audio: true,
        // relatedImages: true,
        // description: true,
      },
    },
    useQueryOptions: {
      staleTime: 5 * 60 * 1000,
    },
  })

  const firstRandomKeyword = randomKeywords?.docs[0]
  const { name, hiragana } = (firstRandomKeyword as KeywordV2) || {}
  const localizedName = name as unknown as LocalizeField<string>
  useEffect(() => {
    if (isRefreshing) {
      refetch()
    }
  }, [isRefreshing])
  if (!isGetRandomKeywordsLoading && !randomKeywords?.docs.length) {
    return null
  }

  return (
    <>
      {isGetRandomKeywordsLoading || isRefreshing ? (
        <DailyVocabularyLoading />
      ) : (
        <Link
          href={{
            pathname:
              APP_ROUTES.MEDICAL_DICTIONARY?.children?.[
                AppRoutesEnum.MEDICAL_DICTIONARY_KEYWORD_DETAILS
              ]?.path,
            params: {
              id: firstRandomKeyword?.id,
            },
          }}
          asChild
        >
          <TouchableOpacity className="flex flex-col gap-y-3">
            <Text size="body3" variant="default">
              {t('MES-758')}
            </Text>
            <LinearGradient
              colors={['#8C82FA', '#C180FF']}
              start={{ x: 0, y: 1 }}
              end={{ x: 1, y: 0 }}
              className="flex items-center justify-center rounded-lg px-3 py-4"
              style={{
                borderRadius: 12,
                padding: 12,
              }}
            >
              <View className="flex  flex-col items-center justify-center gap-y-2">
                <Text
                  size="heading8"
                  variant="white"
                  numberOfLines={2}
                  className="line-clamp-2 text-center"
                >
                  {localizedName[LocaleEnum.JA]}
                </Text>
                {hiragana && (
                  <Text
                    size="body7"
                    variant="white"
                    numberOfLines={2}
                    className="line-clamp-2 text-center"
                  >
                    /{hiragana}/
                  </Text>
                )}
                <Text
                  size="body6"
                  variant="white"
                  numberOfLines={2}
                  className="line-clamp-2 text-center"
                >
                  {localizedName[LocaleEnum.VI]}
                </Text>
              </View>
            </LinearGradient>
          </TouchableOpacity>
        </Link>
      )}
    </>
  )
}

const DailyVocabularyLoading = () => {
  return <Skeleton className="h-[120px] w-full rounded-lg bg-gray-200" />
}
