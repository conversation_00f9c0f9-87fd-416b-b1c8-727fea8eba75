import { Text } from '@/components/ui/Text/Text'
import { APP_ROUTES } from '@/routes/appRoutes'
import { LinkProps, router } from 'expo-router'
import React from 'react'
import { useTranslation } from 'react-i18next'
import { TouchableOpacity, View } from 'react-native'

export const LoginPrompt = () => {
  const { t } = useTranslation()
  const handleLoginPress = () => {
    router.replace({
      pathname: APP_ROUTES.LOGIN.path,
    } as LinkProps['href'])
  }

  return (
    <View className="mt-4 flex-row items-center justify-center gap-1">
      <Text size="body6" variant="default">
        {t('MES-162')}
      </Text>
      <TouchableOpacity onPress={handleLoginPress}>
        <Text size="body6" variant="primary">
          {t('MES-06')}
        </Text>
      </TouchableOpacity>
    </View>
  )
}
