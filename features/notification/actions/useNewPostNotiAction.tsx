import { APP_ROUTES } from '@/routes/appRoutes'
import { Link<PERSON><PERSON>, useRouter } from 'expo-router'
import { Platform } from 'react-native'

export const useNewPostNotiAction = () => {
  const router = useRouter()
  const handleOpenNewPostNoti = (slug: string) => {
    // Alert.alert('New post notification', slug)
    if (Platform.OS === 'ios') {
      setTimeout(() => {
        router.push({
          pathname: APP_ROUTES.POSTS.path + '/[slug]',
          params: {
            slug: slug,
          },
        } as LinkProps['href'])
      }, 1)
    } else {
      setImmediate(() => {
        router.push({
          pathname: APP_ROUTES.POSTS.path + '/[slug]',
          params: {
            slug: slug,
          },
        } as LinkProps['href'])
      })
    }
  }
  return {
    handleOpenNewPostNoti,
  }
}
