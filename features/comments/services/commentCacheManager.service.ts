import { Params } from '@/types/http.type'
import { QueryClient } from '@tanstack/react-query'
import { commentQueryKeys } from '../hooks/query/queryKeys'
import { Comment, TotalCommentsResponse } from '../types'

export interface CommentCacheConfig {
  documentId: string
  relationTo: 'keywords' | 'posts' | 'products'
  queryClient: QueryClient
}

export interface CommentQueryParams extends Params {
  page?: number
  limit?: number
  [key: string]: any
}

export class CommentCacheManager {
  private config: CommentCacheConfig

  constructor(config: CommentCacheConfig) {
    this.config = config
  }

  /**
   * Generate query keys for main comments
   */
  private getMainQueryKeys(params: CommentQueryParams) {
    return [
      commentQueryKeys['comments'].base(),
      this.config.relationTo,
      this.config.documentId,
      params,
    ]
  }

  /**
   * Generate query keys for nested comments
   */
  private getNestedQueryKeys(parentId: string, params: CommentQueryParams) {
    return [commentQueryKeys['nested-comments'].base(), this.config.relationTo, parentId, params]
  }

  /**
   * Generate query keys for total comments
   */
  private getTotalCommentsQueryKeys(params: CommentQueryParams) {
    return [
      commentQueryKeys['totalComments'].base(),
      this.config.documentId,
      this.config.relationTo,
      params,
    ]
  }

  /**
   * Add optimistic root comment to cache
   */
  addOptimisticRootComment(comment: Comment, params: CommentQueryParams) {
    const queryKeys = this.getMainQueryKeys(params)

    this.config.queryClient.setQueryData(queryKeys, (oldData: any) => {
      // Safety: Return early if no data exists or data structure is invalid
      if (!oldData || !oldData.pages || !Array.isArray(oldData.pages)) {
        console.warn('[CommentCache] No valid data to add optimistic comment to')
        return oldData
      }

      // Safety: Create a deep copy to avoid mutation issues in production
      const newPages = oldData.pages.map((page: any) => ({ ...page }))

      if (newPages[0]?.docs && Array.isArray(newPages[0].docs)) {
        newPages[0] = {
          ...newPages[0],
          docs: [comment, ...newPages[0].docs],
          totalDocs: (newPages[0].totalDocs || 0) + 1,
        }
      }

      return {
        ...oldData,
        pages: newPages,
        pageParams: oldData.pageParams || [],
      }
    })
  }

  /**
   * Add optimistic nested comment to cache
   */
  addOptimisticNestedComment(
    comment: Comment,
    parentId: string,
    mainParams: CommentQueryParams,
    nestedParams: CommentQueryParams,
  ) {
    const mainQueryKeys = this.getMainQueryKeys(mainParams)
    const nestedQueryKeys = this.getNestedQueryKeys(parentId, nestedParams)

    // Check if nested comments are already loaded
    const existingNestedData = this.config.queryClient.getQueryData(nestedQueryKeys)
    const isNestedCommentsOpen = !!existingNestedData

    // Update the main comments query - only increment totalDocs, don't add to docs array
    this.config.queryClient.setQueryData(mainQueryKeys, (oldData: any) => {
      // Safety: Return early if no valid data
      if (!oldData || !oldData.pages || !Array.isArray(oldData.pages)) {
        console.warn('[CommentCache] No valid data to add nested comment to')
        return oldData
      }

      const updateParentComment = (comments: Comment[]): Comment[] => {
        // Safety: Ensure comments is an array
        if (!Array.isArray(comments)) return []

        return comments.map((existingComment) => {
          if (typeof existingComment === 'string') return existingComment

          if (existingComment.id === parentId) {
            // Only update totalDocs count, don't add to docs array
            // The docs array is managed by the nested comments query
            return {
              ...existingComment,
              replies: {
                ...(existingComment.replies || {}),
                totalDocs: (existingComment.replies?.totalDocs || 0) + 1,
              },
            }
          }

          // Recursively check nested comments
          if (existingComment.replies?.docs && Array.isArray(existingComment.replies.docs)) {
            return {
              ...existingComment,
              replies: {
                ...existingComment.replies,
                docs: updateParentComment(existingComment.replies.docs as Comment[]),
              },
            }
          }

          return existingComment
        })
      }

      const newPages = oldData.pages.map((page: any) => ({
        ...page,
        docs: updateParentComment(page.docs || []),
      }))

      return {
        ...oldData,
        pages: newPages,
        pageParams: oldData.pageParams || [],
      }
    })

    // If nested comments are open, also update the nested comments cache
    if (isNestedCommentsOpen) {
      this.config.queryClient.setQueryData(nestedQueryKeys, (oldNestedData: any) => {
        if (!oldNestedData) {
          return {
            pages: [
              {
                docs: [comment],
                totalDocs: 1,
                hasNextPage: false,
              },
            ],
            pageParams: [undefined],
          }
        }

        // Safety: Validate nested data structure
        if (!oldNestedData.pages || !Array.isArray(oldNestedData.pages)) {
          console.warn('[CommentCache] Invalid nested data structure')
          return oldNestedData
        }

        const newPages = oldNestedData.pages.map((page: any) => ({ ...page }))
        if (newPages[0]?.docs && Array.isArray(newPages[0].docs)) {
          newPages[0] = {
            ...newPages[0],
            docs: [comment, ...newPages[0].docs],
            totalDocs: (newPages[0].totalDocs || 0) + 1,
          }
        }

        return {
          ...oldNestedData,
          pages: newPages,
          pageParams: oldNestedData.pageParams || [],
        }
      })
    }
  }

  /**
   * Update optimistic comment with real server data
   */
  updateOptimisticComment(
    tempId: string,
    realComment: Comment,
    mainParams: CommentQueryParams,
    nestedParams: CommentQueryParams,
    isAdmin: boolean,
  ) {
    const mainQueryKeys = this.getMainQueryKeys(mainParams)

    // Update main comments cache - only update root level comments, not nested ones in replies.docs
    this.config.queryClient.setQueryData(mainQueryKeys, (oldData: any) => {
      if (!oldData) return oldData

      const now = new Date()
      // prevent the comment created at is in the future 
      const safeCreatedAt = (() => {
        try {
          const serverCreated = new Date(realComment.createdAt as any)
          return serverCreated > now ? now.toISOString() : realComment.createdAt
        } catch {
          return realComment.createdAt
        }
      })()

      const updateComment = (comments: Comment[]): Comment[] => {
        return comments.map((comment) => {
          if (typeof comment === 'string') return comment

          if (comment.id === tempId) {
            return {
              ...realComment,
              createdAt: safeCreatedAt,
              isOptimistic: false,
              isLoading: false,
              hasError: false,
              isAdmin: isAdmin,
            }
          }

          // Skip checking replies.docs since we don't store nested comments there anymore
          // Nested comments are only in their dedicated nested comments cache
          return comment
        })
      }

      const newPages = oldData.pages.map((page: any) => ({
        ...page,
        docs: updateComment(page.docs || []),
      }))

      return {
        ...oldData,
        pages: newPages,
      }
    })

    // If this is a nested comment, also update nested comments cache if it exists
    const parentId =
      typeof realComment.parent === 'object' ? realComment.parent?.id : realComment.parent

    if (parentId) {
      const nestedQueryKeys = this.getNestedQueryKeys(parentId, nestedParams)
      const existingNestedData = this.config.queryClient.getQueryData(nestedQueryKeys)

      if (existingNestedData) {
        this.config.queryClient.setQueryData(nestedQueryKeys, (oldNestedData: any) => {
          if (!oldNestedData) return oldNestedData

          const updateNestedComment = (comments: Comment[]): Comment[] => {
            return comments.map((comment) => {
              if (typeof comment === 'string') return comment

              if (comment.id === tempId) {
                return {
                  ...realComment,
                  isOptimistic: false,
                  isLoading: false,
                  hasError: false,
                }
              }

              return comment
            })
          }

          const newPages = oldNestedData.pages.map((page: any) => ({
            ...page,
            docs: updateNestedComment(page.docs || []),
          }))

          return {
            ...oldNestedData,
            pages: newPages,
          }
        })
      }
    }
  }

  /**
   * Mark optimistic comment as having an error (instead of removing it)
   */
  markOptimisticCommentAsError(
    tempId: string,
    mainParams: CommentQueryParams,
    nestedParams: CommentQueryParams,
    parentId?: string,
  ) {
    const mainQueryKeys = this.getMainQueryKeys(mainParams)

    // Update main comments cache
    this.config.queryClient.setQueryData(mainQueryKeys, (oldData: any) => {
      if (!oldData) return oldData

      const markCommentAsError = (comments: Comment[]): Comment[] => {
        return comments.map((comment) => {
          if (typeof comment === 'string') return comment

          if (comment.id === tempId) {
            return {
              ...comment,
              isOptimistic: true,
              isLoading: false,
              hasError: true,
            }
          }

          return comment
        })
      }

      const newPages = oldData.pages.map((page: any) => ({
        ...page,
        docs: markCommentAsError(page.docs || []),
      }))

      return {
        ...oldData,
        pages: newPages,
      }
    })

    // If this is a nested comment, also mark as error in nested comments cache if it's open
    if (parentId) {
      const nestedQueryKeys = this.getNestedQueryKeys(parentId, nestedParams)
      const existingNestedData = this.config.queryClient.getQueryData(nestedQueryKeys)

      if (existingNestedData) {
        this.config.queryClient.setQueryData(nestedQueryKeys, (oldNestedData: any) => {
          if (!oldNestedData) return oldNestedData

          const markNestedCommentAsError = (comments: Comment[]): Comment[] => {
            return comments.map((comment) => {
              if (typeof comment === 'string') return comment

              if (comment.id === tempId) {
                return {
                  ...comment,
                  isOptimistic: true,
                  isLoading: false,
                  hasError: true,
                }
              }

              return comment
            })
          }

          const newPages = oldNestedData.pages.map((page: any) => ({
            ...page,
            docs: markNestedCommentAsError(page.docs || []),
          }))

          return {
            ...oldNestedData,
            pages: newPages,
          }
        })
      }
    }
  }

  /**
   * Remove optimistic comment from cache (on error)
   */
  removeOptimisticComment(
    tempId: string,
    mainParams: CommentQueryParams,
    nestedParams: CommentQueryParams,
    parentId?: string,
  ) {
    const mainQueryKeys = this.getMainQueryKeys(mainParams)

    // Update main comments cache
    this.config.queryClient.setQueryData(mainQueryKeys, (oldData: any) => {
      if (!oldData) return oldData

      const removeComment = (comments: Comment[]): Comment[] => {
        return comments
          .filter((comment) => {
            if (typeof comment === 'string') return true
            return comment.id !== tempId
          })
          .map((comment) => {
            if (typeof comment === 'string') return comment

            // Update reply counts for parent comments (only decrement totalDocs)
            if (comment.id === parentId && comment.replies) {
              return {
                ...comment,
                replies: {
                  ...comment.replies,
                  totalDocs: Math.max(0, (comment.replies.totalDocs || 0) - 1),
                },
              }
            }

            // Skip checking replies.docs since we don't store nested comments there anymore
            return comment
          })
      }

      const newPages = oldData.pages.map((page: any) => ({
        ...page,
        docs: removeComment(page.docs || []),
        totalDocs: !parentId ? Math.max(0, (page.totalDocs || 0) - 1) : page.totalDocs,
      }))

      return {
        ...oldData,
        pages: newPages,
      }
    })

    // If this is a nested comment, also remove from nested comments cache if it's open
    if (parentId) {
      const nestedQueryKeys = this.getNestedQueryKeys(parentId, nestedParams)
      const existingNestedData = this.config.queryClient.getQueryData(nestedQueryKeys)

      if (existingNestedData) {
        this.config.queryClient.setQueryData(nestedQueryKeys, (oldNestedData: any) => {
          if (!oldNestedData) return oldNestedData

          const newPages = oldNestedData.pages.map((page: any) => ({
            ...page,
            docs: page.docs.filter((comment: Comment) => comment.id !== tempId),
            totalDocs: Math.max(0, (page.totalDocs || 0) - 1),
          }))

          return {
            ...oldNestedData,
            pages: newPages,
          }
        })
      }
    }
  }

  /**
   * Update comment reaction (like/unlike) in cache
   */
  updateCommentReaction(
    commentId: string,
    isLiked: boolean,
    totalLikes: number,
    mainParams: CommentQueryParams,
    nestedParams: CommentQueryParams,
    parentId?: string,
  ) {
    const mainQueryKeys = this.getMainQueryKeys(mainParams)

    // Update main comments cache
    this.config.queryClient.setQueryData(mainQueryKeys, (oldData: any) => {
      if (!oldData) return oldData

      const updateCommentReaction = (comments: Comment[]): Comment[] => {
        const commentIndex = comments.findIndex((comment) => {
          if (typeof comment === 'string') return false
          return comment.id === commentId
        })

        if (commentIndex === -1) return comments

        const updatedComments = [...comments]
        updatedComments[commentIndex] = {
          ...updatedComments[commentIndex],
          isLiked,
          totalLikes,
        }

        return updatedComments
      }

      const newPages = oldData.pages.map((page: any) => ({
        ...page,
        docs: updateCommentReaction(page.docs || []),
      }))

      return {
        ...oldData,
        pages: newPages,
      }
    })

    // If this is a nested comment, also update nested comments cache if it exists
    if (parentId) {
      const nestedQueryKeys = this.getNestedQueryKeys(parentId, nestedParams)
      const existingNestedData = this.config.queryClient.getQueryData(nestedQueryKeys)

      if (existingNestedData) {
        this.config.queryClient.setQueryData(nestedQueryKeys, (oldNestedData: any) => {
          if (!oldNestedData) return oldNestedData

          const updateNestedCommentReaction = (comments: Comment[]): Comment[] => {
            const commentIndex = comments.findIndex((comment) => {
              if (typeof comment === 'string') return false
              return comment.id === commentId
            })

            if (commentIndex === -1) return comments

            const updatedComments = [...comments]
            updatedComments[commentIndex] = {
              ...updatedComments[commentIndex],
              isLiked,
              totalLikes,
            }

            return updatedComments
          }

          const newPages = oldNestedData.pages.map((page: any) => ({
            ...page,
            docs: updateNestedCommentReaction(page.docs || []),
          }))

          return {
            ...oldNestedData,
            pages: newPages,
          }
        })
      }
    }
  }

  /**
   * Update comment content in cache (for edit)
   */
  updateCommentContent(
    commentId: string,
    newContent: string,
    mainParams: CommentQueryParams,
    nestedParams: CommentQueryParams,
    parentId?: string,
  ) {
    const mainQueryKeys = this.getMainQueryKeys(mainParams)

    // Update main comments cache
    this.config.queryClient.setQueryData(mainQueryKeys, (oldData: any) => {
      if (!oldData) return oldData

      const updateComment = (comments: Comment[]): Comment[] => {
        return comments.map((comment) => {
          if (typeof comment === 'string') return comment

          if (comment.id === commentId) {
            return {
              ...comment,
              content: newContent,
              updatedAt: new Date().toISOString(),
            }
          }

          return comment
        })
      }

      const newPages = oldData.pages.map((page: any) => ({
        ...page,
        docs: updateComment(page.docs || []),
      }))

      return {
        ...oldData,
        pages: newPages,
      }
    })

    // If this is a nested comment, also update nested comments cache if it's open
    if (parentId) {
      const nestedQueryKeys = this.getNestedQueryKeys(parentId, nestedParams)
      const existingNestedData = this.config.queryClient.getQueryData(nestedQueryKeys)

      if (existingNestedData) {
        this.config.queryClient.setQueryData(nestedQueryKeys, (oldNestedData: any) => {
          if (!oldNestedData) return oldNestedData

          const updateNestedComment = (comments: Comment[]): Comment[] => {
            return comments.map((comment) => {
              if (typeof comment === 'string') return comment

              if (comment.id === commentId) {
                return {
                  ...comment,
                  content: newContent,
                  updatedAt: new Date().toISOString(),
                }
              }

              return comment
            })
          }

          const newPages = oldNestedData.pages.map((page: any) => ({
            ...page,
            docs: updateNestedComment(page.docs || []),
          }))

          return {
            ...oldNestedData,
            pages: newPages,
          }
        })
      }
    }
  }

  /**
   * Delete comment from cache
   */
  deleteComment(
    commentId: string,
    mainParams: CommentQueryParams,
    nestedParams: CommentQueryParams,
    parentId?: string,
  ) {
    const mainQueryKeys = this.getMainQueryKeys(mainParams)

    // Update main comments cache
    this.config.queryClient.setQueryData(mainQueryKeys, (oldData: any) => {
      if (!oldData) return oldData

      const removeComment = (comments: Comment[]): Comment[] => {
        return comments
          .filter((comment) => {
            if (typeof comment === 'string') return true
            return comment.id !== commentId
          })
          .map((comment) => {
            if (typeof comment === 'string') return comment

            // Update reply counts for parent comments (only decrement totalDocs)
            if (comment.id === parentId && comment.replies) {
              return {
                ...comment,
                replies: {
                  ...comment.replies,
                  totalDocs: Math.max(0, (comment.replies.totalDocs || 0) - 1),
                },
              }
            }

            return comment
          })
      }

      const newPages = oldData.pages.map((page: any) => ({
        ...page,
        docs: removeComment(page.docs || []),
        totalDocs: !parentId ? Math.max(0, (page.totalDocs || 0) - 1) : page.totalDocs,
      }))

      return {
        ...oldData,
        pages: newPages,
      }
    })

    // If this is a nested comment, also remove from nested comments cache if it's open
    if (parentId) {
      const nestedQueryKeys = this.getNestedQueryKeys(parentId, nestedParams)
      const existingNestedData = this.config.queryClient.getQueryData(nestedQueryKeys)

      if (existingNestedData) {
        this.config.queryClient.setQueryData(nestedQueryKeys, (oldNestedData: any) => {
          if (!oldNestedData) return oldNestedData

          const newPages = oldNestedData.pages.map((page: any) => ({
            ...page,
            docs: page.docs.filter((comment: Comment) => {
              if (typeof comment === 'string') return true
              return comment.id !== commentId
            }),
            totalDocs: Math.max(0, (page.totalDocs || 0) - 1),
          }))

          return {
            ...oldNestedData,
            pages: newPages,
          }
        })
      }
    }
  }

  /**
   * Update total comments count in cache
   */
  updateTotalCommentsCount(increment: number, params: CommentQueryParams) {
    const totalCommentsQueryKey = this.getTotalCommentsQueryKeys(params)

    this.config.queryClient.setQueryData(
      totalCommentsQueryKey,
      (oldData: TotalCommentsResponse | undefined) => {
        if (!oldData) return oldData
        return {
          ...oldData,
          totalComments: Math.max(0, (oldData.totalComments || 0) + increment),
        }
      },
    )
  }

  /**
   * Set total comments count in cache (for delete operations with exact count)
   */
  setTotalCommentsCount(newCount: number, params: CommentQueryParams) {
    const totalCommentsQueryKey = this.getTotalCommentsQueryKeys(params)

    this.config.queryClient.setQueryData(
      totalCommentsQueryKey,
      (oldData: TotalCommentsResponse | undefined) => {
        if (!oldData) return { totalComments: newCount }
        return {
          ...oldData,
          totalComments: Math.max(0, newCount),
        }
      },
    )
  }

  /**
   * Invalidate total comments cache
   */
  invalidateTotalCommentsCache() {
    this.config.queryClient.invalidateQueries({
      queryKey: [
        commentQueryKeys['totalComments'].base(),
        this.config.documentId,
        this.config.relationTo,
      ],
    })
  }

  /**
   * Invalidate all comment caches for this document
   */
  invalidateAllCaches() {
    // Invalidate main comments
    this.config.queryClient.invalidateQueries({
      queryKey: [
        commentQueryKeys['comments'].base(),
        this.config.relationTo,
        this.config.documentId,
      ],
    })

    // Invalidate all nested comments for this document
    this.config.queryClient.invalidateQueries({
      queryKey: [commentQueryKeys['nested-comments'].base(), this.config.relationTo],
    })

    // Invalidate total comments
    this.invalidateTotalCommentsCache()
  }
}
