import MessageIcon from '@/assets/icons/message-icon.svg'
import { APP_TABS } from '@/constants/navigation.constant'
import { useAuthentication } from '@/hooks/auth/useAuthentication'
import { APP_ROUTES } from '@/routes/appRoutes'
import { primary } from '@/styles/_colors'
import { getDynamicTabBarHeight } from '@/styles/_variables'
import { LinkProps, Tabs, useRouter } from 'expo-router'
import React from 'react'
import { useTranslation } from 'react-i18next'
import { Platform, StyleSheet, TouchableOpacity, View } from 'react-native'
import { isTablet, scale } from 'react-native-size-scaling'
import { TabBarIcon } from './AppTabs'
import { CurvedTabBar } from './CurvedTabBar'
export const CustomAppTabs = () => {
  const { t } = useTranslation()
  const router = useRouter()
  const { status, user } = useAuthentication()

  const renderCircle = ({
    routeName,
    selectedTab,
    navigate,
  }: {
    routeName: string
    selectedTab: string
    navigate: (routeName: string) => void
  }) => {
    return (
      <TouchableOpacity
        style={Platform.OS === 'android' ? styles.circleButtonAndroid : styles.circleButton}
        onPress={() => navigate(routeName)}
        activeOpacity={0.7}
      >
        <View style={styles.circleInner}>
          <MessageIcon width={24} height={24} />
        </View>
      </TouchableOpacity>
    )
  }

  return (
    <Tabs
      initialRouteName="index"
      // Workaround for blank screen during tab transition animation (disable detachInactiveScreens on iOS)
      // Issue: https://github.com/expo/expo/issues/39514; https://github.com/react-navigation/react-navigation/issues/12755
      // TODO: Remove once the upstream issue is resolved
      detachInactiveScreens={Platform.OS === 'ios' ? false : true}
      screenOptions={{
        animation: 'fade',
        headerShown: false,
        tabBarActiveTintColor: primary[500],
        tabBarInactiveTintColor: '#8B8C99',
        sceneStyle: { backgroundColor: 'white' },
        tabBarStyle: {
          paddingBottom: 0,
        },
      }}
      tabBar={(props) => (
        <CurvedTabBar
          {...props}
          type="UP"
          height={getDynamicTabBarHeight()}
          circleWidth={46}
          bgColor="white"
          borderTopLeftRight
          shadowStyle={{
            ...styles.shadow,
            // ...(Platform.OS === 'android' && { elevation: 8 }),
          }}
          renderCircle={renderCircle}
          tabPositions={{
            [APP_TABS.HOME.name]: 'LEFT',
            [APP_TABS.MEDICAL_DICTIONARY.name!]: 'LEFT',
            [APP_TABS.CHAT.name!]: 'CENTER',
            [APP_TABS.PRODUCTS.name!]: 'RIGHT',
            [APP_TABS.POSTS.name!]: 'RIGHT',
          }}
        />
      )}
    >
      {/* Tab : Home (Trang chủ)  */}
      <Tabs.Screen
        name={APP_TABS.HOME.name}
        options={{
          title: t(APP_TABS.HOME.keyTranslate),
          tabBarIcon: ({ focused, color }) => (
            <TabBarIcon focused={focused} color={color} icon={APP_TABS.HOME.icon} />
          ),
        }}
      />
      {/* Tab : Từ điển */}
      <Tabs.Screen
        name={APP_TABS.MEDICAL_DICTIONARY.name}
        options={{
          title: t(APP_TABS.MEDICAL_DICTIONARY.keyTranslate),
          tabBarIcon: ({ focused, color }) => (
            <TabBarIcon focused={focused} color={color} icon={APP_TABS.MEDICAL_DICTIONARY.icon} />
          ),
        }}
      />
      {/* Tab Chat */}
      <Tabs.Screen
        name={APP_TABS.CHAT.name}
        options={{
          title: t(APP_TABS.CHAT.keyTranslate),
          tabBarLabel: t(APP_TABS.CHAT.keyTranslate),
          tabBarIcon: ({ focused, color }) => (
            <TabBarIcon
              isMainTab={true}
              focused={focused}
              color={color}
              icon={APP_TABS.CHAT.icon}
            />
          ),
        }}
        listeners={{
          tabPress: (e) => {
            if (status === 'loading') {
              e.preventDefault()
              return
            }
            if (status === 'unauthorized' || !user) {
              e.preventDefault()
              router.push({
                pathname: APP_ROUTES.LOGIN.path,
                params: {
                  redirect: APP_ROUTES.CHAT_BOT?.children?.CHAT_WOOT?.path,
                },
              } as LinkProps['href'])
            } else {
              e.preventDefault()
              router.push({
                pathname: APP_ROUTES.CHAT_BOT?.children?.CHAT_WOOT?.path,
              } as LinkProps['href'])
            }
          },
        }}
      />
      {/* Tab : Sổ tay Thuốc */}
      <Tabs.Screen
        name={APP_TABS.PRODUCTS.name}
        options={{
          title: t(APP_TABS.PRODUCTS.keyTranslate),
          tabBarIcon: ({ focused, color }) => (
            <TabBarIcon focused={focused} color={color} icon={APP_TABS.PRODUCTS.icon} />
          ),
        }}
      />
      {/* Tab : Tin tức */}
      <Tabs.Screen
        name={APP_TABS.POSTS.name}
        options={{
          title: t(APP_TABS.POSTS.keyTranslate),
          tabBarIcon: ({ focused, color }) => (
            <TabBarIcon focused={focused} color={color} icon={APP_TABS.POSTS.icon} />
          ),
        }}
      />
    </Tabs>
  )
}

const styles = StyleSheet.create({
  shadow: {
    shadowColor: '#DDDDDD',
    shadowOffset: {
      width: 0,
      height: 0,
    },
    shadowOpacity: 1,
    shadowRadius: 5,
  },

  tabItem: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    height: '100%',
  },
  tabText: {
    fontSize: 12,
    fontWeight: '500',
  },
  circleButton: {
    width: isTablet ? 50 : 46,
    height: isTablet ? 50 : 46,
    borderRadius: 9999,
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: primary[500],
    bottom: isTablet ? scale(24) : 20,
  },
  circleButtonAndroid: {
    width: 46,
    height: 46,
    borderRadius: 9999,
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: primary[500],
    bottom: isTablet ? scale(24) : 20,
  },
  circleInner: {
    width: '100%',
    height: '100%',
    alignItems: 'center',
    justifyContent: 'center',
  },
  circleText: {},
})
