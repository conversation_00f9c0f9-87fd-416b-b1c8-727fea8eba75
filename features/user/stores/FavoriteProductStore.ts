import { create } from 'zustand'
import { devtools } from 'zustand/middleware'

export interface FavoriteProductState {
  searchProductFilters: FavoriteSearchProductFilter
  tempSearchFilters: FavoriteTempSearchProductFilter
  hasActiveFilters: boolean
}

export enum FavoriteProductFilterType {
  AGE_GROUP = 'age_group',
  MEDICINE_TYPE = 'medicine_type',
  PRODUCT_TYPE = 'product_type',
  CATEGORY = 'category',
  SEARCH_TEXT = 'search_text',
}

export interface FavoriteProductFilterItem {
  id: string
  label: string
  type: FavoriteProductFilterType
}

export interface FavoriteSearchProductFilter {
  [FavoriteProductFilterType.AGE_GROUP]: FavoriteProductFilterItem[] | null
  [FavoriteProductFilterType.MEDICINE_TYPE]: FavoriteProductFilterItem[] | null
  [FavoriteProductFilterType.PRODUCT_TYPE]: FavoriteProductFilterItem[] | null
  [FavoriteProductFilterType.CATEGORY]: FavoriteProductFilterItem[] | null
  [FavoriteProductFilterType.SEARCH_TEXT]: string | null
}

export interface FavoriteTempSearchProductFilter {
  [FavoriteProductFilterType.AGE_GROUP]: FavoriteProductFilterItem[] | null
  [FavoriteProductFilterType.MEDICINE_TYPE]: FavoriteProductFilterItem[] | null
  [FavoriteProductFilterType.PRODUCT_TYPE]: FavoriteProductFilterItem[] | null
  [FavoriteProductFilterType.CATEGORY]: FavoriteProductFilterItem[] | null
}

export interface FavoriteProductActions {
  // Search product filter actions
  toggleFavoriteSearchProductFilter: (
    filterType: FavoriteProductFilterType,
    filter: FavoriteProductFilterItem,
  ) => void
  clearFavoriteSearchProductFilters: (filterType?: FavoriteProductFilterType) => void
  setFavoriteSearchTextValue: (searchTextValue: string) => void
  // Temporary search filter actions
  toggleFavoriteTempSearchFilter: (
    filterType: FavoriteProductFilterType,
    filter: FavoriteProductFilterItem,
  ) => void
  applyFavoriteTempSearchFilters: () => void
  resetFavoriteTempSearchFilters: () => void
  initFavoriteTempSearchFilters: () => void
  clearAllFavoriteSearchProductFiltersAndSearchText: () => void
  clearAllFavoriteTempSearchFiltersMedicineType: () => void
  clearAllFavoriteSearchFiltersMedicineType: () => void
  clearAllFavoriteTempSearchFilters: () => void
  clearAllFavoriteSearchProductFilters: () => void
  filterCategoriesByFavoriteProductType: (categories: { id: string; type: string }[]) => void
}

// Initial state for the favorite product store
const initialState: FavoriteProductState = {
  hasActiveFilters: false,
  searchProductFilters: {
    [FavoriteProductFilterType.AGE_GROUP]: null,
    [FavoriteProductFilterType.MEDICINE_TYPE]: null,
    [FavoriteProductFilterType.PRODUCT_TYPE]: null,
    [FavoriteProductFilterType.CATEGORY]: null,
    [FavoriteProductFilterType.SEARCH_TEXT]: null,
  },
  tempSearchFilters: {
    [FavoriteProductFilterType.AGE_GROUP]: null,
    [FavoriteProductFilterType.MEDICINE_TYPE]: null,
    [FavoriteProductFilterType.PRODUCT_TYPE]: null,
    [FavoriteProductFilterType.CATEGORY]: null,
  },
}

// Helper function to check if search filters are active
const hasActiveSearchFilters = (filters: FavoriteSearchProductFilter): boolean => {
  return Object.entries(filters).some(([key, value]) => {
    if (key === FavoriteProductFilterType.SEARCH_TEXT) {
      return false
    }
    return value && Array.isArray(value) && value.length > 0
  })
}

// Create the favorite product store with Zustand
export const useFavoriteProductStore = create<FavoriteProductState & FavoriteProductActions>()(
  devtools(
    (set, get) => ({
      ...initialState,
      // Search product filter actions
      toggleFavoriteSearchProductFilter: (
        filterType: FavoriteProductFilterType,
        filter: FavoriteProductFilterItem,
      ) => {
        set((state) => {
          // Handle different filter types
          if (filterType === FavoriteProductFilterType.SEARCH_TEXT) {
            return state
          }

          const currentFilters =
            (state.searchProductFilters[filterType] as FavoriteProductFilterItem[] | null) || []
          const isSelected = currentFilters.some(
            (f: FavoriteProductFilterItem) => f.id === filter.id,
          )

          const newSearchFilters = {
            ...state.searchProductFilters,
            [filterType]: isSelected
              ? currentFilters.filter((f: FavoriteProductFilterItem) => f.id !== filter.id)
              : [...currentFilters, filter],
          }

          return {
            searchProductFilters: newSearchFilters,
            hasActiveFilters: hasActiveSearchFilters(newSearchFilters),
          }
        })
      },
      clearFavoriteSearchProductFilters: (filterType?: FavoriteProductFilterType) => {
        set((state) => {
          const newSearchFilters = {
            ...state.searchProductFilters,
            ...(filterType
              ? { [filterType]: [] }
              : {
                  [FavoriteProductFilterType.AGE_GROUP]: [],
                  [FavoriteProductFilterType.MEDICINE_TYPE]: [],
                  [FavoriteProductFilterType.PRODUCT_TYPE]: [],
                  [FavoriteProductFilterType.CATEGORY]: [],
                }),
          }

          return {
            searchProductFilters: newSearchFilters,
            hasActiveFilters: hasActiveSearchFilters(newSearchFilters),
          }
        })
      },
      setFavoriteSearchTextValue: (searchTextValue: string) => {
        set((state) => {
          const newSearchFilters = {
            ...state.searchProductFilters,
            [FavoriteProductFilterType.SEARCH_TEXT]: searchTextValue,
          }

          return {
            searchProductFilters: newSearchFilters,
            hasActiveFilters: hasActiveSearchFilters(newSearchFilters),
          }
        })
      },
      // Temporary search filter actions
      toggleFavoriteTempSearchFilter: (
        filterType: FavoriteProductFilterType,
        filter: FavoriteProductFilterItem,
      ) => {
        set((state) => {
          // Only handle filter types that are arrays, not strings like SEARCH_TEXT
          if (filterType === FavoriteProductFilterType.SEARCH_TEXT) {
            return state
          }

          // Type assertion to ensure we're working with array types
          const currentTempFilters =
            (state.tempSearchFilters[filterType] as FavoriteProductFilterItem[] | null) || []
          const isSelected = currentTempFilters.some(
            (f: FavoriteProductFilterItem) => f.id === filter.id,
          )

          const newTempFilters = {
            ...state.tempSearchFilters,
            [filterType]: isSelected
              ? currentTempFilters.filter((f: FavoriteProductFilterItem) => f.id !== filter.id)
              : [...currentTempFilters, filter],
          }

          // If we're toggling a product type, filter out categories that don't match the selected product types
          if (filterType === FavoriteProductFilterType.PRODUCT_TYPE) {
            const selectedProductTypes =
              newTempFilters[FavoriteProductFilterType.PRODUCT_TYPE] || []
            const currentCategories = newTempFilters[FavoriteProductFilterType.CATEGORY] || []

            // Only filter categories if there are selected product types
            if (selectedProductTypes.length > 0 && currentCategories.length > 0) {
              // Note: We need to get the actual category data with type information to filter properly
              // Since we don't have access to the category data with type information in the store,
              // we'll need to handle this filtering in the component where we have access to productCategories
              // For now, we'll just return the new filters and handle the filtering in the component
            }
          }

          return {
            tempSearchFilters: newTempFilters,
          }
        })
      },
      applyFavoriteTempSearchFilters: () => {
        set((state) => {
          const newSearchFilters = {
            ...state.searchProductFilters,
            [FavoriteProductFilterType.AGE_GROUP]:
              state.tempSearchFilters[FavoriteProductFilterType.AGE_GROUP],
            [FavoriteProductFilterType.MEDICINE_TYPE]:
              state.tempSearchFilters[FavoriteProductFilterType.MEDICINE_TYPE],
            [FavoriteProductFilterType.PRODUCT_TYPE]:
              state.tempSearchFilters[FavoriteProductFilterType.PRODUCT_TYPE],
            [FavoriteProductFilterType.CATEGORY]:
              state.tempSearchFilters[FavoriteProductFilterType.CATEGORY],
            [FavoriteProductFilterType.SEARCH_TEXT]:
              state.searchProductFilters[FavoriteProductFilterType.SEARCH_TEXT], // Preserve search text
          }

          return {
            searchProductFilters: newSearchFilters,
            hasActiveFilters: hasActiveSearchFilters(newSearchFilters),
          }
        })
      },
      resetFavoriteTempSearchFilters: () => {
        set((state) => ({
          tempSearchFilters: {
            [FavoriteProductFilterType.AGE_GROUP]:
              state.searchProductFilters[FavoriteProductFilterType.AGE_GROUP],
            [FavoriteProductFilterType.MEDICINE_TYPE]:
              state.searchProductFilters[FavoriteProductFilterType.MEDICINE_TYPE],
            [FavoriteProductFilterType.PRODUCT_TYPE]:
              state.searchProductFilters[FavoriteProductFilterType.PRODUCT_TYPE],
            [FavoriteProductFilterType.CATEGORY]:
              state.searchProductFilters[FavoriteProductFilterType.CATEGORY],
          },
        }))
      },
      clearAllFavoriteTempSearchFilters: () => {
        set({
          tempSearchFilters: initialState.tempSearchFilters,
        })
      },
      initFavoriteTempSearchFilters: () => {
        set((state) => ({
          tempSearchFilters: {
            [FavoriteProductFilterType.AGE_GROUP]:
              state.searchProductFilters[FavoriteProductFilterType.AGE_GROUP],
            [FavoriteProductFilterType.MEDICINE_TYPE]:
              state.searchProductFilters[FavoriteProductFilterType.MEDICINE_TYPE],
            [FavoriteProductFilterType.PRODUCT_TYPE]:
              state.searchProductFilters[FavoriteProductFilterType.PRODUCT_TYPE],
            [FavoriteProductFilterType.CATEGORY]:
              state.searchProductFilters[FavoriteProductFilterType.CATEGORY],
          },
        }))
      },
      clearAllFavoriteSearchProductFiltersAndSearchText: () => {
        set({
          searchProductFilters: initialState.searchProductFilters,
          tempSearchFilters: initialState.tempSearchFilters,
          hasActiveFilters: false,
        })
      },
      clearAllFavoriteTempSearchFiltersMedicineType: () => {
        set((state) => ({
          tempSearchFilters: {
            ...state.tempSearchFilters,
            [FavoriteProductFilterType.MEDICINE_TYPE]: [],
          },
        }))
      },
      clearAllFavoriteSearchFiltersMedicineType: () => {
        set((state) => {
          const newSearchFilters = {
            ...state.searchProductFilters,
            [FavoriteProductFilterType.MEDICINE_TYPE]: [],
          }

          return {
            searchProductFilters: newSearchFilters,
            hasActiveFilters: hasActiveSearchFilters(newSearchFilters),
          }
        })
      },
      clearAllFavoriteSearchProductFilters: () => {
        set((state) => ({
          searchProductFilters: {
            ...initialState.searchProductFilters,
            [FavoriteProductFilterType.SEARCH_TEXT]:
              state.searchProductFilters[FavoriteProductFilterType.SEARCH_TEXT],
          },
          tempSearchFilters: {
            ...state.tempSearchFilters,
          },
          hasActiveFilters: false,
        }))
      },
      filterCategoriesByFavoriteProductType: (categories: { id: string; type: string }[]) => {
        set((state) => {
          const selectedProductTypes =
            state.tempSearchFilters[FavoriteProductFilterType.PRODUCT_TYPE] || []
          const currentCategories =
            state.tempSearchFilters[FavoriteProductFilterType.CATEGORY] || []

          // Only filter categories if there are selected product types
          if (selectedProductTypes.length > 0 && currentCategories.length > 0) {
            // Get the IDs of selected product types
            const selectedProductTypeIds = selectedProductTypes.map((pt) => pt.id)

            // Filter categories to only keep those that match the selected product types
            const filteredCategories = currentCategories.filter((category) => {
              // Find the category data to get its type
              const categoryData = categories.find((cat) => cat.id === category.id)
              return categoryData && selectedProductTypeIds.includes(categoryData.type)
            })

            return {
              tempSearchFilters: {
                ...state.tempSearchFilters,
                [FavoriteProductFilterType.CATEGORY]: filteredCategories,
              },
            }
          }

          return state
        })
      },
    }),
    {
      name: 'favorite-product-store',
    },
  ),
)
