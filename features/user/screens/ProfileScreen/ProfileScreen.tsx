import NotiBellIcon from '@/assets/icons/gold-bell-icon.svg'
import { NotificationRequestBox } from '@/components/NotificationRequestBox/NotificationRequestBox'
import { Text } from '@/components/ui/Text/Text'
import { useGetUserUnviewedNotificationCount } from '@/features/notification/hooks/query/useGetUserUnviewedNotificationCount'
import { useUnifiedNotificationStore } from '@/features/notification/stores/UnifiedNotificationStore'
import { useAuthentication } from '@/hooks/auth/useAuthentication'
import { APP_ROUTES, AppRoutesEnum } from '@/routes/appRoutes'
import { Link, LinkProps } from 'expo-router'
import React from 'react'
import { useTranslation } from 'react-i18next'
import { ScrollView, TouchableOpacity, View } from 'react-native'
import { SafeAreaView } from 'react-native-safe-area-context'
import { useShallow } from 'zustand/react/shallow'
import { CompanyInfo } from '../../components/Profile/CompanyInfo/CompanyInfo'
import { ProfileMenu } from '../../components/Profile/ProfileMenu/ProfileMenu'
import { SummaryInfo } from '../../components/Profile/SummaryInfo/SummaryInfo'
export const ProfileScreen = () => {
  const { user, status } = useAuthentication()
  const { t } = useTranslation()
  const { userUnviewedNotificationCount } = useGetUserUnviewedNotificationCount({
    useQueryOptions: {
      enabled: user && status === 'success',
      staleTime: 5 * 60 * 1000,
    },
  })
  const { permissionGranted } = useUnifiedNotificationStore(
    useShallow((state) => ({
      permissionGranted: state.permissionGranted,
    })),
  )
  return (
    <SafeAreaView className="flex-1 bg-white" edges={['left', 'right', 'bottom']}>
      <View className="flex flex-1 flex-col justify-between">
        <ScrollView className="flex-1">
          <View className="flex flex-col gap-y-3 p-3">
            <SummaryInfo />
            {!permissionGranted && <NotificationRequestBox />}
            <Link
              href={
                APP_ROUTES.NOTIFICATIONS.children?.[AppRoutesEnum.USER_PUSH_NOTIFICATIONS]
                  .path as LinkProps['href']
              }
              asChild
            >
              <TouchableOpacity className="">
                <View className="flex flex-row items-center justify-between rounded-lg bg-custom-warning-200 p-4">
                  <View className="flex flex-row items-center gap-x-2">
                    <NotiBellIcon width={24} height={24} />
                    <Text size="body6" variant="default">
                      {t('MES-263')}
                    </Text>
                  </View>
                  <View>
                    {userUnviewedNotificationCount &&
                      userUnviewedNotificationCount.unviewedNotificationsCount > 0 && (
                        <View className=" flex items-center  justify-center rounded-full bg-red-500 px-[6px] py-[1px] ">
                          <Text size="body8" variant="white">
                            {userUnviewedNotificationCount.unviewedNotificationsCount <= 9
                              ? userUnviewedNotificationCount.unviewedNotificationsCount
                              : '9+'}
                          </Text>
                        </View>
                      )}
                  </View>
                </View>
              </TouchableOpacity>
            </Link>

            <ProfileMenu />
          </View>
        </ScrollView>

        <View className="px-3 ">
          <CompanyInfo />
        </View>
      </View>
    </SafeAreaView>
  )
}
