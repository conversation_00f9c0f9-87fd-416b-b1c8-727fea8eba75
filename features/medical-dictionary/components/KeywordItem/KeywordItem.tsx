import ArchiveIcon from '@/assets/icons/archive-icon.svg'
import ArchiveTickIcon from '@/assets/icons/archive-tick-icon.svg'
import { Text } from '@/components/ui/Text/Text'
import { LocaleEnum } from '@/enums/locale.enum'
import { useUpdateFavoriteKeyword } from '@/hooks/query/keyword/useUpdateFavoriteKeyword'
import { APP_ROUTES, AppRoutesEnum } from '@/routes/appRoutes'
import { LocalizeField } from '@/types/global.type'
import { KeywordV2 } from '@/types/keyword.type'
import { cn } from '@/utils/cn'
import * as Haptics from 'expo-haptics'
import { Link } from 'expo-router'
import { useCallback, useState } from 'react'
import { ActivityIndicator, TouchableOpacity, View } from 'react-native'
interface KeywordItemProps {
  keyword: KeywordV2
  isShowArchiveButton?: boolean
  onUpdateFavoriteKeywordSuccess?: (keyword: KeywordV2, action?: 'add' | 'remove') => void
  customParams?: Record<string, string>
  renderCustomRightCard?: React.ReactNode
  onPress?: (keyword: KeywordV2) => void
  navigationMode?: 'replace' | 'push'
}
export const KeywordItem = ({
  keyword,
  isShowArchiveButton = false,
  onUpdateFavoriteKeywordSuccess,
  customParams,
  renderCustomRightCard,
  onPress,
}: KeywordItemProps) => {
  const { name, hiragana } = keyword
  const localizedName = name as unknown as LocalizeField<string>
  const [isFavoriteKeyword, setIsFavoriteKeyword] = useState<boolean>(Boolean(keyword.isFavorite))

  const { updateFavoriteKeywordMutation, isUpdateFavoriteKeywordPending } =
    useUpdateFavoriteKeyword()
  const handleUpdateFavoriteKeyword = useCallback(
    (id: string, isFavorite: boolean) => {
      const type = isFavorite ? 'delete' : 'add'
      updateFavoriteKeywordMutation(
        { id, type: isFavorite ? 'delete' : 'add' },
        {
          onSuccess: () => {
            setIsFavoriteKeyword(type === 'add' ? true : false)
            onUpdateFavoriteKeywordSuccess?.(keyword, type === 'add' ? 'add' : 'remove')
          },
        },
      )
    },
    [updateFavoriteKeywordMutation, keyword.id, keyword.isFavorite],
  )

  return (
    <Link
      href={{
        pathname:
          APP_ROUTES.MEDICAL_DICTIONARY?.children?.[
            AppRoutesEnum.MEDICAL_DICTIONARY_KEYWORD_DETAILS
          ]?.path,
        params: {
          id: keyword.id,
          ...customParams,
        },
      }}
      // push={navigationMode === 'push'}
      asChild
      onPress={() => {
        Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light)
        onPress?.(keyword)
      }}
    >
      <TouchableOpacity className="flex  flex-row items-start justify-between gap-x-2 rounded-lg bg-custom-background-hover p-3">
        <View className="flex flex-1 flex-col gap-y-2">
          <View className="flex flex-col  gap-x-2 overflow-hidden">
            <Text size="body10" variant="primary" numberOfLines={2} className="line-clamp-2 ">
              {localizedName[LocaleEnum.JA]}
            </Text>
            {hiragana && (
              <Text size="body7" variant="subdued" numberOfLines={2} className="line-clamp-2 ">
                /{hiragana}/
              </Text>
            )}
          </View>
          <View className="flex flex-row gap-x-2">
            <Text size="body7" variant="default" numberOfLines={2} className="line-clamp-2 ">
              {localizedName[LocaleEnum.VI]}
            </Text>
          </View>
        </View>
        {renderCustomRightCard ? (
          renderCustomRightCard
        ) : (
          <>
            {isShowArchiveButton && (
              <TouchableOpacity
                className={cn('shrink-0', isUpdateFavoriteKeywordPending && 'opacity-50')}
                onPress={(e) => {
                  e.stopPropagation()
                  Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light)
                  handleUpdateFavoriteKeyword(keyword.id, isFavoriteKeyword)
                }}
                disabled={isUpdateFavoriteKeywordPending}
              >
                {isUpdateFavoriteKeywordPending ? (
                  <ActivityIndicator size="small" />
                ) : (
                  <>
                    {isFavoriteKeyword ? (
                      <ArchiveTickIcon width={20} height={20} />
                    ) : (
                      <ArchiveIcon width={20} height={20} />
                    )}
                  </>
                )}
              </TouchableOpacity>
            )}
          </>
        )}
      </TouchableOpacity>
    </Link>
  )
}
