// App.jsx
import { danger, primary, success, text } from '@/styles/_colors'
import { BaseToast, ErrorToast } from 'react-native-toast-message'

/*
  1. Create the config
*/
export const toastConfig = {
  /*
    Overwrite 'success' type,
    by modifying the existing `BaseToast` component
  */
  baseToast: (props: any) => (
    <BaseToast
      {...props}
      style={{ borderLeftColor: primary[500] }}
      text1Style={{
        fontSize: 16,
        fontWeight: '600',
        color: primary[500],
      }}
      text2Style={{
        fontSize: 14,
        color: text.subdued,
      }}
      contentContainerStyle={{ paddingHorizontal: 15 }}
    />
  ),

  success: (props: any) => (
    <BaseToast
      {...props}
      style={{ borderLeftColor: success[500] }}
      contentContainerStyle={{ paddingHorizontal: 15 }}
      text1Style={{
        fontSize: 16,
        fontWeight: '600',
        color: success[500],
      }}
      text2Style={{
        fontSize: 14,
        color: text.subdued,
      }}
    />
  ),
  /*
    Overwrite 'error' type,
    by modifying the existing `ErrorToast` component
  */
  error: (props: any) => (
    <ErrorToast
      {...props}
      style={{ borderLeftColor: danger[500] }}
      contentContainerStyle={{ paddingHorizontal: 15 }}
      text1Style={{
        fontSize: 16,
        fontWeight: '600',
        color: danger[500],
      }}
      text2Style={{
        fontSize: 14,
        color: text.subdued,
      }}
    />
  ),
}
