import { getCenterButtonLabelBottomSpacing, getDynamicTabBarHeight } from '@/styles/_variables'
import { BottomTabBarProps } from '@react-navigation/bottom-tabs'
import React, { useCallback, useMemo, useState } from 'react'
import { Dimensions, I18nManager, Platform, View } from 'react-native'
import { useSafeAreaInsets } from 'react-native-safe-area-context'
import { scale } from 'react-native-size-scaling'
import { styles } from '../CurvedBottomBar/components/BottomBarView/styles'
import { CurvedViewComponent } from '../CurvedBottomBar/components/CurvedView/curvedView'
import { useDeviceOrientation } from '../CurvedBottomBar/hooks/useDeviceOrientation'
import { getPathDown } from '../CurvedBottomBar/utils/pathDown'
import { getPathUp } from '../CurvedBottomBar/utils/pathup'
import { HapticTab } from '../HapticTab'
import { Text } from '../ui/Text/Text'

const { width: maxW } = Dimensions.get('window')

/**
 * Hook to calculate safe spacing for content to prevent it from being overridden by the curved tab bar
 * @param type - Tab bar type ('UP' or 'DOWN'), defaults to 'UP'
 * @param customHeight - Custom tab bar height, if not provided uses getDynamicTabBarHeight()
 * @returns Total safe spacing including tab bar height, curve height (if UP), and safe area inset
 */
export const useCurvedTabBarSafeSpacing = (
  type: 'UP' | 'DOWN' = 'UP',
  customHeight?: number,
): number => {
  const inset = useSafeAreaInsets()
  const tabBarHeight = customHeight ?? getDynamicTabBarHeight()

  // Clamp height between 45 and 80 (same logic as in CurvedTabBar)
  const getTabbarHeight = useMemo(() => {
    return tabBarHeight
  }, [tabBarHeight])

  // Calculate total height: scaled tab bar height + curve height (if UP) + safe area bottom
  const safeSpacing = useMemo(() => {
    const scaledHeight = scale(getTabbarHeight)
    const curveHeight = type === 'UP' ? scale(30) : 0
    return scaledHeight + curveHeight + inset.bottom
  }, [getTabbarHeight, type, inset.bottom])

  return safeSpacing
}

export interface CurvedTabBarProps extends BottomTabBarProps {
  type?: 'DOWN' | 'UP'
  circlePosition?: 'CENTER' | 'LEFT' | 'RIGHT'
  style?: any
  width?: number | null
  height?: number
  circleWidth?: number
  bgColor?: string
  borderTopLeftRight?: boolean
  shadowStyle?: any
  borderColor?: string
  borderWidth?: number
  renderCircle?: (props: {
    routeName: string
    selectedTab: string
    navigate: (routeName: string) => void
  }) => React.ReactElement
  tabPositions?: Record<string, 'LEFT' | 'RIGHT' | 'CENTER' | 'CIRCLE'>
}

export const CurvedTabBar: React.FC<CurvedTabBarProps> = ({
  state,
  descriptors,
  navigation,
  type = 'DOWN',
  circlePosition = 'CENTER',
  style,
  width = null,
  height = 55,
  circleWidth = 50,
  bgColor = 'gray',
  borderTopLeftRight = false,
  shadowStyle,
  borderColor = 'gray',
  borderWidth = 0,
  renderCircle,
  tabPositions = {},
}) => {
  const [maxWidth, setMaxWidth] = useState<number>(width || maxW)
  const orientation = useDeviceOrientation()
  const inset = useSafeAreaInsets()
  const selectedTab = useMemo(() => {
    const route = state.routes[state.index]
    return route.name
  }, [state])

  React.useEffect(() => {
    const { width: w } = Dimensions.get('window')
    if (!width) {
      setMaxWidth(w)
    }
  }, [orientation, width])

  const getCircleWidth = useMemo(() => {
    return circleWidth
  }, [circleWidth])

  const getTabbarHeight = useMemo(() => {
    return height < 45 ? 45 : height > 80 ? 80 : height
  }, [height])

  const path = useMemo(() => {
    return type === 'DOWN'
      ? getPathDown(
          maxWidth,
          scale(getTabbarHeight),
          getCircleWidth,
          borderTopLeftRight,
          !I18nManager.isRTL
            ? circlePosition
            : circlePosition === 'LEFT'
              ? 'RIGHT'
              : circlePosition === 'RIGHT'
                ? 'LEFT'
                : 'CENTER',
        )
      : getPathUp(
          maxWidth,
          scale(getTabbarHeight) + 30,
          getCircleWidth,
          borderTopLeftRight,
          !I18nManager.isRTL
            ? circlePosition
            : circlePosition === 'LEFT'
              ? 'RIGHT'
              : circlePosition === 'RIGHT'
                ? 'LEFT'
                : 'CENTER',
        )
  }, [borderTopLeftRight, circlePosition, getCircleWidth, getTabbarHeight, maxWidth, type])

  const navigate = useCallback(
    (routeName: string) => {
      const event = navigation.emit({
        type: 'tabPress',
        target: routeName,
        canPreventDefault: true,
      })

      if (!event.defaultPrevented) {
        navigation.navigate(routeName)
      }
    },
    [navigation],
  )

  // Group routes by position
  const groupedRoutes = useMemo(() => {
    const centerRoute = state.routes.find((route) => {
      const position = tabPositions[route.name]
      return position === 'CIRCLE' || position === 'CENTER'
    })
    const leftRoutes = state.routes.filter((route) => tabPositions[route.name] === 'LEFT')
    const rightRoutes = state.routes.filter((route) => tabPositions[route.name] === 'RIGHT')
    const defaultRoutes = state.routes.filter((route) => {
      const position = tabPositions[route.name]
      return (
        !position ||
        (position !== 'LEFT' &&
          position !== 'RIGHT' &&
          position !== 'CENTER' &&
          position !== 'CIRCLE')
      )
    })

    return { centerRoute, leftRoutes, rightRoutes, defaultRoutes }
  }, [state.routes, tabPositions])

  // Calculate dynamic bottom spacing for center button label with platform-specific adjustment
  const centerButtonLabelBottomSpacing = useMemo(() => {
    const platform =
      Platform.OS === 'ios' ? 'ios' : Platform.OS === 'android' ? 'android' : undefined
    return scale(getCenterButtonLabelBottomSpacing(getTabbarHeight, platform))
  }, [getTabbarHeight])

  const _renderButtonCenter = useCallback(() => {
    if (!groupedRoutes.centerRoute || !renderCircle) {
      return null
    }

    const centerRoute = groupedRoutes.centerRoute
    const centerRouteName = centerRoute.name
    const { options } = descriptors[centerRoute.key]
    const isFocused = state.index === state.routes.findIndex((r) => r.key === centerRoute.key)

    // Create a navigate function that triggers listeners
    const navigateWithListeners = (routeName: string) => {
      // Create an event object that listeners can prevent
      let defaultPrevented = false
      const event = {
        preventDefault: () => {
          defaultPrevented = true
        },
        target: centerRoute.key,
        data: undefined,
        type: 'tabPress' as const,
      }

      // Check if listeners are defined in the route's options
      const routeOptions = descriptors[centerRoute.key].options as any

      if (routeOptions.listeners && routeOptions.listeners.tabPress) {
        try {
          routeOptions.listeners.tabPress(event)
        } catch (error) {
          console.error('Error calling listener:', error)
        }
      }

      // Also emit the event normally
      const emitResult = navigation.emit({
        type: 'tabPress',
        target: centerRoute.key,
        canPreventDefault: true,
      })

      const wasPrevented = defaultPrevented || (emitResult as any)?.defaultPrevented || false

      // Only navigate if event wasn't prevented
      if (!wasPrevented) {
        navigate(routeName)
      }
    }

    const circleButton = renderCircle({
      routeName: centerRouteName,
      selectedTab,
      navigate: navigateWithListeners,
    })

    // Add label below center button if available
    const hasLabel = options.tabBarLabel || options.title
    const labelText = options.tabBarLabel
      ? typeof options.tabBarLabel === 'string'
        ? options.tabBarLabel
        : ''
      : options.title || ''

    if (hasLabel && labelText) {
      return (
        <View className="flex flex-col items-center justify-center">
          <View
            style={{
              display: 'flex',
              flexDirection: 'column',
              alignItems: 'center',
              justifyContent: 'center',
              height: scale(getTabbarHeight),
              width: scale(getCircleWidth) + scale(20),
              position: 'relative',
            }}
          >
            {circleButton}
            <View
              pointerEvents="none"
              style={{
                position: 'absolute',
                bottom: 0,
                left: 0,
                right: 0,
                alignItems: 'center',
                display: 'flex',
                flexDirection: 'column',
                justifyContent: 'center',
                width: '100%',
                height: '100%',
                backgroundColor: 'transparent',
              }}
            >
              <View className="flex flex-col items-center justify-center " style={{}}>
                <View style={{ width: 24, height: 24 }}></View>
                <View className="mt-2">
                  <Text size="body11" variant={isFocused ? 'primary' : 'subdued'} numberOfLines={1}>
                    {labelText}
                  </Text>
                </View>
              </View>
            </View>
          </View>
        </View>
      )
    }

    return circleButton
    //eslint-disable-next-line react-hooks/exhaustive-deps
  }, [
    groupedRoutes,
    renderCircle,
    selectedTab,
    navigate,
    descriptors,
    navigation,
    state,
    getTabbarHeight,
    getCircleWidth,
    centerButtonLabelBottomSpacing,
  ])

  const _renderTabIcon = useCallback(
    (routes: typeof state.routes) => {
      return (
        <View style={[styles.rowLeft, { height: scale(getTabbarHeight) }]}>
          {routes.map((route) => {
            const { options } = descriptors[route.key]
            const isFocused = state.index === state.routes.findIndex((r) => r.key === route.key)

            const onPress = () => {
              // Create an event object that listeners can prevent
              let defaultPrevented = false
              const event = {
                preventDefault: () => {
                  defaultPrevented = true
                },
                target: route.key,
                data: undefined,
                type: 'tabPress' as const,
              }

              // Check if listeners are defined in the route's options
              // Listeners from Tabs.Screen are stored in descriptors[route.key].options.listeners
              const routeOptions = descriptors[route.key].options as any

              if (routeOptions.listeners && routeOptions.listeners.tabPress) {
                try {
                  // Call the listener directly
                  routeOptions.listeners.tabPress(event)
                } catch (error) {
                  console.error('Error calling listener:', error)
                }
              }

              // Also try to access listeners from navigation object (internal React Navigation storage)
              const routeNavigation = descriptors[route.key].navigation as any

              // React Navigation stores listeners internally - try different possible locations
              if (routeNavigation._listeners) {
                const listeners = routeNavigation._listeners['tabPress']
                if (listeners && listeners.length > 0) {
                  listeners.forEach((listener: any) => {
                    try {
                      listener(event)
                    } catch (error) {
                      console.error('Error calling listener from _listeners:', error)
                    }
                  })
                }
              }

              // Also emit the event normally (in case React Navigation handles it)
              const emitResult = navigation.emit({
                type: 'tabPress',
                target: route.key,
                canPreventDefault: true,
              })

              // Check if default was prevented
              const wasPrevented =
                defaultPrevented || (emitResult as any)?.defaultPrevented || false

              // Only navigate if the tab isn't already focused and event wasn't prevented
              if (!isFocused && !wasPrevented) {
                navigation.navigate(route.name)
              }
            }

            const onLongPress = () => {
              navigation.emit({
                type: 'tabLongPress',
                target: route.key,
              })
            }

            return (
              <View key={route.key} style={styles.flex1}>
                {options.tabBarButton ? (
                  options.tabBarButton({
                    onPress,
                    onLongPress,
                    accessibilityState: { selected: isFocused },
                  } as any)
                ) : (
                  <HapticTab style={styles.itemTab} onPress={onPress} onLongPress={onLongPress}>
                    {options.tabBarIcon &&
                      options.tabBarIcon({
                        focused: isFocused,
                        color: isFocused
                          ? options.tabBarActiveTintColor || '#000'
                          : options.tabBarInactiveTintColor || '#999',
                        size: 24,
                      })}
                    {(options.tabBarLabel || options.title) && (
                      <View>
                        {options.tabBarLabel ? (
                          typeof options.tabBarLabel === 'string' ? (
                            <View className="mt-1 overflow-hidden text-ellipsis whitespace-nowrap">
                              <Text
                                size="body11"
                                variant={isFocused ? 'primary' : 'subdued'}
                                numberOfLines={1}
                              >
                                {options.tabBarLabel}
                              </Text>
                            </View>
                          ) : (
                            <></>
                          )
                        ) : (
                          <View className="mt-1 overflow-hidden text-ellipsis whitespace-nowrap">
                            <Text
                              size="body11"
                              variant={isFocused ? 'primary' : 'subdued'}
                              numberOfLines={1}
                            >
                              {options.title}
                            </Text>
                          </View>
                        )}
                      </View>
                    )}
                  </HapticTab>
                )}
              </View>
            )
          })}
        </View>
      )
    },
    //eslint-disable-next-line react-hooks/exhaustive-deps
    [descriptors, getTabbarHeight, navigation, state, tabPositions],
  )

  const renderPosition = useCallback(() => {
    const { centerRoute, leftRoutes, rightRoutes, defaultRoutes } = groupedRoutes

    // If center route exists and renderCircle is provided, render it as center button
    // Otherwise, treat all routes as regular tabs
    const allLeftRoutes = [...leftRoutes, ...(circlePosition === 'LEFT' ? defaultRoutes : [])]
    const allRightRoutes = [...rightRoutes, ...(circlePosition === 'RIGHT' ? defaultRoutes : [])]
    const allCenterRoutes = circlePosition === 'CENTER' ? defaultRoutes : []

    // Include center route in regular tabs if renderCircle is not provided
    const centerRoutesForTabs = centerRoute && renderCircle ? [] : centerRoute ? [centerRoute] : []

    if (circlePosition === 'LEFT' && centerRoute && renderCircle) {
      return (
        <>
          <View style={{ marginLeft: scale(getCircleWidth) / 2 }}>{_renderButtonCenter()}</View>
          {_renderTabIcon([...allLeftRoutes, ...allRightRoutes, ...centerRoutesForTabs])}
        </>
      )
    }

    if (circlePosition === 'RIGHT' && centerRoute && renderCircle) {
      return (
        <>
          {_renderTabIcon([...allLeftRoutes, ...allRightRoutes, ...centerRoutesForTabs])}
          <View style={{ marginRight: scale(getCircleWidth) / 2 }}>{_renderButtonCenter()}</View>
        </>
      )
    }

    if (circlePosition === 'CENTER' && centerRoute && renderCircle) {
      return (
        <>
          {_renderTabIcon([...allLeftRoutes, ...allCenterRoutes])}
          <View style={{ marginHorizontal: scale(12) }}>{_renderButtonCenter()}</View>
          {_renderTabIcon([...allRightRoutes, ...centerRoutesForTabs])}
        </>
      )
    }

    // If no center button, render all tabs normally
    return (
      <>
        {_renderTabIcon([
          ...allLeftRoutes,
          ...allCenterRoutes,
          ...centerRoutesForTabs,
          ...allRightRoutes,
        ])}
      </>
    )
  }, [
    circlePosition,
    groupedRoutes,
    _renderButtonCenter,
    _renderTabIcon,
    getCircleWidth,
    renderCircle,
  ])

  const curvedViewShadowStyle = useMemo(() => {
    if (Platform.OS === 'android') {
      // Remove elevation from CurvedViewComponent on Android to prevent layering issues
      // Elevation is applied to container instead
      const { elevation: _elevation, ...rest } = shadowStyle || {}
      return rest
    }
    return shadowStyle || {}
  }, [shadowStyle])

  return (
    <View style={[styles.container, style]}>
      <CurvedViewComponent
        style={curvedViewShadowStyle}
        width={maxWidth}
        height={scale(getTabbarHeight) + (type === 'DOWN' ? 0 : scale(30))}
        bgColor={bgColor}
        path={path}
        borderColor={Platform.OS === 'android' ? '#eee' : borderColor}
        borderWidth={Platform.OS === 'android' ? 1 : borderWidth}
      />
      <View
        style={[
          styles.main,
          { width: maxWidth, paddingHorizontal: scale(8) },
          type === 'UP' && styles.top30,
        ]}
      >
        {renderPosition()}
      </View>
      {/* Platform-specific bottom padding: Android needs minimum padding to match iOS appearance */}
      <View
        style={{
          marginTop: -4,
          height:
            Platform.OS === 'android'
              ? Math.max(inset.bottom + 2, scale(8)) // Minimum 8px spacing for Android
              : inset.bottom + 2,
          backgroundColor: 'white',
        }}
      />
    </View>
  )
}
