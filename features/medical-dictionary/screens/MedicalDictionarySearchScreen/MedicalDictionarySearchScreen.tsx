import { useEffect } from 'react'

import { SafeAreaView } from 'react-native-safe-area-context'
import { useShallow } from 'zustand/react/shallow'
import { SearchKeywords } from '../../components/SearchKeywords/SearchKeywords'
import { useMedicalDictionaryStore } from '../../stores/MedicalDictionaryStore'
export const MedicalDictionarySearchScreen = () => {
  const { clearAllFiltersAndSearchText, searchTextValue } = useMedicalDictionaryStore(
    useShallow((state) => ({
      clearAllFiltersAndSearchText: state.clearAllFiltersAndSearchText,
      searchTextValue: state.searchText,
    })),
  )

  useEffect(() => {
    return () => {
      clearAllFiltersAndSearchText()
    }
  }, [])

  return (
    <SafeAreaView className="flex-1 bg-white" edges={['left', 'right', 'bottom']}>
      <SearchKeywords />
    </SafeAreaView>
  )
}
