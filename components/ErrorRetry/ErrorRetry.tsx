import { Text } from '@/components/ui/Text/Text'
import { useTranslation } from 'react-i18next'
import { TouchableOpacity, View } from 'react-native'

interface ErrorRetryProps {
  onRetry?: () => void
  onDismiss?: () => void
  showDismiss?: boolean
  className?: string
}

export const ErrorRetry = ({
  onRetry,
  onDismiss,
  showDismiss = false,
  className = '',
}: ErrorRetryProps) => {
  const { t } = useTranslation()

  return (
    <View
      className={`flex flex-row items-center justify-between rounded-lg border border-red-200 bg-red-50 px-3 py-2 ${className}`}
    >
      <Text size="body6" className="flex-1 text-red-600">
        {t('MES-197')}
      </Text>
      <View className="flex flex-row items-center gap-x-2">
        <TouchableOpacity onPress={onRetry} className="rounded-md bg-red-500 px-3 py-1">
          <Text size="body6" className="text-white">
            {t('MES-230') || 'Retry'}
          </Text>
        </TouchableOpacity>
        {showDismiss && (
          <TouchableOpacity
            onPress={onDismiss}
            className="rounded-md border border-red-300 px-3 py-1"
          >
            <Text size="body6" className="text-red-600">
              {t('MES-224') || 'Cancel'}
            </Text>
          </TouchableOpacity>
        )}
      </View>
    </View>
  )
}
