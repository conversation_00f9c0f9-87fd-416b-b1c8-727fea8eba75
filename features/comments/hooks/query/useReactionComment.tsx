import { MutationOptions, useMutation } from '@tanstack/react-query'
import { useRef } from 'react'
import { commentService } from '../../services/comment.service'
import { commentMutationKeys } from './queryKeys'

type ReactionCommentVariables = { commentId: string; type: 'like' | 'unlike' }

export const useReactionComment = ({
  options,
}: {
  options?: Omit<MutationOptions | undefined, 'mutationFn' | 'mutationKey'>
} = {}) => {
  // Define an AbortController to cancel previous requests
  const abortControllerRef = useRef<AbortController | null>(null)

  const {
    isError: isReactionCommentError,
    isPending: isReactionCommentPending,
    mutate: reactionCommentMutation,
    ...rest
  } = useMutation({
    mutationKey: commentMutationKeys['reactionComment'].base(),
    mutationFn: async ({ commentId, type }: ReactionCommentVariables) => {
      // Abort any ongoing request before initiating a new one
      if (abortControllerRef.current) {
        abortControllerRef.current.abort()
      }

      // Create a new AbortController for the new request
      const abortController = new AbortController()
      abortControllerRef.current = abortController

      // Pass the signal from AbortController to the service method
      if (type === 'like') {
        return commentService.likeComment(
          { commentId },
          {
            signal: abortControllerRef.current.signal,
          },
        )
      } else if (type === 'unlike') {
        return commentService.unlikeComment(
          { commentId },
          {
            signal: abortControllerRef.current.signal,
          },
        )
      }

      return Promise.resolve()
    },
    ...options,
  })

  return {
    isReactionCommentError,
    isReactionCommentPending,
    reactionCommentMutation,
    ...rest,
  }
}
