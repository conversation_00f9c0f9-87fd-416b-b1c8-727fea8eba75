import { Text } from '@/components/ui/Text/Text'
import { useTranslation } from 'react-i18next'
import { TouchableOpacity, View } from 'react-native'
import Svg, { Path } from 'react-native-svg'
import { PRODUCT_V2_TYPE_OPTIONS } from '../../constants'
import { ProductV2TypeEnum } from '../../enums'
import { ProductCategory } from '../../types'

interface BreadcrumbNavigationProps {
  categoryHistory: ProductCategory[]
  onNavigateToCategory: (category: ProductCategory) => void
  onGoToRoot: () => void
  productType: ProductV2TypeEnum
}

export const BreadcrumbNavigation = ({
  categoryHistory,
  onNavigateToCategory,
  onGoToRoot,
  productType,
}: BreadcrumbNavigationProps) => {
  const { t } = useTranslation()
  if (categoryHistory.length === 0) return null

  return (
    <View className="mb-3 flex flex-row flex-wrap items-center gap-1">
      {/* Root button */}
      <TouchableOpacity
        onPress={onGoToRoot}
        className="flex flex-row items-center gap-1 rounded-md bg-custom-background-hover px-2 py-1"
      >
        <Text size="body8" variant="primary">
          {t(PRODUCT_V2_TYPE_OPTIONS?.[productType]?.translationKey)}
        </Text>
      </TouchableOpacity>
      <Svg width={12} height={12} viewBox="0 0 12 12" fill="none" className="mx-1">
        <Path
          d="M4.5 3L7.5 6L4.5 9"
          stroke="#6B7280"
          strokeWidth={1.5}
          strokeLinecap="round"
          strokeLinejoin="round"
        />
      </Svg>
      {/* Category breadcrumbs */}
      {categoryHistory.map((category, index) => (
        <View key={category.id} className="flex flex-row items-center">
          {/* Separator */}
          {index > 0 && (
            <Svg width={12} height={12} viewBox="0 0 12 12" fill="none" className="mx-1">
              <Path
                d="M4.5 3L7.5 6L4.5 9"
                stroke="#6B7280"
                strokeWidth={1.5}
                strokeLinecap="round"
                strokeLinejoin="round"
              />
            </Svg>
          )}

          {/* Category button */}
          <TouchableOpacity
            onPress={() => onNavigateToCategory(category)}
            className={cn(
              'rounded-md px-2 py-1',
              index === categoryHistory.length - 1 ? 'bg-custom-primary/10' : '',
            )}
          >
            <Text
              size="body8"
              className={cn(
                index === categoryHistory.length - 1
                  ? 'text-custom-primary font-medium'
                  : 'text-custom-text-subdued',
              )}
            >
              {category.title}
            </Text>
          </TouchableOpacity>
        </View>
      ))}
    </View>
  )
}

function cn(...classes: (string | undefined | null | false)[]): string {
  return classes.filter(Boolean).join(' ')
}
