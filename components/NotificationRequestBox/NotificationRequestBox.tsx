import NotificationPermissionIcon from '@/assets/icons/noti-permission-icon.svg'
import { useTranslation } from 'react-i18next'
import { Linking, TouchableOpacity, View } from 'react-native'
import { Text } from '../ui/Text/Text'
export const NotificationRequestBox = () => {
  const { t } = useTranslation()
  return (
    <View className="flex flex-col gap-y-3 rounded-md bg-primary-50 p-3">
      <View className="flex-row items-start gap-2">
        <NotificationPermissionIcon width={20} height={20} />
        <View className="flex-1">
          <View className="mb-[6px]">
            <Text size="body6" variant="primary">
              {t('MES-838')}
            </Text>
          </View>
          <View>
            <Text size="body9">{t('MES-839')}</Text>
          </View>
        </View>
      </View>
      <TouchableOpacity
        className="ml-auto flex flex-row items-center gap-x-2"
        onPress={() => {
          Linking.openSettings()
        }}
      >
        <Text size="body6" variant="primary">
          {t('MES-840')}
        </Text>
      </TouchableOpacity>
    </View>
  )
}
