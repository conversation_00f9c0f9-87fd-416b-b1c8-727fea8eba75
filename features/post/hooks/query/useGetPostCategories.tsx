import { postService } from '@/features/post/services/post.service'
import { PaginatedDocs } from '@/types/global.type'
import { Params } from '@/types/http.type'
import { PostCategory } from '@/types/post.type'
import { UseQueryOptions, useQuery } from '@tanstack/react-query'
import { AxiosRequestConfig } from 'axios'
import { postQueryKeys } from './queryKeys'

export const useGetPostCategories = ({
  params = {},
  options = {},
  useQueryOptions,
}: {
  params?: Params
  options?: AxiosRequestConfig
  useQueryOptions?: Omit<
    UseQueryOptions<PaginatedDocs<PostCategory> | null>,
    'queryKey' | 'queryFn'
  >
} = {}) => {
  const {
    isError: isGetPostCategoriesError,
    isPending: isGetPostCategoriesLoading,
    data: postCategories,
    ...rest
  } = useQuery({
    queryKey: [postQueryKeys['postCategories'].base(), params],
    queryFn: async () =>
      postService.getPostCategories({
        params: params,
        options: options,
      }),
    ...useQueryOptions,
  })
  return {
    isGetPostCategoriesError,
    isGetPostCategoriesLoading,
    postCategories,
    ...rest,
  }
}
