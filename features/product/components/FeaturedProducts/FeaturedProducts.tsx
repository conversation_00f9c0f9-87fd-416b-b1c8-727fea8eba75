import FireIcon from '@/assets/icons/fire-icon.svg'
import { Skeleton } from '@/components/ui/Skeleton/Skeleton'
import { Text } from '@/components/ui/Text/Text'
import { BLURHASH_CODE } from '@/constants/global.constant'
import { useAppLanguage } from '@/hooks/common/useAppLanguage'
import { StyledExpoImage } from '@/libs/styled'
import { APP_ROUTES } from '@/routes/appRoutes'
import { Media } from '@/types/media.type'
import { Link, LinkProps } from 'expo-router'
import { useEffect } from 'react'
import { useTranslation } from 'react-i18next'
import { ScrollView, TouchableOpacity, View } from 'react-native'
import { useShallow } from 'zustand/react/shallow'
import { ProductV2TypeEnum } from '../../enums'
import { useGetFilteredProducts } from '../../hooks/query/product/useGetFilteredProducts'
import { ProductFilterType, useProductStore } from '../../stores/ProductStore'

interface FeaturedProductsProps {
  isRefreshing?: boolean
  productType: ProductV2TypeEnum
}
export const FeaturedProducts = ({ isRefreshing, productType }: FeaturedProductsProps) => {
  const { t } = useTranslation()
  const { primaryLanguage } = useAppLanguage()
  const { productTabState } = useProductStore(
    useShallow((state) => ({
      productTabState: state.productTabState,
    })),
  )

  const { filteredProducts, isGetFilteredProductsLoading, isRefetching, refetch } =
    useGetFilteredProducts({
      useQueryOptions: {
        staleTime: 5 * 60 * 1000,
      },
      params: {
        limit: 10,
        locale: primaryLanguage,
        categories: productTabState[productType].selectedCategory
          ? [productTabState[productType].selectedCategory]
          : [],
        where: {
          and: [
            {
              featured: {
                equals: true,
              },
            },
            {
              type: {
                equals: productType,
              },
            },
            {
              ageGroups: {
                in:
                  productTabState[productType].filters[ProductFilterType.AGE_GROUP]?.map(
                    (filter) => filter.id,
                  ) || undefined,
              },
            },
            {
              medicineType: {
                in:
                  productTabState[productType].filters[ProductFilterType.MEDICINE_TYPE]?.map(
                    (filter) => filter.id,
                  ) || undefined,
              },
            },
          ],
        },
      },
    })

  useEffect(() => {
    if (isRefreshing) {
      refetch()
    }
  }, [isRefreshing])

  if (!filteredProducts?.docs.length && !isGetFilteredProductsLoading && !isRefetching) {
    return null
  }

  return (
    <>
      <View className="flex flex-col gap-y-4">
        <View className="flex flex-row items-center gap-x-1">
          <Text size="body3">{t('MES-69')}</Text>
          <FireIcon width={24} height={24} />
        </View>

        {isGetFilteredProductsLoading || isRefetching ? (
          <FeaturedProductsLoading />
        ) : (
          <ScrollView
            horizontal
            showsHorizontalScrollIndicator={false}
            contentContainerStyle={{ gap: 16 }}
            style={{
              width: '100%',
              height: 'auto',
              gap: 16,
            }}
          >
            {filteredProducts?.docs.map((product) => {
              const media = product?.heroImage as Media
              const imageURL = media?.url || media?.thumbnailURL
              return (
                <Link
                  key={product.id}
                  href={
                    (APP_ROUTES.PRODUCTS.children?.PRODUCTS_DETAIL_V2.path +
                      `/${product.slug}`) as LinkProps['href']
                  }
                  asChild
                >
                  <TouchableOpacity
                    className="flex aspect-square flex-col gap-y-3 overflow-hidden rounded-lg bg-custom-warning-100 p-3"
                    style={{
                      height: 144,
                    }}
                  >
                    <View
                      className="w-full flex-1 bg-white"
                      style={{
                        maxHeight: 80,
                      }}
                    >
                      {imageURL && (
                        <StyledExpoImage
                          source={imageURL}
                          contentFit="cover"
                          transition={300}
                          placeholder={BLURHASH_CODE}
                          className=" h-full w-full"
                        />
                      )}
                    </View>
                    <Text size="body8" className=" line-clamp-2 flex">
                      {product.title}
                    </Text>
                  </TouchableOpacity>
                </Link>
              )
            })}
          </ScrollView>
        )}
      </View>
    </>
  )
}

const FeaturedProductsLoading = () => {
  return (
    <View className="flex  flex-row gap-x-2">
      {[...Array.from({ length: 3 }).fill(1)].map((_, index) => (
        <View
          key={index}
          className="flex aspect-square h-24 w-1/3 flex-1 flex-col gap-y-2 rounded-lg "
        >
          <Skeleton className="flex-1 rounded-lg bg-gray-200" />
          <Skeleton className="h-4 w-2/3 rounded-lg bg-gray-200" />
        </View>
      ))}
    </View>
  )
}
