import { LocaleEnum } from '@/enums/locale.enum'
import { TFunction } from 'i18next'
import { PUSH_NOTIFICATION_TYPE_OPTIONS_MAP } from '../constants'
import { PushNotificationMessagesTypeEnum } from '../enums/push-notification.enum'
import {
  AppSystemNotificationBaseData,
  ChatMessageNotificationBaseData,
  CommentReplyNotificationBaseData,
  GeneralNotificationBaseData,
  NewCommentNotificationBaseData,
  NewPostNotificationBaseData,
  NewProductNotificationBaseData,
  UserPushNotification,
} from '../types'
import { parseDocName } from './parseDocName'

export interface NotificationItemContent {
  title: string
  description: string
  icon: any
  backgroundColor: string
}

/**
 * Get notification title and description based on notification type
 * Provides a direct way to extract content from notification data
 */
export const getNotificationContent = (
  notification: UserPushNotification,
  t: TFunction,
  language: LocaleEnum,
): NotificationItemContent => {
  const { type, title, description, data } = notification

  // Get default metadata for the notification type
  const notificationTypeData = PUSH_NOTIFICATION_TYPE_OPTIONS_MAP?.[type]
    ? PUSH_NOTIFICATION_TYPE_OPTIONS_MAP[type]
    : PUSH_NOTIFICATION_TYPE_OPTIONS_MAP[PushNotificationMessagesTypeEnum.GENERAL]

  // Base return object with defaults
  const baseContent: NotificationItemContent = {
    title: title || t(notificationTypeData.translationKey),
    description: description || '',
    icon: notificationTypeData.icon,
    backgroundColor: notificationTypeData.backgroundColor,
  }

  // Handle specific notification types
  switch (type) {
    case PushNotificationMessagesTypeEnum.NEW_POST: {
      const postData = data as NewPostNotificationBaseData | undefined
      return {
        ...baseContent,
        title: title || t(notificationTypeData.translationKey),
        description: description || '',
      }
    }

    case PushNotificationMessagesTypeEnum.NEW_PRODUCT: {
      const productData = data as NewProductNotificationBaseData | undefined
      return {
        ...baseContent,
        title: title || t(notificationTypeData.translationKey),
        description: description || '',
      }
    }

    case PushNotificationMessagesTypeEnum.CHAT_MESSAGE: {
      const chatData = data as ChatMessageNotificationBaseData | undefined
      return {
        ...baseContent,
        title: t(notificationTypeData.translationKey),
        description: description || '',
      }
    }

    case PushNotificationMessagesTypeEnum.APP_SYSTEM: {
      const systemData = data as AppSystemNotificationBaseData | undefined
      return {
        ...baseContent,
        title: title || t(notificationTypeData.translationKey),
        description: description || '',
      }
    }

    case PushNotificationMessagesTypeEnum.COMMENT_REPLY: {
      const { docName, relationTo, replyUserName } =
        (data as CommentReplyNotificationBaseData) || {}

      const parsedDocName = parseDocName(docName, language)

      const documentName = `"${parsedDocName}"`
      const getTitlte = () => {
        switch (relationTo) {
          case 'posts':
            return t('MES-880', { username: replyUserName, name: documentName })
          case 'products':
            return t('MES-881', { username: replyUserName, name: documentName })
          case 'keywords':
            return t('MES-882', { username: replyUserName, name: documentName })
          default:
            return title || ''
        }
      }
      return {
        ...baseContent,
        title: getTitlte(),
        description: description || '',
      }
    }

    case PushNotificationMessagesTypeEnum.NEW_COMMENT: {
      const { docName, relationTo, commentAuthorName } =
        (data as NewCommentNotificationBaseData) || {}

      const parsedDocName = parseDocName(docName, language)

      const documentName = `"${parsedDocName}"`
      const getTitlte = () => {
        switch (relationTo) {
          case 'posts':
            return t('MES-940', { username: commentAuthorName, name: documentName })
          case 'products':
            return t('MES-941', { username: commentAuthorName, name: documentName })
          case 'keywords':
            return t('MES-942', { username: commentAuthorName, name: documentName })
          default:
            return title || ''
        }
      }
      return {
        ...baseContent,
        title: getTitlte(),
        description: description || '',
      }
    }

    case PushNotificationMessagesTypeEnum.GENERAL:
    default: {
      const generalData = data as GeneralNotificationBaseData | undefined
      return {
        ...baseContent,
        title: title || t(notificationTypeData.translationKey),
        description: description || '',
      }
    }
  }
}

/**
 * Get notification title based on type
 */
export const getNotificationTitle = (
  notification: UserPushNotification,
  t: TFunction,
  language: LocaleEnum,
): string => {
  return getNotificationContent(notification, t, language).title
}

/**
 * Get notification description based on type
 */
export const getNotificationDescription = (
  notification: UserPushNotification,
  t: TFunction,
  language: LocaleEnum,
): string => {
  return getNotificationContent(notification, t, language).description
}

/**
 * Get notification metadata (icon and background color) based on type
 */
export const getNotificationMetadata = (
  notification: UserPushNotification,
): {
  icon: any
  backgroundColor: string
} => {
  const { type } = notification

  const notificationTypeData = PUSH_NOTIFICATION_TYPE_OPTIONS_MAP?.[type]
    ? PUSH_NOTIFICATION_TYPE_OPTIONS_MAP[type]
    : PUSH_NOTIFICATION_TYPE_OPTIONS_MAP[PushNotificationMessagesTypeEnum.GENERAL]

  return {
    icon: notificationTypeData.icon,
    backgroundColor: notificationTypeData.backgroundColor,
  }
}
