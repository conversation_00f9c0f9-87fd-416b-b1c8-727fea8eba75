import SendIconPrimary from '@/assets/icons/send-icon-primary.svg'
import { Text } from '@/components/ui/Text/Text'
import {
  TextInputWithClear,
  TextInputWithClearRef,
} from '@/components/ui/TextInput/TextInputWithClear'
import { forwardRef, useCallback, useEffect, useImperativeHandle, useRef, useState } from 'react'
import { useTranslation } from 'react-i18next'
import {
  InputAccessoryView,
  Keyboard,
  Platform,
  TextInput as RNTextInput,
  TouchableOpacity,
  View,
} from 'react-native'
import { BaseCommentInputRef } from '.'

export interface CommentInputIOSProps {
  onSubmit: (content: string) => void
  onCancel: () => void
  isLoading?: boolean
  placeholder?: string
  replyingTo?: string
  instanceId?: string
  onContentChange?: (content: string) => void
  onCancelReply?: () => void
  onFocus?: () => void
  onBlur?: () => void
}

export interface CommentInputIOSRef extends BaseCommentInputRef {}

export const CommentInputIOS = forwardRef<CommentInputIOSRef, CommentInputIOSProps>(
  (
    {
      onSubmit,
      onCancel,
      isLoading = false,
      placeholder,
      replyingTo,
      instanceId,
      onContentChange,
      onCancelReply,
      onFocus,
      onBlur,
    },
    ref,
  ) => {
    const { t } = useTranslation()
    const [content, setContent] = useState('')
    const [isMounted, setIsMounted] = useState(false)
    const [isAccessoryViewActive, setIsAccessoryViewActive] = useState(false)
    const inputRef = useRef<TextInputWithClearRef>(null)
    const hiddenInputRef = useRef<RNTextInput>(null)
    const MAX_LENGTH = 512
    const isDisabled = isLoading || !content.trim() || content.trim().length > MAX_LENGTH

    // Unique ID for the input accessory view
    const inputAccessoryViewID = `commentInputAccessory_${instanceId || 'default'}`

    const handleSubmit = useCallback(() => {
      const trimmedContent = content.trim()
      if (trimmedContent) {
        onSubmit(trimmedContent)
        setContent('')
        onContentChange?.('')
        setIsAccessoryViewActive(false)
        onBlur?.() // Call blur when submitted
        // Dismiss keyboard after submit
        hiddenInputRef.current?.blur()
      }
    }, [content, onSubmit, onContentChange, onBlur])

    const handleCancel = useCallback(() => {
      setContent('')
      onContentChange?.('')
      setIsAccessoryViewActive(false)
      onBlur?.() // Call blur when explicitly cancelled
      onCancel()
      // Dismiss keyboard after cancel
      hiddenInputRef.current?.blur()
    }, [onCancel, onContentChange, onBlur])

    // Handle input focus/blur
    const handleInputFocus = useCallback(() => {
      setIsAccessoryViewActive(true)
      onFocus?.()
    }, [onFocus])

    const handleInputBlur = useCallback(() => {
      // Don't trigger blur if accessory view is active (user is interacting with input)
      if (isAccessoryViewActive) {
        return
      }
      // Always trigger blur when input loses focus
      setTimeout(() => {
        onBlur?.()
      }, 100)
    }, [onBlur, isAccessoryViewActive])

    const handleContentChange = useCallback(
      (newContent: string) => {
        setContent(newContent)
        onContentChange?.(newContent)
      },
      [onContentChange],
    )

    const handleCancelReply = useCallback(() => {
      onCancelReply?.()
    }, [onCancelReply])

    // Render input content
    const renderInputContent = () => (
      <View className="flex flex-col gap-y-2">
        {replyingTo && (
          <View className="flex flex-row items-center justify-between">
            <View className=" flex flex-row">
              <Text size="body7" variant="subdued" numberOfLines={1}>
                {t('MES-849')}{' '}
              </Text>
              <View className="max-w-[80%] overflow-hidden text-ellipsis">
                <Text size="body7" variant="primary" className="font-bold" numberOfLines={1}>
                  @{replyingTo}
                </Text>
              </View>
            </View>
            <TouchableOpacity onPress={handleCancelReply} hitSlop={10} className="shrink-0">
              <Text size="body7" variant="subdued" className="text-red-500">
                {t('MES-45')}
              </Text>
            </TouchableOpacity>
          </View>
        )}
        <View className="flex flex-row items-center">
          <View className="flex-1">
            <TextInputWithClear
              ref={inputRef}
              placeholder={placeholder || t('MES-887')}
              value={content}
              onChangeText={handleContentChange}
              onClear={() => handleContentChange('')}
              multiline
              onSubmitEditing={handleSubmit}
              className="py-2"
              style={{ maxHeight: 120 }}
              // onBlur={handleInputBlur}
            />
          </View>

          <TouchableOpacity
            className="shrink-0 pl-2"
            hitSlop={10}
            onPress={handleSubmit}
            disabled={isDisabled}
            style={{
              opacity: isDisabled ? 0.5 : 1,
            }}
          >
            <SendIconPrimary />
          </TouchableOpacity>
        </View>
        <View className="mt-1 flex flex-row">
          <Text
            size="body8"
            variant={content.trim().length > MAX_LENGTH ? 'error' : 'subdued'}
          >{`${content.trim().length}/${MAX_LENGTH}`}</Text>
        </View>
      </View>
    )

    useImperativeHandle(
      ref,
      () => ({
        focus: () => {
          // iOS: Use InputAccessoryView - focus hidden input to trigger keyboard
          // Safety: Add multiple fallback attempts to prevent crash
          const attemptFocus = (attempt = 0) => {
            if (attempt > 3) {
              console.warn('[CommentInputIOS] Failed to focus after 3 attempts')
              return
            }

            if (hiddenInputRef.current) {
              try {
                hiddenInputRef.current.focus()
              } catch (error) {
                console.warn(`[CommentInputIOS] Focus attempt ${attempt + 1} failed:`, error)
                setTimeout(() => attemptFocus(attempt + 1), 100)
              }
            } else {
              setTimeout(() => attemptFocus(attempt + 1), 100)
            }
          }

          if (isMounted) {
            attemptFocus()
            handleInputFocus()
          } else {
            setTimeout(() => {
              attemptFocus()
              handleInputFocus()
            }, 150)
          }
        },
        blur: () => {
          try {
            setIsAccessoryViewActive(false)
            hiddenInputRef.current?.blur()
            handleInputBlur()
          } catch (error) {
            console.warn('[CommentInputIOS] Failed to blur:', error)
          }
        },
        clear: () => {
          try {
            setContent('')
            onContentChange?.('')
          } catch (error) {
            console.warn('[CommentInputIOS] Failed to clear:', error)
          }
        },
      }),
      [isMounted, onContentChange, isAccessoryViewActive],
    )
    // Ensure component is mounted before allowing focus
    useEffect(() => {
      let mounted = true
      requestAnimationFrame(() => {
        if (mounted) {
          setIsMounted(true)
        }
      })

      return () => {
        mounted = false
      }
    }, [])

    // Reset state when instanceId changes
    useEffect(() => {
      let mounted = true
      setContent('')
      setIsMounted(false)
      requestAnimationFrame(() => {
        if (mounted) {
          setIsMounted(true)
        }
      })

      return () => {
        mounted = false
      }
    }, [instanceId])

    // Cleanup on unmount - critical for iOS to prevent crashes
    useEffect(() => {
      return () => {
        try {
          // Ensure input is blurred and keyboard dismissed before unmount
          hiddenInputRef.current?.blur()
        } catch (error) {
          console.warn('[CommentInputIOS] Cleanup error:', error)
        }
      }
    }, [])

    useEffect(() => {
      const showSub = Keyboard.addListener(
        Platform.OS === 'ios' ? 'keyboardWillShow' : 'keyboardDidShow',
        () => {
          setIsAccessoryViewActive(true)
        },
      )
      const hideSub = Keyboard.addListener(
        Platform.OS === 'ios' ? 'keyboardWillHide' : 'keyboardDidHide',
        () => {
          setIsAccessoryViewActive(false)
        },
      )

      return () => {
        showSub.remove()
        hideSub.remove()
      }
    }, [])

    useEffect(() => {
      if (!isAccessoryViewActive) {
        handleInputBlur()
      }
    }, [isAccessoryViewActive])
    return (
      <View pointerEvents="none">
        {/* Hidden TextInput that triggers keyboard - iOS production fix */}
        <RNTextInput
          ref={hiddenInputRef}
          value={content}
          onChangeText={handleContentChange}
          // onBlur={handleInputBlur}
          style={{
            position: 'absolute',
            left: -9999,
            opacity: 0,
            width: 1,
            height: 1,
          }}
          inputAccessoryViewID={inputAccessoryViewID}
          autoFocus={false}
          // iOS production safety - prevent touch events
          pointerEvents="none"
          // Prevent iOS keyboard dismiss crashes
          // Ensure proper cleanup
          enablesReturnKeyAutomatically={false}
        />

        <InputAccessoryView nativeID={inputAccessoryViewID}>
          <View
            className="border-t border-custom-neutral-100 bg-white px-4 py-3"
            pointerEvents="auto"
            onTouchStart={() => setIsAccessoryViewActive(true)}
            onTouchEnd={() => setIsAccessoryViewActive(true)}
          >
            {renderInputContent()}
          </View>
        </InputAccessoryView>
      </View>
    )
  },
)

CommentInputIOS.displayName = 'CommentInputIOS'
