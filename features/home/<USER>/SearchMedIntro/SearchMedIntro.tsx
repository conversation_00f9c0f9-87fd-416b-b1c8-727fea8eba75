import TakeAPhoto from '@/assets/icons/take-a-photo-icon.svg'
import { Button } from '@/components/ui/Button/Button'
import { Text } from '@/components/ui/Text/Text'
import { APP_ROUTES } from '@/routes/appRoutes'
import * as Haptics from 'expo-haptics'
import { LinearGradient } from 'expo-linear-gradient'
import { Link, LinkProps } from 'expo-router'
import React from 'react'
import { useTranslation } from 'react-i18next'
import { StyleSheet, View } from 'react-native'

export const SearchMedIntro = () => {
  const { t } = useTranslation()

  return (
    <View className="h-[120px] flex-1 rounded-lg px-4">
      <LinearGradient
        colors={['#F79029', '#FDC661']}
        start={{ x: 0.017, y: 0 }}
        end={{ x: 0.978, y: 1 }}
        className="relative flex-col items-center justify-center"
        style={{
          borderRadius: 8,
          flex: 1,
          alignContent: 'center',
          justifyContent: 'center',
        }}
      >
        {/* Decorative circle */}
        <View style={styles.decorativeCircleQuick} />

        <View className="flex-1 flex-row items-center justify-between p-4">
          <View className="flex-1">
            <Text size="body3" className="mb-2  text-white">
              {t('MES-589')}
            </Text>
            <Link
              href={
                APP_ROUTES.CHAT_BOT.children?.CHAT_BOT_SEARCH_MEDICINE.path as LinkProps['href']
              }
              asChild
            >
              <Button
                role="link"
                className="self-start rounded-[6px] bg-primary-50"
                onPress={() => {
                  Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light)
                }}
              >
                <Text size="body6" variant="primary">
                  {t('MES-609')}
                </Text>
              </Button>
            </Link>
          </View>
          {/* Translate icon */}
          <TakeAPhoto width={70} height={70} />
        </View>
      </LinearGradient>
    </View>
  )
}

const styles = StyleSheet.create({
  decorativeCircleQuick: {
    position: 'absolute',
    width: 95.02,
    height: 95.02,
    right: -47.51,
    bottom: -47.51,
    borderWidth: 1,
    borderColor: 'rgba(207, 213, 242, 0.1)',
    borderRadius: 47.51,
    backgroundColor: 'transparent',
  },
})
