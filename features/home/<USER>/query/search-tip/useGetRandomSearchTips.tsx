import { searchTipService } from '@/features/home/<USER>/search-tip.service'
import { PaginatedDocs } from '@/types/global.type'
import { Params } from '@/types/http.type'
import { SearchTip } from '@/types/search-tip.type'
import { UseQueryOptions, useQuery } from '@tanstack/react-query'
import { AxiosRequestConfig } from 'axios'
import { searchTipQueryKeys } from './queryKeys'

export const useGetRandomSearchTips = ({
  params = {},
  options = {},
  useQueryOptions,
}: {
  params?: Params
  options?: AxiosRequestConfig
  useQueryOptions?: Omit<UseQueryOptions<PaginatedDocs<SearchTip> | null>, 'queryKey' | 'queryFn'>
} = {}) => {
  const {
    isError: isGetRandomSearchTipsError,
    isPending: isGetRandomSearchTipsLoading,
    data: randomSearchTips,
    ...rest
  } = useQuery({
    queryKey: [searchTipQueryKeys['randomSearchTips'].base(), params],
    queryFn: async () =>
      searchTipService.getRandomSearchTips({
        params: params,
        options: options,
      }),
    ...useQueryOptions,
  })
  return {
    isGetRandomSearchTipsError,
    isGetRandomSearchTipsLoading,
    randomSearchTips,
    ...rest,
  }
}
