import CameraIcon from '@/assets/icons/camera-icon.svg'
import SearchInputIcon from '@/assets/icons/search-input-icon.svg'
import { Text } from '@/components/ui/Text/Text'
import { APP_ROUTES } from '@/routes/appRoutes'
import * as Haptics from 'expo-haptics'
import { Link, LinkProps } from 'expo-router'
import { useTranslation } from 'react-i18next'
import { TouchableOpacity, View } from 'react-native'

export const ProductHeader = () => {
  const { t } = useTranslation()
  return (
    <View className="mb-3 flex flex-col gap-3 px-4 pt-4">
      <Text size="heading7" variant="primary">
        {t('MES-561')}
      </Text>
      <View className="flex w-full flex-row items-center gap-x-2">
        <Link
          href={APP_ROUTES.PRODUCTS?.children?.PRODUCTS_SEARCH.path as LinkProps['href']}
          asChild
        >
          <TouchableOpacity
            className="flex-1 rounded-lg border border-custom-divider p-2"
            onPress={() => {
              Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light)
            }}
            accessibilityRole="button"
          >
            <View className="flex flex-row gap-x-2">
              <SearchInputIcon width={18} height={18} />
              <View className="flex-1 ">
                <Text className="line-clamp-1" size="field1" variant="subdued">
                  {t('MES-66')}
                </Text>
              </View>
            </View>
          </TouchableOpacity>
        </Link>

        <Link
          href={APP_ROUTES.CHAT_BOT.children?.CHAT_BOT_SEARCH_MEDICINE.path as LinkProps['href']}
          asChild
        >
          <TouchableOpacity
            role="button"
            className="rounded-full bg-primary p-2"
            onPress={() => {
              Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light)
            }}
          >
            <CameraIcon width={20} height={20} />
          </TouchableOpacity>
        </Link>
      </View>
    </View>
  )
}
