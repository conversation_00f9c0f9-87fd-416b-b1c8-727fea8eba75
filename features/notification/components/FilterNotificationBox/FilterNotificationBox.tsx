import { useSheetActions } from '@/contexts/SheetContext/SheetContext'
import { useCallback, useEffect, useMemo, useRef } from 'react'
import { useTranslation } from 'react-i18next'
import { View } from 'react-native'
import { useShallow } from 'zustand/react/shallow'

import { BaseFilterListSection } from '@/components/Filter/BaseFilterListSection/BaseFilterListSection'
import {
  BaseFilterSheetBox,
  type BaseFilterSheetBoxRef,
} from '@/components/Filter/BaseFilterSheetBox/BaseFilterSheetBox'
import { BottomSheetScrollView } from '@gorhom/bottom-sheet'

import { PUSH_NOTIFICATION_TYPE_OPTIONS } from '../../constants'
import { PushNotificationMessagesTypeEnum } from '../../enums/push-notification.enum'
import {
  NotificationFilterType,
  useUnifiedNotificationStore,
} from '../../stores/UnifiedNotificationStore'
import { FilterNotificationFooter } from './FilterNotificationFooter'

interface FilterNotificationBoxProps {
  closeSheet?: () => void
  sharedFooterRef: React.RefObject<View | null>
  sharedSheetBoxRef?: React.RefObject<BaseFilterSheetBoxRef | null>
}

export const FilterNotificationBox = ({
  closeSheet,
  sharedFooterRef,
  sharedSheetBoxRef,
}: FilterNotificationBoxProps) => {
  const { t } = useTranslation()

  const localSheetBoxRef = useRef<BaseFilterSheetBoxRef>(null)
  const sheetBoxRef = sharedSheetBoxRef || localSheetBoxRef

  const notificationTypes = useMemo(() => {
    return Object.values(PUSH_NOTIFICATION_TYPE_OPTIONS).map((item) => ({
      id: item.value,
      label: t(item.filterLabelKey),
      value: item.value as PushNotificationMessagesTypeEnum,
    }))
  }, [t])

  const { toggleTempFilter, initTempFilters, tempFilters } = useUnifiedNotificationStore(
    useShallow((state) => ({
      toggleTempFilter: state.toggleTempFilter,
      initTempFilters: state.initTempFilters,
      tempFilters: state.tempFilters,
    })),
  )

  // Initialize temp search filters when component mounts
  useEffect(() => {
    initTempFilters()
  }, [initTempFilters])

  // Trigger manual footer measurement when filters change (no re-render!)
  useEffect(() => {
    // Small delay to allow footer DOM updates to complete
    const timer = setTimeout(() => {
      sheetBoxRef.current?.measureFooter()
    }, 100)

    return () => clearTimeout(timer)
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [tempFilters])

  return (
    <BaseFilterSheetBox
      ref={sheetBoxRef}
      title={t('MES-481')}
      onClose={closeSheet}
      footerRef={sharedFooterRef}
      enableFooterHeightMeasurement={true}
      footerHeightPadding={40}
      useBottomSheetScrollView={true}
    >
      <BottomSheetScrollView className="relative" showsVerticalScrollIndicator={false}>
        <View className="flex flex-col gap-y-3 px-4">
          <BaseFilterListSection
            title={t('MES-956')}
            data={notificationTypes}
            onSelectFilter={(item) => {
              toggleTempFilter(NotificationFilterType.TYPE, {
                id: item.id,
                label: item.label,
                type: NotificationFilterType.TYPE,
                value: item.value,
              })
            }}
            activeFilters={tempFilters?.[NotificationFilterType.TYPE]?.map((filter) => filter.id)}
            idKey="id"
            labelKey="label"
          />
        </View>
      </BottomSheetScrollView>
    </BaseFilterSheetBox>
  )
}

export const useOpenFilterNotificationBox = () => {
  const { openCustomSheet, closeSheet } = useSheetActions()

  const sharedFooterRef = useRef<View>(null)
  const sharedSheetBoxRef = useRef<BaseFilterSheetBoxRef>(null)

  const handleOpenFilterNotificationBox = useCallback(() => {
    openCustomSheet({
      children: ({ close }) => (
        <FilterNotificationBox
          closeSheet={close}
          sharedFooterRef={sharedFooterRef}
          sharedSheetBoxRef={sharedSheetBoxRef}
        />
      ),
      baseProps: {
        snapPoints: ['85%', '100%'],
        enableHandlePanningGesture: true,
        enableDynamicSizing: false,
        enableOverDrag: false,
      },
      options: {
        footerComponent: (props) => {
          return (
            <FilterNotificationFooter ref={sharedFooterRef} closeSheet={closeSheet} {...props} />
          )
        },
      },
    })
  }, [openCustomSheet, closeSheet])

  return { handleOpenFilterNotificationBox }
}
