import ArrowDownIcon from '@/assets/icons/arrow-down-icon.svg'
import LightBulbIcon from '@/assets/icons/light-bulb-v2-icon.svg'
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from '@/components/ui/Accordion/Accordion'
import { Text } from '@/components/ui/Text/Text'
import { useAppLanguage } from '@/hooks/common/useAppLanguage'
import { cn } from '@/utils/cn'
import React, { memo } from 'react'
import { useTranslation } from 'react-i18next'
import { Platform, ScrollView, View } from 'react-native'
import { useGetRandomSearchTips } from '../../hooks/query/search-tip/useGetRandomSearchTips'
export const HomeTips = memo(() => {
  const { primaryLanguage } = useAppLanguage()
  const { t } = useTranslation()
  const { randomSearchTips, isGetRandomSearchTipsLoading } = useGetRandomSearchTips({
    params: {
      locale: primaryLanguage,
      limit: 5,
    },
    useQueryOptions: {
      staleTime: 5 * 60 * 1000,
    },
  })
  return (
    <Accordion className="w-full rounded-b-xl" type="multiple">
      <AccordionItem
        value="1"
        className={cn(
          'w-full rounded-b-lg ',

          Platform.OS === 'android' && 'border border-t-0 border-custom-divider',
        )}
        isDisabled={isGetRandomSearchTipsLoading || !randomSearchTips?.docs.length}
      >
        <AccordionTrigger className="w-full  ">
          <View className="w-full  flex-row items-center justify-between ">
            <View className="flex-row items-center gap-x-2">
              <Text size="body6" variant="primary" className="!text-custom-informative-700">
                {t('MES-555')}
              </Text>
              <LightBulbIcon width={20} height={20} />
            </View>
            <ArrowDownIcon width={18} height={18} />
          </View>
        </AccordionTrigger>
        <AccordionContent className="rounded-b-lg ">
          <ScrollView
            horizontal
            className=""
            pagingEnabled
            contentContainerStyle={{ gap: 12 }}
            snapToInterval={272 + 12}
            showsHorizontalScrollIndicator={false}
            decelerationRate={'fast'}
          >
            {randomSearchTips?.docs.map((searchTip) => (
              <View
                key={searchTip.id}
                className="w-[272px] flex-col gap-2 rounded-lg border border-custom-divider p-3"
              >
                <Text size="body6">{searchTip.title}</Text>
                <Text size="body7" variant="subdued">
                  {searchTip.description}
                </Text>
              </View>
            ))}
          </ScrollView>
        </AccordionContent>
      </AccordionItem>
    </Accordion>
  )
})
