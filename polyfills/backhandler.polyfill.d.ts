/**
 * Type definitions for BackHandler polyfill
 */

import { BackHandler as RN<PERSON><PERSON><PERSON>and<PERSON> } from 'react-native'

declare module 'react-native' {
  interface BackHandlerStatic {
    /**
     * @deprecated This method is deprecated in React Native 0.65+
     * Use the subscription object returned by addEventListener instead
     */
    removeEventListener(eventName: 'hardwareBackPress', handler: () => boolean): void
  }
}

export default RNBackHandler
