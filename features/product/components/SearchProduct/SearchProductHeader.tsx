import FilterIcon from '@/assets/icons/filter-icon.svg'
import { SearchInput, SearchInputRef } from '@/components/ui/SearchInput/SearchInput'
import { useEffect, useRef, useState } from 'react'
import { useTranslation } from 'react-i18next'
import { Keyboard, TouchableOpacity, View } from 'react-native'
import { useShallow } from 'zustand/react/shallow'
import { useProductStore } from '../../stores/ProductStore'
import { useOpenFilterSearchBox } from '../FilterProductBox/FilterSearchBox'
import { SeachTipsBox } from './SeachTipsBox'

interface SearchProductHeaderProps {
  showTipsBox?: boolean
}

export const SearchProductHeader = ({ showTipsBox }: SearchProductHeaderProps) => {
  const { t } = useTranslation()
  const { hasActiveFilters } = useProductStore(
    useShallow((state) => ({
      hasActiveFilters: state.hasActiveFilters,
    })),
  )
  const [searchInputValue, setSearchInputValue] = useState('')
  const searchInputRef = useRef<SearchInputRef>(null)

  const handleSearchInputChange = (text: string) => {
    setSearchInputValue(text)
  }

  const handleClearSearchInput = () => {
    setSearchInputValue('')
    setSearchTextValue('')
  }

  const { setSearchTextValue } = useProductStore(
    useShallow((state) => ({
      setSearchTextValue: state.setSearchTextValue,
    })),
  )

  const { handleOpenFilterSearchBox } = useOpenFilterSearchBox()

  useEffect(() => {
    setTimeout(() => {
      searchInputRef.current?.focus()
    }, 500)
  }, [])

  return (
    <View className="flex flex-col gap-y-2">
      <View className="flex flex-row items-center gap-x-2">
        <SearchInput
          ref={searchInputRef}
          placeholder={t('MES-66')}
          value={searchInputValue}
          onChangeText={handleSearchInputChange}
          onClear={handleClearSearchInput}
          onSubmitEditing={() => {
            setSearchTextValue(searchInputValue)
          }}
        />
        <TouchableOpacity
          hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
          onPress={() => {
            Keyboard.dismiss()
            handleOpenFilterSearchBox()
          }}
          className="relative flex h-full flex-row items-center gap-x-2 rounded-lg border border-custom-divider-border p-3"
        >
          <FilterIcon width={18} height={18} />
          {hasActiveFilters && (
            <View className="absolute -right-1 -top-1 size-4 rounded-full bg-custom-danger-600/80" />
          )}
        </TouchableOpacity>
      </View>
      {showTipsBox && <SeachTipsBox />}
    </View>
  )
}
