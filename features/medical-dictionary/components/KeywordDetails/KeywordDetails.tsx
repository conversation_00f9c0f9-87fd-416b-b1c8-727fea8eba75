import { Skeleton } from '@/components/ui/Skeleton/Skeleton'

import { useGetKeywordDetails } from '@/hooks/query/keyword/useGetKeywordDetails'
import { primary } from '@/styles/_colors'
import { Keyword, KeywordV2 } from '@/types/keyword.type'
import { User } from '@/types/user.type'

import { CommentInput } from '@/features/comments/components/CommentInput/CommentInput'
import { CommentsSection } from '@/features/comments/components/CommentsSection/CommentsSection'
import { useCommentManagement } from '@/features/comments/hooks/common/useCommentManagement'
import { useLocalSearchParams } from 'expo-router'
import { useEffect, useState } from 'react'
import { useTranslation } from 'react-i18next'
import { RefreshControl, View } from 'react-native'
import { KeyboardAwareScrollView } from 'react-native-keyboard-controller'
import { useSafeAreaInsets } from 'react-native-safe-area-context'
import { KeywordDetailsContent } from './KeywordDetailsContent'
import { KeywordDetailsHeader } from './KeywordDetailsHeader'
import { KeywordRelatedPosts } from './KeywordRelatedPosts'
import { KeywordRelatedProducts } from './KeywordRelatedProducts'
import { RelatedFaculties } from './RelatedFaculties'
import { RelatedKeywords } from './RelatedKeywords'

export const KeywordDetails = () => {
  const { id } = useLocalSearchParams()
  const { t } = useTranslation()
  const [refreshing, setRefreshing] = useState(false)
  const [shouldRefreshChildren, setShouldRefreshChildren] = useState(false)
  const {
    keywordDetails,
    isGetKeywordDetailsError,
    isGetKeywordDetailsLoading,
    isFetching,
    refetch,
  } = useGetKeywordDetails({
    id: id as string,
    params: {
      locale: 'all',
      convertDescriptionHTML: true,
      withFavoriteFlag: true,
    },
    useQueryOptions: {
      staleTime: 0,
      gcTime: 0,
      enabled: Boolean(id),
    },
  })
  const { isFavorite } = (keywordDetails as KeywordV2) || {}

  const insets = useSafeAreaInsets()
  const [commentInputHeight, setCommentInputHeight] = useState(0)

  // Comment management
  const {
    commentInputRef,
    isAddCommentPending,
    handleCommentSubmit,
    handleCommentCancel,
    handleInputContentChange,
    handleCancelReply,
    handleAddCommentPress,
    handleReplyPress,
    handleEditComment,
    handleStartEdit,
    handleCancelEdit,
    handleDeleteComment,
    handleRetryAddComment,
    isEditCommentPending,

    editingCommentId,
    replyingTo,
    currentInputContent,
    allComments,
    totalComments,
    isGetCommentsLoading,
    isGetCommentsFetching,
    cacheManager,
    mainParams,
    nestedParams,
    showCommentInput,
  } = useCommentManagement({
    id: id as string,
    relationTo: 'keywords',
    customMainParams: { limit: 5 }, // Match CommentsSection params
  })

  // Reset shouldRefreshChildren after child components have had a chance to refresh
  useEffect(() => {
    if (shouldRefreshChildren) {
      const timer = setTimeout(() => {
        setShouldRefreshChildren(false)
      }, 100)
      return () => clearTimeout(timer)
    }
  }, [shouldRefreshChildren])

  const handleRefresh = async () => {
    setRefreshing(true)
    setShouldRefreshChildren(false)

    try {
      await refetch()
      // Only trigger child refreshes after main fetch is done
      setShouldRefreshChildren(true)
    } finally {
      setRefreshing(false)
    }
  }

  if (!id || isGetKeywordDetailsError) return null

  return (
    <View className="flex-1 bg-white" style={{ paddingBottom: insets.bottom }}>
      <KeywordDetailsHeader
        isFavorite={isFavorite}
        id={id as string}
        isLoading={isGetKeywordDetailsLoading || isFetching}
      />

      <KeyboardAwareScrollView
        contentContainerStyle={{
          paddingBottom: commentInputHeight,
        }}
        bottomOffset={commentInputHeight || 40}
        showsVerticalScrollIndicator={false}
        keyboardShouldPersistTaps="handled"
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={handleRefresh}
            colors={[primary['500']]}
            tintColor={primary['500']}
            progressBackgroundColor="#FFFFFF"
            progressViewOffset={0}
          />
        }
      >
        {isGetKeywordDetailsLoading || isFetching ? (
          <KeywordDetailsLoading />
        ) : (
          <View className=" flex flex-col gap-y-4 py-3">
            <KeywordDetailsContent keyword={keywordDetails as unknown as Keyword} />
            <CommentsSection
              id={id as string}
              relationTo="keywords"
              isRefreshing={shouldRefreshChildren}
              customMainParams={{ limit: 5 }}
              customTitle={t('MES-856')}
              commentInputRef={commentInputRef}
              handleAddCommentPress={handleAddCommentPress}
              handleReplyPress={handleReplyPress}
              handleCommentSubmit={handleCommentSubmit}
              handleCommentCancel={handleCommentCancel}
              handleInputContentChange={handleInputContentChange}
              handleCancelReply={handleCancelReply}
              handleEditComment={handleEditComment}
              handleStartEdit={handleStartEdit}
              handleCancelEdit={handleCancelEdit}
              handleDeleteComment={handleDeleteComment}
              handleRetryAddComment={handleRetryAddComment}
              isAddCommentPending={isAddCommentPending}
              isEditCommentPending={isEditCommentPending}
              editingCommentId={editingCommentId}
              // isDeleteCommentPending={isDeleteCommentPending}
              replyingTo={replyingTo}
              currentInputContent={currentInputContent}
              allComments={allComments}
              totalComments={totalComments}
              isGetCommentsLoading={isGetCommentsLoading}
              isGetCommentsFetching={isGetCommentsFetching}
              cacheManager={cacheManager}
              mainParams={mainParams}
              nestedParams={nestedParams}
            />
            <RelatedKeywords
              categories={keywordDetails?.categories}
              isRefreshing={shouldRefreshChildren}
              id={id as string}
            />
            <RelatedFaculties id={id as string} isRefreshing={shouldRefreshChildren} />
            <KeywordRelatedPosts id={id as string} isRefreshing={shouldRefreshChildren} />
            <KeywordRelatedProducts id={id as string} isRefreshing={shouldRefreshChildren} />
          </View>
        )}
      </KeyboardAwareScrollView>

      {/* Unified input for all platforms - shows only when adding/replying */}
      {(showCommentInput || replyingTo) && !editingCommentId && (
        <CommentInput
          ref={commentInputRef}
          onSubmit={handleCommentSubmit}
          onCancel={handleCommentCancel}
          isLoading={isAddCommentPending}
          placeholder={t('MES-887')}
          replyingTo={replyingTo ? (replyingTo?.author as User)?.name || 'Hico User' : undefined}
          instanceId="keyword-details"
          onContentChange={handleInputContentChange}
          onCancelReply={handleCancelReply}
          onBlur={handleCommentCancel}
          onLayout={(event) => {
            setCommentInputHeight(event.nativeEvent.layout.height)
          }}
        />
      )}
    </View>
  )
}

const KeywordDetailsLoading = () => {
  return (
    <View
      className=" flex flex-1 flex-col bg-white px-4 py-3 "
      style={{
        gap: 24,
      }}
    >
      <Skeleton className=" h-[120px] w-full rounded-lg" />
      <View className="flex-col gap-y-3">
        <View className="h-10  rounded-lg" style={{ width: '40%' }}>
          <Skeleton className="h-full w-full" />
        </View>
        <View className="flex-col gap-y-2">
          <Skeleton className="h-8 w-full" />
          <Skeleton className="h-8 w-full" />
          <Skeleton className="h-8 w-full" />
          <Skeleton className="h-8 w-full" />
        </View>
      </View>

      <View className="w-full flex-col gap-y-4">
        <View className="h-10  rounded-lg" style={{ width: '40%' }}>
          <Skeleton className="h-full w-full" />
        </View>
        <View className="w-full flex-col gap-y-3">
          {new Array(3).fill(0).map((_, index) => (
            <View key={index} className="flex-1 flex-row gap-x-3">
              <View
                style={{
                  width: 64,
                  height: 64,
                  borderRadius: '100%',
                }}
              >
                <Skeleton className="h-full w-full rounded-full" />
              </View>
              <View className="w-full flex-1 flex-col gap-y-2">
                <Skeleton className="h-8 w-1/3" />
                <Skeleton className="h-12 w-full" />
              </View>
            </View>
          ))}
        </View>
      </View>

      <View className="flex-col gap-y-4">
        <View className="h-10  rounded-lg" style={{ width: '40%' }}>
          <Skeleton className="h-full w-full" />
        </View>
        <View className="flex-col gap-y-3">
          {new Array(3).fill(0).map((_, index) => (
            <Skeleton key={index} className="h-12 w-full" />
          ))}
        </View>
      </View>
    </View>
  )
}
