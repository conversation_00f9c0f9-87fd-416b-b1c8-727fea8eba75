import { Text } from '@/components/ui/Text/Text'
import { Product } from '@/features/product/types'
import { useAppLanguage } from '@/hooks/common/useAppLanguage'
import React, { useCallback, useMemo } from 'react'
import { ActivityIndicator, FlatList, View } from 'react-native'
import { useGetInfiniteFilteredProducts } from '../../hooks/query/product/useGetInfiniteFilteredProducts'
import { ProductItem } from './ProductItem'

export const ProductList = () => {
  const { primaryLanguage } = useAppLanguage()
  const {
    filteredProducts,
    isGetFilteredProductsLoading,
    isGetFilteredProductsError,
    fetchNextPage,
    hasNextPage,
    isGetFilteredProductsFetchingNextPage,
  } = useGetInfiniteFilteredProducts({
    config: {
      staleTime: 5 * 60 * 1000,
    },
    params: {
      locale: primaryLanguage,
      limit: 1,
    },
  })

  // Flatten all products from all pages
  const allProducts = useMemo(() => {
    if (!filteredProducts?.pages) return []
    return filteredProducts.pages.flatMap((page) => page?.docs || [])
  }, [filteredProducts])

  const handleLoadMore = useCallback(() => {
    if (hasNextPage && !isGetFilteredProductsFetchingNextPage) {
      fetchNextPage()
    }
  }, [hasNextPage, isGetFilteredProductsFetchingNextPage, fetchNextPage])

  const renderItem = useCallback(({ item }: { item: Product }) => {
    return <ProductItem product={item} />
  }, [])

  const renderFooter = useCallback(() => {
    if (!isGetFilteredProductsFetchingNextPage) return null

    return (
      <View className="py-4">
        <ActivityIndicator size="small" />
      </View>
    )
  }, [isGetFilteredProductsFetchingNextPage])

  const renderEmpty = useCallback(() => {
    if (isGetFilteredProductsLoading) {
      return (
        <View className="flex-1 items-center justify-center py-8">
          <ActivityIndicator size="large" />
        </View>
      )
    }

    if (isGetFilteredProductsError) {
      return (
        <View className="flex-1 items-center justify-center py-8">
          <Text size="body6" className="text-red-500">
            Failed to load products. Please try again.
          </Text>
        </View>
      )
    }

    return (
      <View className="flex-1 items-center justify-center py-8">
        <Text size="body6" className="text-gray-500">
          No products found.
        </Text>
      </View>
    )
  }, [isGetFilteredProductsLoading, isGetFilteredProductsError])

  return (
    <FlatList
      data={allProducts}
      renderItem={renderItem}
      keyExtractor={(item) => item.id}
      numColumns={2}
      columnWrapperStyle={{ gap: 12 }}
      contentContainerStyle={{
        paddingHorizontal: 16,
        gap: 16,
        paddingBottom: 20,
      }}
      onEndReached={handleLoadMore}
      onEndReachedThreshold={0.1}
      ListFooterComponent={renderFooter}
      ListEmptyComponent={renderEmpty}
      showsVerticalScrollIndicator={false}
      removeClippedSubviews={true}
      maxToRenderPerBatch={10}
      windowSize={10}
      initialNumToRender={10}
    />
  )
}
