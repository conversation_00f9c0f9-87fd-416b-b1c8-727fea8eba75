import React from 'react'
import { SvgXml } from 'react-native-svg'

interface SvgIconProps {
  xml: string
  width?: number
  height?: number
  color?: string
}

export function SvgIcon({ xml, width = 24, height = 24, color }: SvgIconProps) {
  // Replace stroke color in the SVG if color prop is provided
  const modifiedXml = color ? xml.replace(/stroke="[^"]*"/g, `stroke="${color}"`) : xml

  return <SvgXml xml={modifiedXml} width={width} height={height} />
}
