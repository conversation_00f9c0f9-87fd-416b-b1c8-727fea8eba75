import { Skeleton } from '@/components/ui/Skeleton/Skeleton'
import { ScrollView, View } from 'react-native'

export const HospitalDetailsSkeleton = () => {
  return (
    <ScrollView
      className="flex-1 bg-white"
      showsVerticalScrollIndicator={false}
      contentContainerStyle={{
        paddingBottom: 20,
      }}
    >
      <View className="flex flex-col gap-y-3 py-3">
        {/* Hospital Info Section Skeleton */}
        <View className="px-4">
          {/* Banner Image Skeleton */}
          <View className="relative overflow-hidden rounded-xl">
            <Skeleton className="aspect-video w-full " />
            {/* Logo Skeleton */}
            <View className="absolute right-[10px] top-[10px] rounded-lg bg-white p-1">
              <Skeleton className="h-9 w-32 " />
            </View>
          </View>

          {/* Hospital Information Skeleton */}
          <View className="p-3">
            {/* Hospital Name */}
            <Skeleton className="mb-2 h-7 w-3/4 " />
            {/* Japanese Name */}
            <Skeleton className="mb-3 h-4 w-1/2 " />
            {/* Address */}
            <View className="mb-3 flex flex-row items-center gap-3">
              <Skeleton className="h-[18px] w-[18px] rounded " />
              <Skeleton className="h-4 w-2/3 " />
            </View>
            {/* Description */}
            <View className="flex flex-col gap-1">
              <Skeleton className="h-4 w-full " />
              <Skeleton className="h-4 w-5/6 " />
              <Skeleton className="h-4 w-3/4 " />
            </View>
          </View>
        </View>

        {/* Examination Forms Section Skeleton */}
        <View className="flex flex-col gap-y-3 px-4">
          {/* Section Title */}
          <View className="flex flex-row items-center justify-between">
            <Skeleton className="h-6 w-48 " />
          </View>

          {/* Examination Form Note Accordion Skeleton */}
          <View className="w-full rounded-lg bg-primary-50 p-3">
            <View className="flex-row items-center justify-between">
              <View className="flex-1 flex-row items-center gap-x-3">
                <Skeleton className="h-5 w-5 rounded " />
                <Skeleton className="h-5 w-32 " />
              </View>
              <Skeleton className="h-[18px] w-[18px] rounded " />
            </View>
          </View>

          {/* Faculty Cards Skeleton */}
          <View className="flex-col gap-3">
            {[...Array(4)].map((_, index) => (
              <View
                key={index}
                className="h-[50px] w-full flex-row items-center justify-start gap-3 rounded-lg bg-custom-background-hover px-3 py-2"
              >
                <Skeleton className="h-8 w-8 rounded " />
                <Skeleton className="h-5 w-32 " />
              </View>
            ))}
          </View>
        </View>

        {/* Medical Document Categories Section Skeleton */}
        <View className="flex flex-col gap-y-3 px-4">
          {/* Section Title */}
          <View className="flex flex-row items-center justify-between">
            <Skeleton className="h-6 w-40 " />
            <Skeleton className="h-4 w-16 " />
          </View>

          {/* Medical Document Category Cards */}
          <View className="flex-row flex-wrap gap-3">
            {[...Array(4)].map((_, index) => (
              <View
                key={index}
                className="h-20 w-[calc(50%-6px)] flex-col items-center justify-center rounded-lg border border-neutral-200 bg-white p-3"
              >
                <Skeleton className="mb-2 h-6 w-6 rounded " />
                <Skeleton className="h-4 w-16 " />
              </View>
            ))}
          </View>
        </View>
      </View>
    </ScrollView>
  )
}
