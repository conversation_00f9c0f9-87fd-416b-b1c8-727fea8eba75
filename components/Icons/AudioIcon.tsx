import { primary } from '@/styles/_colors'
import { SVGIconProps } from '@/types/global.type'
import * as React from 'react'
import Svg, { Path } from 'react-native-svg'
interface AudioIconProps extends SVGIconProps {
  isPlaying?: boolean
}
export const AudioIcon = ({ isPlaying, ...props }: AudioIconProps) => (
  <Svg width={20} height={20} viewBox="0 0 20 20" fill="none" {...props}>
    <Path
      d="M1.66699 8.33381V11.6671C1.66699 13.3338 2.50033 14.1671 4.16699 14.1671H5.35866C5.66699 14.1671 5.97533 14.2588 6.24199 14.4171L8.67533 15.9421C10.7753 17.2588 12.5003 16.3005 12.5003 13.8255V6.17548C12.5003 3.69214 10.7753 2.74214 8.67533 4.05881L6.24199 5.58381C5.97533 5.74214 5.66699 5.83381 5.35866 5.83381H4.16699C2.50033 5.83381 1.66699 6.66714 1.66699 8.33381Z"
      stroke={isPlaying ? primary[500] : '#8B8C99'}
      strokeWidth={1.5}
    />
    <Path
      d="M15 6.66699C16.4833 8.64199 16.4833 11.3587 15 13.3337"
      stroke={isPlaying ? primary[500] : '#8B8C99'}
      strokeWidth={1.5}
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <Path
      d="M16.5254 4.58301C18.9337 7.79134 18.9337 12.208 16.5254 15.4163"
      stroke={isPlaying ? primary[500] : '#8B8C99'}
      strokeWidth={1.5}
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </Svg>
)
