import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/Tabs/Tabs'
import { Text } from '@/components/ui/Text/Text'
import { cn } from '@/utils/cn'
import * as Haptics from 'expo-haptics'
import { useEffect, useMemo, useRef, useState } from 'react'
import { useTranslation } from 'react-i18next'
import { NativeMethods, ScrollView, View } from 'react-native'
import { USER_PUSH_NOTIFICATION_TAB_OPTIONS } from '../../constants'
import { UserPushNotificationTabContent } from './UserPushNotificationTabContent'

type NotificationTabType = 'all' | 'viewed' | 'not-viewed'

export const UserPushNotificationTabs = () => {
  const { t } = useTranslation()
  const scrollViewRef = useRef<ScrollView>(null)
  const tabRefs = useRef<{ [key: string]: View | null }>({})
  const [selectedTab, setSelectedTab] = useState<NotificationTabType>('all')

  // Auto-scroll to active tab
  useEffect(() => {
    if (scrollViewRef.current && tabRefs.current[selectedTab]) {
      setTimeout(() => {
        const activeTabRef = tabRefs.current[selectedTab]
        if (activeTabRef) {
          activeTabRef.measureLayout(
            scrollViewRef.current as unknown as NativeMethods,
            (x) => {
              // Scroll to center the active tab
              const scrollX = Math.max(0, x - 50) // 50px offset for better visibility
              scrollViewRef.current?.scrollTo({
                x: scrollX,
                animated: true,
              })
            },
            () => {
              // Fallback to simple scroll if measureLayout fails
              console.warn('Failed to measure tab layout')
            },
          )
        }
      }, 100)
    }
  }, [selectedTab])

  const tabTriggerClass = useMemo(() => {
    return {
      default:
        'self-start h-8 min-w-[67px] border border-transparent rounded-[60px] overflow-hidden bg-custom-neutral-50 px-3 py-1 text-center',
      active: 'border border-primary !bg-primary-50 text-primary',
      'text-default': 'text-custom-text-subdued text-center',
      'text-active': 'text-primary-500',
    }
  }, [])

  const handleTabChange = (value: string) => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light)
    setSelectedTab(value as NotificationTabType)
  }

  return (
    <View className="flex-1 px-4">
      <Tabs value={selectedTab} onValueChange={handleTabChange} className=" h-full flex-1 !px-0">
        <View className="flex h-full flex-col">
          <View className="w-full ">
            <ScrollView
              ref={scrollViewRef}
              horizontal
              showsHorizontalScrollIndicator={false}
              contentContainerStyle={{ paddingHorizontal: 0 }}
            >
              <TabsList className=" flex flex-row gap-x-2  !px-0">
                {USER_PUSH_NOTIFICATION_TAB_OPTIONS.map((tab) => (
                  <TabsTrigger
                    key={tab.value}
                    value={tab.value}
                    isActive={selectedTab === tab.value}
                    accessibilityLabel={t(tab.translationKey)}
                    accessibilityRole="tab"
                  >
                    <View
                      ref={(ref) => {
                        tabRefs.current[tab.value] = ref
                      }}
                      className={cn(
                        tabTriggerClass['default'],
                        selectedTab === tab.value &&
                          'border !border-primary !bg-primary-50 text-primary',
                      )}
                    >
                      <Text
                        size="body6"
                        className={cn(
                          tabTriggerClass['text-default'],
                          selectedTab === tab.value && tabTriggerClass['text-active'],
                        )}
                      >
                        {t(tab.translationKey)}
                      </Text>
                    </View>
                  </TabsTrigger>
                ))}
              </TabsList>
            </ScrollView>
          </View>

          <View className="flex-1 overflow-hidden p-0">
            {USER_PUSH_NOTIFICATION_TAB_OPTIONS.map((tab) => (
              <TabsContent key={tab.value} value={tab.value} className="h-full">
                <UserPushNotificationTabContent tabType={tab.value as NotificationTabType} />
              </TabsContent>
            ))}
          </View>
        </View>
      </Tabs>
    </View>
  )
}
