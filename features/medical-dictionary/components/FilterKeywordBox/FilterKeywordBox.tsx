import { useSheetActions } from '@/contexts/SheetContext/SheetContext'
import { useCallback, useEffect, useMemo, useRef } from 'react'
import { useTranslation } from 'react-i18next'
import { View } from 'react-native'
import { useShallow } from 'zustand/react/shallow'

import { BaseFilterListSection } from '@/components/Filter/BaseFilterListSection/BaseFilterListSection'
import {
  BaseFilterSheetBox,
  type BaseFilterSheetBoxRef,
} from '@/components/Filter/BaseFilterSheetBox/BaseFilterSheetBox'
import { KEYWORD_CATEGORY_OPTIONS } from '@/constants/keyword.constant'
import { BottomSheetScrollView } from '@gorhom/bottom-sheet'
import {
  MedicalDictionaryFilterType,
  useMedicalDictionaryStore,
} from '../../stores/MedicalDictionaryStore'
import { FilterKeywordFooter } from './FilterKeywordFooter'

interface FilterKeywordBoxProps {
  closeSheet?: () => void
  sharedFooterRef: React.RefObject<View | null>
  sharedSheetBoxRef?: React.RefObject<BaseFilterSheetBoxRef | null>
}

export const FilterKeywordBox = ({
  closeSheet,
  sharedFooterRef,
  sharedSheetBoxRef,
}: FilterKeywordBoxProps) => {
  const { t } = useTranslation()

  const localSheetBoxRef = useRef<BaseFilterSheetBoxRef>(null)
  const sheetBoxRef = sharedSheetBoxRef || localSheetBoxRef

  const keywordCategories = useMemo(() => {
    return Object.values(KEYWORD_CATEGORY_OPTIONS).map((item) => ({
      id: item.value,
      label: t(item.translationKey),
      type: MedicalDictionaryFilterType.KEYWORD_TYPE,
    }))
  }, [])

  const { toggleTempSearchFilter, initTempSearchFilters, tempFilters } = useMedicalDictionaryStore(
    useShallow((state) => ({
      toggleTempSearchFilter: state.toggleTempFilter,
      initTempSearchFilters: state.initTempFilters,
      tempFilters: state.tempFilters,
    })),
  )

  // Initialize temp search filters when component mounts
  useEffect(() => {
    initTempSearchFilters()
  }, [initTempSearchFilters])

  // Trigger manual footer measurement when filters change (no re-render!)
  useEffect(() => {
    // Small delay to allow footer DOM updates to complete
    const timer = setTimeout(() => {
      sheetBoxRef.current?.measureFooter()
    }, 100)

    return () => clearTimeout(timer)
  }, [tempFilters])

  return (
    <BaseFilterSheetBox
      ref={sheetBoxRef}
      title={t('MES-481')}
      onClose={closeSheet}
      footerRef={sharedFooterRef}
      enableFooterHeightMeasurement={true}
      footerHeightPadding={40}
      useBottomSheetScrollView={true}
    >
      <BottomSheetScrollView className="relative" showsVerticalScrollIndicator={false}>
        <View className="flex flex-col gap-y-3 px-4">
          <BaseFilterListSection
            title={t('MES-785')}
            data={keywordCategories}
            onSelectFilter={(item) => toggleTempSearchFilter(item?.type, item)}
            activeFilters={tempFilters?.[MedicalDictionaryFilterType.KEYWORD_TYPE]?.map(
              (filter) => filter.id,
            )}
            idKey="id"
            labelKey="label"
          />
        </View>
      </BottomSheetScrollView>
    </BaseFilterSheetBox>
  )
}

export const useOpenFilterKeywordBox = () => {
  const { openCustomSheet, closeSheet } = useSheetActions()

  const sharedFooterRef = useRef<View>(null)
  const sharedSheetBoxRef = useRef<BaseFilterSheetBoxRef>(null)

  const handleOpenFilterKeywordBox = useCallback(() => {
    openCustomSheet({
      children: ({ close }) => (
        <FilterKeywordBox
          closeSheet={close}
          sharedFooterRef={sharedFooterRef}
          sharedSheetBoxRef={sharedSheetBoxRef}
        />
      ),
      baseProps: {
        snapPoints: ['85%', '100%'],
        enableHandlePanningGesture: true,
        enableDynamicSizing: false,
        enableOverDrag: false,
      },
      options: {
        footerComponent: (props) => {
          return <FilterKeywordFooter ref={sharedFooterRef} closeSheet={closeSheet} {...props} />
        },
      },
    })
  }, [openCustomSheet])

  return { handleOpenFilterKeywordBox }
}
