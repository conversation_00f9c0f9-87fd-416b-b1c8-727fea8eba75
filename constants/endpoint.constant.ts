export const API_ENDPOINTS: {
  [key: string]: string
} = {
  posts_api: 'posts',
  post_categories_api: 'post-categories',
  medicines_api: 'medicines',
  medicine_categories_api: 'medicine-categories',
  favorite_medicines_api: 'favorite-medicines',
  users_api: 'users',
  versions_api: 'versions',
  faculties_api: 'faculties',
  subscriptions_api: 'subscriptions',
  email_subscriptions_api: 'email-subscriptions',
  coupons_api: 'coupons',
  payment_methods_api: 'payment-methods',
  user_subscriptions_api: 'user-subscriptions',
  payment_transactions_api: 'transactions',
  login_sessions_api: 'login-sessions',
  body_parts_api: 'body-parts',
  patient_groups_api: 'patient-group',
  medicine_types_api: 'medicine-type',
  user_examinations_api: 'user-examinations',
  examination_forms_api: 'examination-forms',
  examination_questions_api: 'examination-questions',
  keywords_api: 'keywords',
  ai_bot_api: 'ai-bot',
  supplement_category_api: 'dietary-supplement-categories',
  favorite_keywords_api: 'favorite-keywords',
  image_search_conversations_api: 'image-search-conversations',
  dynamic_popups_api: 'dynamic-popups',
  popup_tracking_api: 'popup-tracking',
  facts_api: 'facts',
  search_tips_api: 'search-tips',
  products_api: 'products',
  product_categories_api: 'product-categories',
  product_age_groups_api: 'product-age-groups',
  favorite_products_api: 'favorite-products',
  keyword_search_history_api: 'keyword-search-history',
  medical_document_api: 'medical-documents',
  medical_document_categories_api: 'medical-document-categories',
  hospitals_api: 'hospitals',
  push_notification_tokens_api: 'push-notification-tokens',
  user_push_notifications_api: 'user-push-notifications',
  comments_api: 'comments',
  comment_likes_api: 'comment-likes',
  comment_reports_api: 'comment-reports',
}

export const GLOBAL_DOC_API_ENDPOINTS: {
  [key: string]: string
} = {
  home_page_config_api: 'home-page-config',
}

export const CUSTOM_API_ENDPOINTS: {
  [key: string]: string
} = {
  ai_bot_api: 'ai-bot',
}
