// components/LoadingScreenIndicator.tsx
import React from 'react'
import { ActivityIndicator, Modal, StyleSheet, View } from 'react-native'

interface Props {
  visible: boolean
}

export const LoadingScreenIndicator = ({ visible }: Props) => {
  return (
    <Modal transparent animationType="fade" visible={visible}>
      <View style={styles.overlay}>
        <ActivityIndicator size="large" color="#fff" />
      </View>
    </Modal>
  )
}

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    backgroundColor: 'rgba(0,0,0,0.4)',
    justifyContent: 'center',
    alignItems: 'center',
  },
})
