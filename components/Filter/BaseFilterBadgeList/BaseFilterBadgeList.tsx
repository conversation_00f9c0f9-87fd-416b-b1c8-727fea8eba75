import { Text } from '@/components/ui/Text/Text'
import { useState } from 'react'
import { useTranslation } from 'react-i18next'
import { TouchableOpacity, View } from 'react-native'

// Generic filter item interface
export interface BaseFilterItem {
  id: string | number
  label: string
  type: string
}

// Generic badge component props
interface FilterBadgeProps<T extends BaseFilterItem> {
  filterItem: T
  onClearFilter?: (item: T) => void
}

// Default badge component
const DefaultFilterBadge = <T extends BaseFilterItem>({
  filterItem,
  onClearFilter,
}: FilterBadgeProps<T>) => {
  return (
    <TouchableOpacity
      onPress={() => onClearFilter?.(filterItem)}
      className="flex flex-row items-center gap-x-1 self-start rounded border border-primary p-1"
    >
      <Text size="body7" variant="primary">
        {filterItem.label}
      </Text>
      {/* You can add a close icon here if needed */}
      <Text size="body7" variant="primary">
        ×
      </Text>
    </TouchableOpacity>
  )
}

interface BaseFilterBadgeListProps<T extends BaseFilterItem> {
  activeFilters: T[]
  onClearFilter: (filterItem: T) => void
  maxDisplayCount?: number
  BadgeComponent?: React.ComponentType<FilterBadgeProps<T>>
  selectedFiltersLabel?: string
  showMoreText?: string
  showLessText?: string
}

export const BaseFilterBadgeList = <T extends BaseFilterItem>({
  activeFilters,
  onClearFilter,
  maxDisplayCount = 5,
  BadgeComponent = DefaultFilterBadge,
  selectedFiltersLabel,
  showMoreText,
  showLessText,
}: BaseFilterBadgeListProps<T>) => {
  const { t } = useTranslation()
  const [showAllFilters, setShowAllFilters] = useState(false)

  if (!activeFilters?.length) {
    return null
  }

  const displayedFilters = showAllFilters ? activeFilters : activeFilters.slice(0, maxDisplayCount)
  const hiddenFiltersCount = activeFilters.length - maxDisplayCount

  const toggleShowAllFilters = () => {
    setShowAllFilters(!showAllFilters)
  }

  return (
    <View className="flex flex-col gap-y-3">
      <Text size="body7" variant="subdued">
        {selectedFiltersLabel || t('MES-706')} ({activeFilters?.length}):
      </Text>
      <View className="flex flex-row flex-wrap gap-2">
        {displayedFilters?.map((filter: T) => (
          <BadgeComponent
            key={`${filter.type}-${filter.id}`}
            filterItem={filter}
            onClearFilter={onClearFilter}
          />
        ))}
        {!showAllFilters && hiddenFiltersCount > 0 && (
          <TouchableOpacity
            onPress={toggleShowAllFilters}
            className="rounded bg-primary-50 px-2 py-1"
          >
            <Text size="body6" variant="primary">
              {showMoreText || `+${hiddenFiltersCount}`}
            </Text>
          </TouchableOpacity>
        )}
        {showAllFilters && hiddenFiltersCount > 0 && (
          <TouchableOpacity
            onPress={toggleShowAllFilters}
            className="rounded bg-custom-danger-100 px-2 py-1"
          >
            <Text size="body6" variant="error">
              {showLessText || t('MES-493')}
            </Text>
          </TouchableOpacity>
        )}
      </View>
    </View>
  )
}
