import JapanFlag from '@/assets/login/japan-flag.svg'
import VietnamFlag from '@/assets/login/vietnam-flag.svg'
import { Text } from '@/components/ui/Text/Text'
import { LocaleEnum } from '@/enums/locale.enum'
import { useAppLanguage } from '@/hooks/common/useAppLanguage'
import * as Haptics from 'expo-haptics'
import React from 'react'
import { TouchableOpacity, View } from 'react-native'

export const LanguageSelector = () => {
  const { primaryLanguage, changeLanguage } = useAppLanguage()
  const handleChangeLanguage = (locale: LocaleEnum) => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light)
    changeLanguage(locale)
  }

  return (
    <View className="mt-4 flex-row items-center justify-center gap-10">
      <TouchableOpacity
        className="flex-row items-center justify-center gap-2"
        onPress={() => handleChangeLanguage(LocaleEnum.VI)}
      >
        <VietnamFlag width={16} height={16} />
        <Text size="body7" variant={primaryLanguage === LocaleEnum.VI ? 'primary' : 'default'}>
          Tiếng Việt
        </Text>
      </TouchableOpacity>

      <View className="h-4 w-[1px] bg-neutral-200" />

      <TouchableOpacity
        className="flex-row items-center justify-center gap-2"
        onPress={() => handleChangeLanguage(LocaleEnum.JA)}
      >
        <JapanFlag width={16} height={16} />
        <Text size="body7" variant={primaryLanguage === LocaleEnum.JA ? 'primary' : 'default'}>
          日本語
        </Text>
      </TouchableOpacity>
    </View>
  )
}
