import { Params } from '@/types/http.type'
import { UseQueryOptions, useQuery } from '@tanstack/react-query'
import { AxiosRequestConfig } from 'axios'
import { hospitalPartnerService } from '../../services/hospital-partner.service'
import { Hospital } from '../../types'
import { hospitalPartnersQueryKeys } from './queryKeys'

export const useGetHospitalDetails = ({
  id,
  params = {},
  options = {},
  useQueryOptions,
}: {
  id: string
  params?: Params
  options?: AxiosRequestConfig
  useQueryOptions?: Omit<UseQueryOptions<Hospital>, 'queryKey' | 'queryFn'>
}) => {
  const {
    isError: isGetHospitalDetailsError,
    isPending: isGetHospitalDetailsLoading,
    data: hospitalDetails,
    ...rest
  } = useQuery({
    queryKey: [hospitalPartnersQueryKeys['hospital-details'].base(), id, params],
    queryFn: async () =>
      hospitalPartnerService.getHospital({
        id,
        params: params,
        ...options,
      }),
    ...useQueryOptions,
  })
  return {
    isGetHospitalDetailsError,
    isGetHospitalDetailsLoading,
    hospitalDetails,
    ...rest,
  }
}
