import FilterIcon from '@/assets/icons/filter-icon.svg'
import { SearchInput, SearchInputRef } from '@/components/ui/SearchInput/SearchInput'
import { useEffect, useRef, useState } from 'react'
import { useTranslation } from 'react-i18next'
import { Keyboard, TouchableOpacity, View } from 'react-native'
import { useShallow } from 'zustand/react/shallow'

import { Text } from '@/components/ui/Text/Text'
import { useAuthentication } from '@/hooks/auth/useAuthentication'
import { APP_ROUTES, AppRoutesEnum } from '@/routes/appRoutes'
import { Link, useLocalSearchParams } from 'expo-router'
import { useGetKeywordSearchHistoryByUser } from '../../hooks/query/useGetKeywordSearchHistoryByUser'
import { useMedicalDictionaryStore } from '../../stores/MedicalDictionaryStore'
import { useOpenFilterKeywordBox } from '../FilterKeywordBox/FilterKeywordBox'
import { KeywordSearchHistoryBadgeList } from '../KeywordSearchHistoryBadgeList/KeywordSearchHistoryBadgeList'
import { SeachTipsBox } from './SeachTipsBox'

interface SearchKeywordsHeaderProps {
  showTipsBox?: boolean
}

export const SearchKeywordsHeader = ({ showTipsBox }: SearchKeywordsHeaderProps) => {
  const { name } = useLocalSearchParams()
  const { user, status } = useAuthentication()

  const { t } = useTranslation()
  const { hasActiveFilters, searchTextValue } = useMedicalDictionaryStore(
    useShallow((state) => ({
      hasActiveFilters: state.hasActiveFilters,
      searchTextValue: state.searchText,
    })),
  )
  const [searchInputValue, setSearchInputValue] = useState(name as string)
  const searchInputRef = useRef<SearchInputRef>(null)

  const {
    keywordSearchHistoryByUser,
    isGetKeywordSearchHistoryByUserLoading,
    isFetching,
    isRefetching,
  } = useGetKeywordSearchHistoryByUser({
    params: {
      locale: 'all',
      limit: 10,
      populate: {
        keywords: {
          name: true,
        },
      },
    },
    useQueryOptions: {
      staleTime: 3 * 60 * 1000,
      enabled: Boolean(status === 'success' && user),
    },
  })

  const handleSearchInputChange = (text: string) => {
    setSearchInputValue(text)
  }

  const handleClearSearchInput = () => {
    setSearchInputValue('')
    setSearchTextValue('')
  }

  const { setSearchTextValue } = useMedicalDictionaryStore(
    useShallow((state) => ({
      setSearchTextValue: state.setSearchText,
    })),
  )

  const { handleOpenFilterKeywordBox } = useOpenFilterKeywordBox()
  const handleSearchHistoryPress = (keywordName: string) => {
    setSearchTextValue(keywordName)
    setSearchInputValue(keywordName)
    Keyboard.dismiss()
  }
  useEffect(() => {
    if (name) {
      setSearchTextValue(name as string)
    } else {
      setTimeout(() => {
        searchInputRef.current?.focus()
      }, 500)
    }
  }, [])

  const isLoading = isGetKeywordSearchHistoryByUserLoading || isFetching || isRefetching

  return (
    <View className="flex flex-col gap-y-4">
      <View className="flex flex-row items-center gap-x-2">
        {/* Search Input */}
        <SearchInput
          ref={searchInputRef}
          placeholder={t('MES-66')}
          value={searchInputValue}
          onChangeText={handleSearchInputChange}
          onClear={handleClearSearchInput}
          onSubmitEditing={() => {
            setSearchTextValue(searchInputValue)
          }}
        />

        {/* Filter Button */}
        <TouchableOpacity
          hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
          onPress={() => {
            Keyboard.dismiss()
            handleOpenFilterKeywordBox()
          }}
          className="relative flex h-full flex-row items-center gap-x-2 rounded-lg border border-custom-divider-border p-3"
        >
          <FilterIcon width={18} height={18} />
          {hasActiveFilters && (
            <View className="absolute -right-1 -top-1 size-4 rounded-full bg-custom-danger-600/80" />
          )}
        </TouchableOpacity>
      </View>
      {showTipsBox && <SeachTipsBox />}
      {!searchTextValue && !hasActiveFilters && (
        <>
          {status === 'success' && user ? (
            <>
              <View className="flex flex-col gap-y-4">
                {keywordSearchHistoryByUser?.docs &&
                  keywordSearchHistoryByUser?.docs?.length > 0 && (
                    <View className="flex flex-row items-center justify-between gap-x-2">
                      <Text size="body6">{t('MES-777')}</Text>
                      <Link
                        href={
                          APP_ROUTES.MEDICAL_DICTIONARY.children?.[
                            AppRoutesEnum.MEDICAL_DICTIONARY_SEARCH_HISTORY
                          ].path
                        }
                        asChild
                      >
                        <TouchableOpacity>
                          <Text size="body6" variant="primary">
                            {t('MES-141')}
                          </Text>
                        </TouchableOpacity>
                      </Link>
                    </View>
                  )}

                <KeywordSearchHistoryBadgeList
                  keywordSearchHistory={keywordSearchHistoryByUser?.docs}
                  isLoading={isLoading}
                  onPress={handleSearchHistoryPress}
                />
              </View>
            </>
          ) : null}
        </>
      )}
    </View>
  )
}
