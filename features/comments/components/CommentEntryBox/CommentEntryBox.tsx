import DefaultAvatarIcon from '@/assets/icons/default-avatar-icon.svg'
import SendIcon from '@/assets/icons/send-icon.svg'
import { Text } from '@/components/ui/Text/Text'
import { useAuthentication } from '@/hooks/auth/useAuthentication'
import { StyledExpoImage } from '@/libs/styled'
import { Media } from '@/types/media.type'
import { useTranslation } from 'react-i18next'
import { TouchableOpacity, View } from 'react-native'
interface CommentEntryBoxProps {
  onPress: () => void
}
export const CommentEntryBox = ({ onPress }: CommentEntryBoxProps) => {
  const { user } = useAuthentication()
  const { thumbnailURL, url } = (user?.avatar as Media) || {}
  const oauthAvatar = user?.oauthAvatar as string
  const { t } = useTranslation()
  return (
    <TouchableOpacity
      className="flex flex-row items-start gap-x-3 rounded-lg border border-custom-divider p-3"
      onPress={onPress}
    >
      {thumbnailURL || url || oauthAvatar ? (
        <View className="h-12 w-12 flex-shrink-0 rounded-full border border-gray-100">
          <StyledExpoImage
            source={thumbnailURL || url || oauthAvatar}
            className="h-full w-full rounded-full"
          />
        </View>
      ) : (
        <View className="shrink-0">
          <DefaultAvatarIcon width={36} height={36} />
        </View>
      )}
      <Text size="field2" variant="subdued">
        {t('MES-846')}
      </Text>
      <View className="ml-auto">
        <SendIcon width={24} height={24} />
      </View>
    </TouchableOpacity>
  )
}
