import { LocaleEnum } from '@/enums/locale.enum'
import {
  PushNotificationMessagesPlatformGroupsEnum,
  PushNotificationMessagesSendToEnum,
  PushNotificationMessagesTypeEnum,
} from '@/features/notification/enums/push-notification.enum'
import { Post } from '@/features/post/types'
import { Product } from '@/features/product/types'
import { Media } from '@/types/media.type'

import { User } from '@/types/user.type'

// Base notification interface for both push and local notifications
export interface BaseNotification {
  title: string
  description: string
  data?: Record<string, any>
}

// Push notification specific types
export interface PushNotificationToken {
  id: string
  user?: User | null
  token: string
  platform: PushNotificationMessagesPlatformGroupsEnum
  appVersion?: string | null
  lastRegistered?: string | null
  language: LocaleEnum
  authNotifiable?: boolean | null
  invalid?: boolean | null
  updatedAt: string
  createdAt: string
}

export interface CreatePushNotificationTokenPayload {
  platform: PushNotificationMessagesPlatformGroupsEnum
  appVersion: string | undefined
  authNotifiable: boolean
  language: LocaleEnum
  token: string
}

export interface CreatePushNotificationTokenResponse {
  message: string
  doc: {
    id: string
    token: string
  }
}

export interface CheckTokenValidityPayload {
  token: string
  platform: PushNotificationMessagesPlatformGroupsEnum
}

export interface CheckTokenValidityResponse {
  message: string
  isValid: boolean
  exists: boolean
  token: string
  platformMismatch?: boolean
}

export interface UpdateAuthNotifiableHandlerPayload {
  token: string
  authNotifiable: boolean
}

export interface UpdateAuthNotifiableHandlerResponse {
  message: string
  doc: {
    id: string
    token: string
    authNotifiable: boolean
  }
}

export interface UpdateNotificationLanguagePayload {
  token: string
  language: LocaleEnum
}

export interface UpdateNotificationLanguageResponse {
  message: string
  doc: {
    id: string
    token: string
    language: LocaleEnum
  }
}

export interface AttachUserToPushNotificationTokenPayload {
  token: string
}

export interface AttachUserToPushNotificationTokenResponse {
  message: string
  doc: {
    id: string
    token: string
  }
  success: boolean
}

// FCM specific types
export interface FCMNotificationData {
  type?: string
  chatId?: string
  appointmentId?: string
  medicineId?: string
  [key: string]: any
}

export interface FCMMessage {
  messageId: string
  data?: FCMNotificationData
  notification?: {
    title?: string
    body?: string
    imageUrl?: string
  }
  from?: string
  to?: string
  collapseKey?: string
  sentTime?: number
  ttl?: number
}

// Local notification types
export interface LocalNotificationOptions {
  title: string
  body: string
  data?: Record<string, any>
  sound?: boolean
  badge?: number
  channelId?: string
}

export interface ScheduledNotification {
  identifier: string
  content: {
    title: string
    body: string
    data?: Record<string, any>
  }
  trigger: any
  date: Date
}

export interface NotificationPayload {
  notification: {
    title: string
    body: string
    image?: string
  }
  data?: Record<string, any> & {
    trackingId?: string
  }
  android?: {
    notification: {
      imageUrl?: string
    }
  }
  apns?: {
    payload: {
      aps: {
        'mutable-content': number
      }
    }
    fcm_options: {
      image?: string
    }
  }
}

export interface NotificationJob {
  payload: NotificationPayload
  tokens: string[]
  language: string
  allTokens: PushNotificationToken[]
  notificationType: string
}

export interface NotificationTemplate {
  language: string
  titleTemplate?: string
  titleTemplateCustomText?: string
  descriptionTemplate?: string
  descriptionTemplateCustomText?: string
}

export interface SendToSettings {
  sendTo: PushNotificationMessagesSendToEnum
  platformGroups?: string[]
  specificUsers?: (string | User)[]
}

export interface NewPostNotificationData {
  post: string | Post
  templates: NotificationTemplate[]
  sendToSettings: SendToSettings
}

export interface NewProductNotificationData {
  product: string | Product
  templates: NotificationTemplate[]
  sendToSettings: SendToSettings
}

export interface GeneralNotificationTemplate extends NotificationTemplate {}

export interface GeneralNotificationData {
  templates: GeneralNotificationTemplate[]
  image?: string | Media
  sendToSettings: SendToSettings
}

export interface AppSystemNotificationTemplate extends NotificationTemplate {}

export interface AppSystemNotificationData {
  templates: AppSystemNotificationTemplate[]
  image?: string | Media
  sendToSettings: SendToSettings
}

export interface UserPushNotification {
  id: string
  trackingId: string
  user: string | User | null
  type: PushNotificationMessagesTypeEnum
  title: string
  description?: string | null
  data?:
    | GeneralNotificationBaseData
    | NewPostNotificationBaseData
    | NewProductNotificationBaseData
    | AppSystemNotificationBaseData
    | ChatMessageNotificationBaseData
  viewed?: boolean | null
  viewedAt?: string | null
  sentAt?: string | null
  updatedAt: string
  createdAt: string
}

export interface NotificationBaseData {
  trackingId?: string
  notiId?: string
  topic?: string
}

export interface GeneralNotificationBaseData extends NotificationBaseData {}

export interface NewPostNotificationBaseData extends NotificationBaseData {
  postId: string
  postSlug: string
}

export interface NewProductNotificationBaseData extends NotificationBaseData {
  productId: string
  productSlug: string
}

export interface AppSystemNotificationBaseData extends NotificationBaseData {}

export interface ChatMessageNotificationBaseData extends NotificationBaseData {
  chatId: string
}

export interface ChatMessageNotificationData extends ChatMessageNotificationBaseData {
  message: string
  userId: string
  conversationId: string
}

export interface MarkNotificationAsViewedPayload {
  trackingId: string
}

export interface MarkNotificationAsViewedResponse {
  message: string
}

export interface MarkAllChatNotificationAsViewedResponse {
  message: string
  updatedCount: number
}

export interface GetUserUnviewedNotificationCountResponse {
  id: string
  unviewedNotificationsCount: number
}

export interface CommentReplyNotificationBaseData extends NotificationBaseData {
  parentAuthorId: string
  commentContent: string
  documentId: string
  relationTo: 'posts' | 'products' | 'keywords'
  commentId: string
  replyUserName: string
  docSlug?: string
  type: PushNotificationMessagesTypeEnum.COMMENT_REPLY
  trackingId: string
  docName?: string | Record<string, string>

  parentCommentId?: string
}

export interface NewCommentNotificationBaseData extends NotificationBaseData {
  commentId: string
  commentContent: string
  commentAuthorId: string
  commentAuthorName: string
  documentId: string
  relationTo: 'posts' | 'products' | 'keywords'
  docSlug?: string
  type: PushNotificationMessagesTypeEnum.NEW_COMMENT
  trackingId: string
  docName?: string | Record<string, string>
}
