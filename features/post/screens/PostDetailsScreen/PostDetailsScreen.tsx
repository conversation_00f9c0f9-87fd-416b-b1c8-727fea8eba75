import CommentIcon from '@/assets/icons/comment-noti-white-icon.svg'
import { BaseWebView } from '@/components/BaseWebView/BaseWebView'
import { Text } from '@/components/ui/Text/Text'
import { MESSAGE_NATIVE } from '@/constants/message-native.constant'
import { APP_ROUTES } from '@/routes/appRoutes'
import { WEBVIEW_APP_ROUTES } from '@/routes/webviewRoutes'
import { Link, LinkProps } from 'expo-router'
import { useState } from 'react'
import { useTranslation } from 'react-i18next'
import { Platform, TouchableOpacity, View } from 'react-native'
import { useSafeAreaInsets } from 'react-native-safe-area-context'
import { WebViewMessageEvent } from 'react-native-webview'
interface PostDetailsScreenProps {
  slug: string
  id: string
}
export const PostDetailsScreen = ({ slug, id }: PostDetailsScreenProps) => {
  const { t } = useTranslation()
  const insets = useSafeAreaInsets()

  const [isFinishedLoading, setIsFinishedLoading] = useState(false)

  const [totalComment, setTotalComment] = useState(0)
  const handleMessage = async (e: WebViewMessageEvent) => {
    const event = JSON.parse(e.nativeEvent.data)
    switch (event.action) {
      case MESSAGE_NATIVE.finishedLoading: {
        setIsFinishedLoading(true)
        break
      }

      case MESSAGE_NATIVE.updateTotalComment: {
        const { payload } = event
        setTotalComment(payload.totalComments)
        break
      }

      default:
        break
    }
  }
  return (
    <View className="flex-1 flex-col" style={{}}>
      <View className="flex-1">
        <BaseWebView
          source={{
            uri: WEBVIEW_APP_ROUTES.POSTS?.path + `/${slug}`,
          }}
          onMessage={handleMessage}
          // containerStyle={{ paddingBottom: 24 }}
        />
      </View>

      {/* Comment Button */}
      {isFinishedLoading && (
        <View
          className="px-4 py-4"
          style={{
            borderTopLeftRadius: 16,
            borderTopRightRadius: 16,
            boxShadow: '0px 4px 22px 0px #00000026',
            // paddingBottom: 24,
            paddingBottom: Platform.OS === 'android' ? insets.bottom + 8 : 24,
          }}
        >
          <Link
            href={
              {
                pathname: APP_ROUTES.COMMENTS.path,
                params: {
                  id: id as string,
                  relationTo: 'posts',
                  // staleTime: 5 * 60 * 1000,
                  // gcTime: 5 * 60 * 1000,
                  // customTitle: t('MES-853'),
                },
              } as LinkProps['href']
            }
            asChild
            className="mx-auto flex w-full flex-row items-center justify-center gap-x-2 rounded-lg bg-primary p-3"
          >
            <TouchableOpacity>
              <CommentIcon />
              <Text size="body6" variant="white">
                {t('MES-853')} ({totalComment || 0})
              </Text>
            </TouchableOpacity>
          </Link>
        </View>
      )}
    </View>
  )
}
