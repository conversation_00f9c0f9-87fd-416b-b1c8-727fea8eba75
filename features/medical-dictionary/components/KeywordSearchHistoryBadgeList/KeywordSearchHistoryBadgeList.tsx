import { Skeleton } from '@/components/ui/Skeleton/Skeleton'
import { Text } from '@/components/ui/Text/Text'
import { LocaleEnum } from '@/enums/locale.enum'
import { useAppLanguage } from '@/hooks/common/useAppLanguage'
import { APP_ROUTES, AppRoutesEnum } from '@/routes/appRoutes'
import { LocalizeField } from '@/types/global.type'
import { Keyword } from '@/types/keyword.type'
import { Link } from 'expo-router'
import { TouchableOpacity, useWindowDimensions, View } from 'react-native'
import { KeywordSearchHistory } from '../../types'

interface KeywordSearchHistoryBadgeListProps {
  keywordSearchHistory?: KeywordSearchHistory[]
  isLoading?: boolean
  limit?: number
  limitWidth?: number
  onPress?: (keyword: string) => void
}

export function KeywordSearchHistoryBadgeList({
  keywordSearchHistory = [],
  isLoading = false,
  limit = 6,
  limitWidth,
  onPress,
}: KeywordSearchHistoryBadgeListProps) {
  const { primaryLanguage, secondaryLanguage } = useAppLanguage()
  const { width } = useWindowDimensions()
  if (isLoading) {
    return <KeywordSearchHistoryBadgeListLoading />
  }

  return (
    <View className="flex flex-row flex-wrap gap-2">
      {keywordSearchHistory?.slice(0, limit).map((item) => {
        const keyword = item.keyword as Keyword
        if (!keyword || typeof keyword === 'string') {
          return null
        }
        const { name } = keyword
        const localizedName = name as unknown as LocalizeField<string>
        const mergedName = `${localizedName?.[primaryLanguage as LocaleEnum] ?? ''} ${localizedName?.[secondaryLanguage as LocaleEnum] ? '(' + localizedName?.[secondaryLanguage as LocaleEnum] + ')' : ''}`
        return (
          <Link
            href={{
              pathname:
                APP_ROUTES.MEDICAL_DICTIONARY?.children?.[AppRoutesEnum.MEDICAL_DICTIONARY_SEARCH]
                  ?.path,
              params: {
                name:
                  localizedName[primaryLanguage as LocaleEnum] ||
                  localizedName[secondaryLanguage as LocaleEnum] ||
                  '',
              },
            }}
            asChild
            key={item.id}
            onPress={() => {
              onPress?.(
                localizedName[primaryLanguage as LocaleEnum] ||
                  localizedName[secondaryLanguage as LocaleEnum] ||
                  '',
              )
            }}
          >
            <TouchableOpacity
              className="  rounded-[99px] bg-custom-neutral-50 px-3 py-2"
              style={{
                maxWidth: limitWidth ? limitWidth : width > 600 ? 360 : width - 32,
              }}
            >
              <Text size="body7" variant="subdued" numberOfLines={1}>
                {mergedName}
              </Text>
            </TouchableOpacity>
          </Link>
        )
      })}
    </View>
  )
}

export const KeywordSearchHistoryBadgeListLoading = () => {
  return (
    <View className="flex flex-row flex-wrap gap-2">
      <View className="h-9 w-[120px] overflow-hidden rounded-[99px]">
        <Skeleton className={`h-full w-full `} />
      </View>
      <View className="h-9 w-[180px] overflow-hidden rounded-[99px]">
        <Skeleton className={`h-full w-full `} />
      </View>
      <View className="h-9 w-[80px] overflow-hidden rounded-[99px]">
        <Skeleton className={`h-full w-full `} />
      </View>
      <View className="h-9 w-[240px] overflow-hidden rounded-[99px]">
        <Skeleton className={`h-full w-full `} />
      </View>
      <View className="h-9 w-[80px] overflow-hidden rounded-[99px]">
        <Skeleton className={`h-full w-full `} />
      </View>
    </View>
  )
}
