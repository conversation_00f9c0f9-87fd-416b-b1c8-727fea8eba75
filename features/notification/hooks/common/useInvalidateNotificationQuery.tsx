import { useQueryClient } from '@tanstack/react-query'
import { PushNotificationMessagesTabEnum } from '../../enums/push-notification.enum'
import { unifiedNotificationQueryKeys } from '../query/queryKeys'

export const useInvalidateNotificationQuery = () => {
  const queryClient = useQueryClient()

  const handleInvalidateUnviewedNotificationCount = () => {
    queryClient.invalidateQueries({
      queryKey: [unifiedNotificationQueryKeys['user-unviewed-notification-count'].base()],
      type: 'active',
      refetchType: 'active',
    })
  }

  const handleResetUnviewedNotificationCount = () => {
    queryClient.resetQueries({
      queryKey: [unifiedNotificationQueryKeys['user-unviewed-notification-count'].base()],
      type: 'all',
    })
  }
  const handleInvalidateUserPushNotifications = (tabType: PushNotificationMessagesTabEnum) => {
    queryClient.invalidateQueries({
      queryKey: [...unifiedNotificationQueryKeys['user-push-notifications'].base(), tabType],
      type: 'active',
      refetchType: 'active',
    })
  }

  const handleResetAllUserPushNotifications = () => {
    queryClient.resetQueries({
      queryKey: [...unifiedNotificationQueryKeys['user-push-notifications'].base()],
      type: 'all',

      exact: false,
    })
  }

  const handleInvalidateAllUserPushNotifications = () => {
    queryClient.invalidateQueries({
      queryKey: [...unifiedNotificationQueryKeys['user-push-notifications'].base()],
      type: 'all',
      refetchType: 'all',
      exact: false,
    })
  }

  const handleRemoveAllNotificationQueries = () => {
    queryClient.removeQueries({
      queryKey: [...unifiedNotificationQueryKeys['user-push-notifications'].base()],
      type: 'all',
    })
    queryClient.removeQueries({
      queryKey: [unifiedNotificationQueryKeys['user-unviewed-notification-count'].base()],
      type: 'all',
    })
  }

  const handleResetAllNotificationQueries = async () => {
    await queryClient.resetQueries({
      queryKey: [...unifiedNotificationQueryKeys['user-push-notifications'].base()],
      type: 'all',
      exact: false,
    })
    await queryClient.resetQueries({
      queryKey: [unifiedNotificationQueryKeys['user-unviewed-notification-count'].base()],
      type: 'all',
      exact: false,
    })
  }
  return {
    handleInvalidateUnviewedNotificationCount,
    handleInvalidateUserPushNotifications,
    handleInvalidateAllUserPushNotifications,
    handleResetAllUserPushNotifications,
    handleResetUnviewedNotificationCount,
    handleRemoveAllNotificationQueries,
    handleResetAllNotificationQueries,
  }
}
