import { keywordService } from '@/services/handbook/keyword.service'
import { PaginatedDocs } from '@/types/global.type'
import { Params } from '@/types/http.type'
import { Keyword } from '@/types/keyword.type'
import { UseQueryOptions, useQuery } from '@tanstack/react-query'
import { AxiosRequestConfig } from 'axios'
import { keywordQueryKeys } from './queryKeys'

export const useGetKeywords = ({
  params = {},
  options = {},
  useQueryOptions,
}: {
  params?: Params
  options?: AxiosRequestConfig
  useQueryOptions?: Omit<UseQueryOptions<PaginatedDocs<Keyword> | null>, 'queryKey' | 'queryFn'>
}) => {
  const {
    isError: isGetKeywordsError,
    isPending: isGetKeywordsLoading,
    data: keywords,
    ...rest
  } = useQuery({
    queryKey: [keywordQueryKeys['keywords'].base(), params],
    queryFn: async () =>
      keywordService.getKeywords({
        params: params,
        ...options,
      }),
    ...useQueryOptions,
  })
  return {
    isGetKeywordsError,
    isGetKeywordsLoading,
    keywords,
    ...rest,
  }
}
