import CloseIcon from '@/assets/icons/close-icon.svg'
import { Text } from '@/components/ui/Text/Text'
import { TouchableOpacity } from 'react-native'
import { ProductFilterItem, SearchProductFilterItem } from '../../stores/ProductStore'

// Union type for both filter item types
type FilterItem = ProductFilterItem | SearchProductFilterItem

interface SelectedFilterBadgeProps {
  filterItem: FilterItem
  onClearFilter?: (item: FilterItem) => void
}

export const SelectedFilterBadge = ({ filterItem, onClearFilter }: SelectedFilterBadgeProps) => {
  const getFilterLabel = (item: FilterItem): string => {
    return item.label
  }

  return (
    <TouchableOpacity
      onPress={() => onClearFilter?.(filterItem)}
      className="flex flex-row items-center gap-x-1 self-start rounded border border-primary p-1"
    >
      <Text size="body7" variant="primary">
        {getFilterLabel(filterItem)}
      </Text>
      <CloseIcon width={18} height={18} />
    </TouchableOpacity>
  )
}
