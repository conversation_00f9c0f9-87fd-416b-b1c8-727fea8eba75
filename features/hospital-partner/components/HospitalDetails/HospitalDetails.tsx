import colors from '@/styles/_colors'
import { useLocalSearchParams } from 'expo-router'
import { useState } from 'react'
import { ScrollView, View } from 'react-native'
import { RefreshControl } from 'react-native-gesture-handler'
import { useGetHospitalDetails } from '../../hooks/query/useGetHospitalDetails'
import { HospitalDetailsExaminationFormsSection } from './HospitalDetailsExaminationFormsSection'
import { HospitalDetailsInfoSection } from './HospitalDetailsInfoSection'
import { HospitalDetailsMedicalDocumentCategories } from './HospitalDetailsMedicalDocumentCategories'
import { HospitalDetailsSkeleton } from './HospitalDetailsSkeleton'

export const HospitalDetails = () => {
  const { id } = useLocalSearchParams()
  const [refreshing, setRefreshing] = useState(false)
  const { hospitalDetails, isGetHospitalDetailsLoading, isFetching, refetch } =
    useGetHospitalDetails({
      id: id as string,

      useQueryOptions: {
        enabled: Boolean(id),
        staleTime: 0,
      },
    })
  const handleRefresh = async () => {
    setRefreshing(true)
    await refetch()
    setRefreshing(false)
  }
  if (isGetHospitalDetailsLoading || isFetching) {
    return <HospitalDetailsSkeleton />
  }
  if (!hospitalDetails) {
    return null
  }
  return (
    <ScrollView
      className="flex-1  bg-white "
      showsVerticalScrollIndicator={false}
      contentContainerStyle={{
        paddingBottom: 20,
      }}
      refreshControl={
        <RefreshControl
          refreshing={refreshing}
          onRefresh={handleRefresh}
          colors={[colors.primary['500']]}
          tintColor={colors.primary['500']}
          progressBackgroundColor="#FFFFFF"
        />
      }
    >
      <View className="flex flex-col gap-y-3 py-3">
        <HospitalDetailsInfoSection hospital={hospitalDetails} />
        <HospitalDetailsExaminationFormsSection hospital={hospitalDetails} />
        <HospitalDetailsMedicalDocumentCategories
          isRefreshing={refreshing}
          hospital={hospitalDetails}
        />
      </View>
    </ScrollView>
  )
}
