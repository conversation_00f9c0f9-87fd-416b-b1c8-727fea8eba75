import { BaseWebView } from '@/components/BaseWebView/BaseWebView'
import { StackScreenBase } from '@/components/StackBase/StackScreenBase'
import { withAuthentication } from '@/hoc/withAuthentication'
import { WEBVIEW_APP_ROUTES } from '@/routes/webviewRoutes'
import React from 'react'
import { useTranslation } from 'react-i18next'
function SearchMedicineScreen() {
  const { t } = useTranslation()
  return (
    <>
      <StackScreenBase
        options={{
          headerTitle: t('MES-642'),
        }}
      />
      <BaseWebView
        source={{ uri: WEBVIEW_APP_ROUTES.CHAT_BOT?.children?.CHAT_BOT_SEARCH_MEDICINE?.path }}
      />
    </>
  )
}
export default withAuthentication(SearchMedicineScreen)
