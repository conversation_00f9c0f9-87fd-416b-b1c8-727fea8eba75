import { Text } from '@/components/ui/Text/Text'
import { HomeFeaturedCategory } from '@/constants/navigation.constant'
import { useAuthentication } from '@/hooks/auth/useAuthentication'
import { APP_ROUTES } from '@/routes/appRoutes'
import * as Haptics from 'expo-haptics'
import { Link, LinkProps } from 'expo-router'
import React from 'react'
import { TouchableOpacity, View } from 'react-native'

export const FeatureCard = ({
  icon: IconComponent,
  title,
  url,
  authRequired,
}: HomeFeaturedCategory) => {
  const { status, user } = useAuthentication()
  const href = authRequired ? (user && status === 'success' ? url : APP_ROUTES.LOGIN.path) : url

  return (
    <Link href={href as LinkProps['href']} asChild>
      <TouchableOpacity
        className="flex-1 items-center justify-center gap-2 rounded-[8px] border border-custom-neutral-50 bg-white p-3"
        onPress={() => {
          Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light)
        }}
      >
        <View className="flex-col items-center justify-center gap-2">
          <View className="h-[45px] w-[45px] items-center justify-center rounded-[6px] bg-primary-50">
            <IconComponent width={28} height={28} />
          </View>

          <Text size="body8" className="line-clamp-2 h-10 text-center" numberOfLines={2}>
            {title}
          </Text>
        </View>
      </TouchableOpacity>
    </Link>
  )
}
