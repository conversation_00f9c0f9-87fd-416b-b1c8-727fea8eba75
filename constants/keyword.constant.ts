import AllergyIcon from '@/assets/icons/allergy-icon.svg'
import BrainIcon from '@/assets/icons/brain-icon.svg'
import DrugPillV2Icon from '@/assets/icons/drug-pill-v2-icon.svg'
import IllnessPeopleIcon from '@/assets/icons/illness-people-icon.svg'
import LifeStyleIcon from '@/assets/icons/life-style-icon.svg'
import MedicalScienceIcon from '@/assets/icons/medical-scienece-icon.svg'
import OtherCategoriesDotIcon from '@/assets/icons/other-categories-dot-icon.svg'
import PillBlisterIcon from '@/assets/icons/pill-blister-icon.svg'
import SyringeIcon from '@/assets/icons/syringe-icon.svg'
import { KeywordCategoryEnum } from '@/enums/keyword.enum'
import { LocaleEnum } from '@/enums/locale.enum'
export const getKeywordCategoryLabel = (
  category: KeywordCategoryEnum,
  locale: string = LocaleEnum.VI,
): string => {
  const labels: Record<KeywordCategoryEnum, Record<string, string>> = {
    [KeywordCategoryEnum.BODY_PART]: {
      [LocaleEnum.VI]: 'Bộ phận cơ thể',
      [LocaleEnum.JA]: '体の部位',
    },
    [KeywordCategoryEnum.MEDICINE]: {
      [LocaleEnum.VI]: 'Thuốc/TPCN',
      [LocaleEnum.JA]: '薬',
    },
    [KeywordCategoryEnum.MEDICINE_TYPE]: {
      [LocaleEnum.VI]: 'Loại thuốc/TPCN',
      [LocaleEnum.JA]: '薬の種類',
    },
    [KeywordCategoryEnum.ALLERGY_TYPE]: {
      [LocaleEnum.VI]: 'Loại dị ứng',
      [LocaleEnum.JA]: 'アレルギーの種類',
    },
    [KeywordCategoryEnum.SYMPTOM_OR_DISEASE]: {
      [LocaleEnum.VI]: 'Triệu chứng/Tên bệnh',
      [LocaleEnum.JA]: '症状/病名',
    },
    [KeywordCategoryEnum.TEST_VACCINE_TREATMENT]: {
      [LocaleEnum.VI]: 'Xét nghiệm/Tiêm phòng/Điều trị',
      [LocaleEnum.JA]: '検査/予防接種/治療',
    },
    [KeywordCategoryEnum.LIFESTYLE]: {
      [LocaleEnum.VI]: 'Lối sống',
      [LocaleEnum.JA]: 'ライフスタイル',
    },
    [KeywordCategoryEnum.MEDICAL_SCIENCE]: {
      [LocaleEnum.VI]: 'Y tế và Khoa học',
      [LocaleEnum.JA]: '医療と科学',
    },
    [KeywordCategoryEnum.OTHER]: {
      [LocaleEnum.VI]: 'Khác',
      [LocaleEnum.JA]: '他の',
    },
  }

  return labels[category][locale]
}

export const KEYWORD_CATEGORY_OPTIONS = {
  [KeywordCategoryEnum.BODY_PART]: {
    value: KeywordCategoryEnum.BODY_PART,
    label: {
      [LocaleEnum.VI]: getKeywordCategoryLabel(KeywordCategoryEnum.BODY_PART, LocaleEnum.VI),
      [LocaleEnum.JA]: getKeywordCategoryLabel(KeywordCategoryEnum.BODY_PART, LocaleEnum.JA),
    },
    translationKey: 'MES-36',
    icon: BrainIcon,
  },
  [KeywordCategoryEnum.MEDICINE]: {
    value: KeywordCategoryEnum.MEDICINE,
    label: {
      [LocaleEnum.VI]: getKeywordCategoryLabel(KeywordCategoryEnum.MEDICINE, LocaleEnum.VI),
      [LocaleEnum.JA]: getKeywordCategoryLabel(KeywordCategoryEnum.MEDICINE, LocaleEnum.JA),
    },
    translationKey: 'MES-762',
    icon: DrugPillV2Icon,
  },
  [KeywordCategoryEnum.MEDICINE_TYPE]: {
    value: KeywordCategoryEnum.MEDICINE_TYPE,
    label: {
      [LocaleEnum.VI]: getKeywordCategoryLabel(KeywordCategoryEnum.MEDICINE_TYPE, LocaleEnum.VI),
      [LocaleEnum.JA]: getKeywordCategoryLabel(KeywordCategoryEnum.MEDICINE_TYPE, LocaleEnum.JA),
    },
    translationKey: 'MES-763',
    icon: PillBlisterIcon,
  },
  [KeywordCategoryEnum.ALLERGY_TYPE]: {
    value: KeywordCategoryEnum.ALLERGY_TYPE,
    label: {
      [LocaleEnum.VI]: getKeywordCategoryLabel(KeywordCategoryEnum.ALLERGY_TYPE, LocaleEnum.VI),
      [LocaleEnum.JA]: getKeywordCategoryLabel(KeywordCategoryEnum.ALLERGY_TYPE, LocaleEnum.JA),
    },
    translationKey: 'MES-764',
    icon: AllergyIcon,
  },
  [KeywordCategoryEnum.SYMPTOM_OR_DISEASE]: {
    value: KeywordCategoryEnum.SYMPTOM_OR_DISEASE,
    label: {
      [LocaleEnum.VI]: getKeywordCategoryLabel(
        KeywordCategoryEnum.SYMPTOM_OR_DISEASE,
        LocaleEnum.VI,
      ),
      [LocaleEnum.JA]: getKeywordCategoryLabel(
        KeywordCategoryEnum.SYMPTOM_OR_DISEASE,
        LocaleEnum.JA,
      ),
    },
    translationKey: 'MES-761',
    icon: IllnessPeopleIcon,
  },
  [KeywordCategoryEnum.TEST_VACCINE_TREATMENT]: {
    value: KeywordCategoryEnum.TEST_VACCINE_TREATMENT,
    label: {
      [LocaleEnum.VI]: getKeywordCategoryLabel(
        KeywordCategoryEnum.TEST_VACCINE_TREATMENT,
        LocaleEnum.VI,
      ),
      [LocaleEnum.JA]: getKeywordCategoryLabel(
        KeywordCategoryEnum.TEST_VACCINE_TREATMENT,
        LocaleEnum.JA,
      ),
    },
    translationKey: 'MES-760',
    icon: SyringeIcon,
  },
  [KeywordCategoryEnum.LIFESTYLE]: {
    value: KeywordCategoryEnum.LIFESTYLE,
    label: {
      [LocaleEnum.VI]: getKeywordCategoryLabel(KeywordCategoryEnum.LIFESTYLE, LocaleEnum.VI),
      [LocaleEnum.JA]: getKeywordCategoryLabel(KeywordCategoryEnum.LIFESTYLE, LocaleEnum.JA),
    },
    translationKey: 'MES-765',
    icon: LifeStyleIcon,
  },
  [KeywordCategoryEnum.MEDICAL_SCIENCE]: {
    value: KeywordCategoryEnum.MEDICAL_SCIENCE,
    label: {
      [LocaleEnum.VI]: getKeywordCategoryLabel(KeywordCategoryEnum.MEDICAL_SCIENCE, LocaleEnum.VI),
      [LocaleEnum.JA]: getKeywordCategoryLabel(KeywordCategoryEnum.MEDICAL_SCIENCE, LocaleEnum.JA),
    },
    translationKey: 'MES-766',
    icon: MedicalScienceIcon,
  },
  [KeywordCategoryEnum.OTHER]: {
    value: KeywordCategoryEnum.OTHER,
    label: {
      [LocaleEnum.VI]: getKeywordCategoryLabel(KeywordCategoryEnum.OTHER, LocaleEnum.VI),
      [LocaleEnum.JA]: getKeywordCategoryLabel(KeywordCategoryEnum.OTHER, LocaleEnum.JA),
    },
    translationKey: 'MES-767',
    icon: OtherCategoriesDotIcon,
  },
}
