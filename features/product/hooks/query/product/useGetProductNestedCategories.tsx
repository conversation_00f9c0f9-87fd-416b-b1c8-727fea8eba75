import { productService } from '@/features/product/services/product.service'
import { ProductCategory } from '@/features/product/types'
import { PaginatedDocs } from '@/types/global.type'
import { Params } from '@/types/http.type'
import { UseQueryOptions, useQuery } from '@tanstack/react-query'
import { AxiosRequestConfig } from 'axios'
import { productQueryKeys } from './queryKeys'

export const useGetProductNestedCategories = ({
  categoryId,
  params = {},
  options = {},
  useQueryOptions,
}: {
  categoryId: string
  params?: Params
  options?: AxiosRequestConfig
  useQueryOptions?: Omit<
    UseQueryOptions<PaginatedDocs<ProductCategory> | null>,
    'queryKey' | 'queryFn'
  >
}) => {
  const {
    isError: isGetProductNestedCategoriesError,
    isPending: isGetProductNestedCategoriesLoading,
    data: productNestedCategories,
    ...rest
  } = useQuery({
    queryKey: [productQueryKeys['productNestedCategories'].base(), categoryId, params],
    queryFn: async () =>
      productService.getProductNestedCategories({
        categoryId: categoryId,
        params: params,
        options: options,
      }),
    ...useQueryOptions,
  })
  return {
    isGetProductNestedCategoriesError,
    isGetProductNestedCategoriesLoading,
    productNestedCategories,
    ...rest,
  }
}
