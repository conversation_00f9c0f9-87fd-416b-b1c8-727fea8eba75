import { SafeAreaView } from 'react-native-safe-area-context'
import { Comments } from '../../components/Comments/Comments'
import { Comment } from '../../types'
interface CommentsScreenProps {
  id: string
  relationTo: Comment['commentedOn']['relationTo']
  queryKeys?: string
  staleTime?: number
  gcTime?: number
  totalCommentsQueryKeys?: string
}
export const CommentsScreen = ({
  id,
  relationTo,
  queryKeys,
  staleTime,
  gcTime,
  totalCommentsQueryKeys,
}: CommentsScreenProps) => {
  return (
    <SafeAreaView className="flex-1 bg-white" edges={['left', 'right']}>
      <Comments
        id={id}
        relationTo={relationTo}
        queryKeys={queryKeys}
        staleTime={staleTime}
        gcTime={gcTime}
        totalCommentsQueryKeys={totalCommentsQueryKeys}
      />
    </SafeAreaView>
  )
}
