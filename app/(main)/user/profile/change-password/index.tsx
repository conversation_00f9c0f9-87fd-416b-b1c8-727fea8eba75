import { StackScreenBase } from '@/components/StackBase/StackScreenBase'
import { ChangePasswordScreen } from '@/features/user/screens/ProfileScreen/ChangePasswordScreen/ChangePasswordScreen'

import { primary } from '@/styles/_colors'
import React from 'react'
import { useTranslation } from 'react-i18next'

export default function StackChangePasswordScreen() {
  const { t } = useTranslation()
  return (
    <>
      <StackScreenBase
        options={{
          headerTitle: t('MES-61'),
          headerShown: false,
          headerBackTitle: '',
          headerBackButtonDisplayMode: 'minimal',
          headerTintColor: primary[500],
        }}
      />
      <ChangePasswordScreen />
    </>
  )
}
