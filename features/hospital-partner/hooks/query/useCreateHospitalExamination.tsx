import { useMutation } from '@tanstack/react-query'
import { useRef } from 'react'
import { hospitalPartnerService } from '../../services/hospital-partner.service'
import { CreateHospitalExaminationPayload } from '../../types'
import { hospitalExaminationMutationKeys } from './queryKeys'

export const useCreateHospitalExamination = () => {
  // Define an AbortController to cancel previous requests
  const abortControllerRef = useRef<AbortController | null>(null)

  const {
    isError: isCreateHospitalExaminationError,
    isPending: isCreateHospitalExaminationPending,
    mutate: createHospitalExaminationMutation,
    ...rest
  } = useMutation({
    mutationKey: hospitalExaminationMutationKeys['create-hospital-examination'].base(),
    mutationFn: (payload: CreateHospitalExaminationPayload) => {
      // Abort any ongoing request before initiating a new one
      if (abortControllerRef.current) {
        abortControllerRef.current.abort()
      }

      // Create a new AbortController for the new request
      const abortController = new AbortController()
      abortControllerRef.current = abortController

      // Pass the signal from AbortController to the service method
      return hospitalPartnerService.createHospitalExamination(payload, {
        signal: abortControllerRef.current.signal,
      })
    },
  })

  return {
    isCreateHospitalExaminationError,
    isCreateHospitalExaminationPending,
    createHospitalExaminationMutation,
    ...rest,
  }
}
