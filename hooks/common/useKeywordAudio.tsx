import { LocaleEnum } from '@/enums/locale.enum'
import { useGetKeywordAudio } from '@/hooks/query/keyword/useGetKeywordAudio'
import { Keyword } from '@/types/keyword.type'
import { Media } from '@/types/media.type'
import { useAudioPlayer, useAudioPlayerStatus } from 'expo-audio'
import { useEffect, useState } from 'react'

interface UseKeywordAudioProps {
  keyword: Keyword
}

export const useKeywordAudio = ({ keyword }: UseKeywordAudioProps) => {
  const { audio } = keyword || {}
  const existingUrl = (audio?.find((item) => item.language === LocaleEnum.JA) as Media)?.url || null

  const [audioUrl, setAudioUrl] = useState<string | null>(null)
  const [isAudioRequested, setIsAudioRequested] = useState<boolean>(false)
  const [currentAudioPlayer, setCurrentAudioPlayer] = useState<any>(null)

  const audioPlayer = useAudioPlayer(audioUrl ?? undefined)
  const audioPlayerStatus = useAudioPlayerStatus(audioPlayer)

  const { isGetKeywordAudioLoading, keywordAudio } = useGetKeywordAudio({
    id: keyword.id,
    params: {
      audioLanguage: LocaleEnum.JA,
      depth: 5,
    },
    useQueryOptions: {
      enabled: !existingUrl && isAudioRequested,
      staleTime: Infinity,
    },
  })

  // Update current player when audioPlayer changes
  useEffect(() => {
    if (audioPlayer && audioUrl) {
      setCurrentAudioPlayer(audioPlayer)
    }
  }, [audioPlayer, audioUrl])

  const playAudioSafely = (player: any) => {
    if (!player || !audioUrl) {
      return
    }

    try {
      // Only pause if the player is currently playing
      if (player.currentStatus?.playing) {
        player.pause()
      }
      player.seekTo(0)
      player.play()
    } catch (error) {
      console.error('Error playing audio:', error)
    }
  }

  const handlePlayAudio = () => {
    if (audioUrl && currentAudioPlayer) {
      playAudioSafely(currentAudioPlayer)
    } else {
      setIsAudioRequested(true)
    }
  }

  useEffect(() => {
    if (keywordAudio) {
      const newAudioUrl = keywordAudio?.url
      if (!newAudioUrl) return

      setAudioUrl(newAudioUrl as string)
    }
  }, [keywordAudio])

  useEffect(() => {
    if (audioUrl && currentAudioPlayer && isAudioRequested) {
      const timeout = setTimeout(() => {
        playAudioSafely(currentAudioPlayer)
        setIsAudioRequested(false)
      }, 300)

      return () => clearTimeout(timeout)
    }
  }, [audioUrl, currentAudioPlayer, isAudioRequested])

  return {
    handlePlayAudio,
    isAudioLoading:
      (isGetKeywordAudioLoading && isAudioRequested) || (isAudioRequested && !audioUrl),
    isPlaying: audioPlayer?.currentStatus?.playing,
    audioPlayerStatus,
  }
}
