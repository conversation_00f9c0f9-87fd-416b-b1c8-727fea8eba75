import DeleteIcon from '@/assets/icons/close-icon.svg'
import EmptyBoxIcon from '@/assets/icons/empty-box.svg'
import TrashRedIcon from '@/assets/icons/trash-red-icon.svg'
import WarningIcon from '@/assets/icons/warning-2.svg'
import { Button, ButtonText } from '@/components/ui/Button/Button'
import { SearchInput, SearchInputRef } from '@/components/ui/SearchInput/SearchInput'
import { Skeleton } from '@/components/ui/Skeleton/Skeleton'
import { Text } from '@/components/ui/Text/Text'
import { LocaleEnum } from '@/enums/locale.enum'
import { useLoadingScreen } from '@/hooks/common/useLoadingScreen'
import { useDialog } from '@/hooks/dialog/useDialog'
import colors from '@/styles/_colors'
import { KeywordV2 } from '@/types/keyword.type'
import { useQueryClient } from '@tanstack/react-query'
import { useCallback, useEffect, useMemo, useRef, useState } from 'react'
import { useTranslation } from 'react-i18next'
import { ActivityIndicator, FlatList, RefreshControl, TouchableOpacity, View } from 'react-native'
import Toast from 'react-native-toast-message'
import { useShallow } from 'zustand/react/shallow'
import { KEYWORD_SEARCH_HISTORY_SORT_BY } from '../../enums'
import { keywordSearchHistoryQueryKeys } from '../../hooks/query/queryKeys'
import { useDeleteAllKeywordSearchHistory } from '../../hooks/query/useDeleteAllKeywordSearchHistory'
import { useGetInfiniteKeywordSearchHistory } from '../../hooks/query/useGetInfiniteKeywordSearchHistory'
import { useUpdateKeywordSearchHistory } from '../../hooks/query/useUpdateKeywordSearchHistory'
import { useMedicalDictionaryStore } from '../../stores/MedicalDictionaryStore'
import { KeywordSearchHistory } from '../../types'
import { KeywordItem } from '../KeywordItem/KeywordItem'
import { SortButton } from '../SortButton'
type ListItem =
  | { type: 'search_history'; searchHistory: KeywordSearchHistory }
  | { type: 'loading' }
  | { type: 'loading_skeleton' }

export const MedicalDictionarySearchHistory = () => {
  const { t } = useTranslation()
  const [searchInputValue, setSearchInputValue] = useState('')
  const [searchQuery, setSearchQuery] = useState('')
  const [refreshing, setRefreshing] = useState(false)
  const [localSearchHistory, setLocalSearchHistory] = useState<KeywordSearchHistory[]>([])
  const [totalSearchHistory, setTotalSearchHistory] = useState(0)
  const [deletingItems, setDeletingItems] = useState<Set<string>>(new Set())
  const searchInputRef = useRef<SearchInputRef>(null)
  const { openDeleteAllPopup } = useOpenDeleteAllPopup()
  const { showLoading, hideLoading } = useLoadingScreen()
  const [sortBy, setSortBy] = useState(KEYWORD_SEARCH_HISTORY_SORT_BY.NEWEST)
  const queryClient = useQueryClient()
  const shouldInvalidateQueries = useRef(false)

  const params = useMemo(() => {
    return {
      limit: 10,
      locale: 'all',
      populate: {
        keyword: {
          name: true,
          hiragana: true,
        },
      },
      sort: sortBy === KEYWORD_SEARCH_HISTORY_SORT_BY.NEWEST ? '-updatedAt' : 'updatedAt',
      where: searchQuery
        ? {
            or: Object.values(LocaleEnum)?.map((locale) => ({
              [`keyword.name.${locale}`]: {
                like: searchQuery.trim(),
              },
            })),
          }
        : undefined,
    }
  }, [searchQuery, sortBy])
  const {
    keywordSearchHistoryByUser,
    isGetKeywordSearchHistoryByUserLoading,
    isGetKeywordSearchHistoryByUserError,
    isFetchingNextPage: isGetKeywordSearchHistoryFetchingNextPage,
    fetchNextPage,
    hasNextPage,
    refetch,
  } = useGetInfiniteKeywordSearchHistory({
    params,
    overrideKey: ['list-search-history', params],
    config: {
      staleTime: 0,
      refetchOnWindowFocus: true,
      refetchOnMount: true,
    },
  })

  const { updateKeywordSearchHistoryMutation, isUpdateKeywordSearchHistoryPending } =
    useUpdateKeywordSearchHistory()

  const { deleteAllKeywordSearchHistoryMutation, isDeleteAllKeywordSearchHistoryPending } =
    useDeleteAllKeywordSearchHistory()

  // Flatten all search history from all pages
  const allSearchHistory = useMemo(() => {
    if (!keywordSearchHistoryByUser?.pages) return []
    return keywordSearchHistoryByUser.pages.flatMap((page) => page?.docs || [])
  }, [keywordSearchHistoryByUser])

  // Update local state when new data comes in
  useEffect(() => {
    setLocalSearchHistory(allSearchHistory)
    // Update total from server data
    const serverTotal = keywordSearchHistoryByUser?.pages?.[0]?.totalDocs || 0
    setTotalSearchHistory(serverTotal)
  }, [allSearchHistory, keywordSearchHistoryByUser])

  // Handle pull-to-refresh
  const handleRefresh = useCallback(async () => {
    setRefreshing(true)
    try {
      await refetch()
    } finally {
      setRefreshing(false)
    }
  }, [refetch])

  // Handle removing item using the mutation hook
  const handleRemoveFromHistory = useCallback(
    (searchHistoryId: string) => {
      setDeletingItems((prev) => new Set(prev).add(searchHistoryId))

      updateKeywordSearchHistoryMutation(
        { id: searchHistoryId, type: 'delete' },
        {
          onSuccess: () => {
            // Remove from local state on successful deletion
            setLocalSearchHistory((prev) => prev.filter((item) => item.id !== searchHistoryId))
            setTotalSearchHistory((prev) => Math.max(0, prev - 1))
            setDeletingItems((prev) => {
              const newSet = new Set(prev)
              newSet.delete(searchHistoryId)
              return newSet
            })
            shouldInvalidateQueries.current = true
          },
          onError: (error) => {
            console.error('Failed to delete search history:', error)
            setDeletingItems((prev) => {
              const newSet = new Set(prev)
              newSet.delete(searchHistoryId)
              return newSet
            })
            // Optionally show an error message to the user
          },
        },
      )
    },
    [updateKeywordSearchHistoryMutation],
  )

  // Handle load more
  const handleLoadMore = useCallback(() => {
    if (hasNextPage && !isGetKeywordSearchHistoryFetchingNextPage) {
      fetchNextPage()
    }
  }, [hasNextPage, isGetKeywordSearchHistoryFetchingNextPage, fetchNextPage])

  // Handle delete all functionality
  const handleDeleteAll = useCallback(
    (close?: () => void) => {
      showLoading()
      deleteAllKeywordSearchHistoryMutation(undefined, {
        onSuccess: () => {
          // Clear all local state on successful deletion
          setLocalSearchHistory([])
          setTotalSearchHistory(0)
          setDeletingItems(new Set())
          hideLoading()
          close?.() // Close popup only on success
          shouldInvalidateQueries.current = true
        },
        onError: (error) => {
          console.error('Failed to delete all search history:', error)
          hideLoading()
          Toast.show({
            type: 'error',
            text1: t('MES-197'),
          })
          // Don't close popup on error, let user see the error state
        },
      })
    },
    [deleteAllKeywordSearchHistoryMutation, showLoading, hideLoading],
  )

  // Handle delete all with confirmation popup
  const handleDeleteAllWithConfirmation = useCallback(() => {
    openDeleteAllPopup((close) => {
      handleDeleteAll(close)
    })
  }, [openDeleteAllPopup, handleDeleteAll])

  const handleSearchInputChange = (text: string) => {
    setSearchInputValue(text)
  }

  const handleClearSearchInput = () => {
    setSearchInputValue('')
    setSearchQuery('')
    setSearchTextValue('')
  }

  const { setSearchTextValue } = useMedicalDictionaryStore(
    useShallow((state) => ({
      setSearchTextValue: state.setSearchText,
    })),
  )

  // Build data array with different ListItem types
  const data = useMemo((): ListItem[] => {
    const items: ListItem[] = []

    // Show loading skeletons during initial load or refresh (but not during pagination)
    const isShowingLoadingSkeleton =
      (isGetKeywordSearchHistoryByUserLoading || refreshing) &&
      !isGetKeywordSearchHistoryFetchingNextPage

    if (isShowingLoadingSkeleton) {
      items.push({ type: 'loading_skeleton' })
    } else {
      // Search history as individual items - use local state for instant updates
      localSearchHistory.forEach((searchHistory) => {
        items.push({ type: 'search_history', searchHistory })
      })

      // Loading indicator for pagination
      if (isGetKeywordSearchHistoryFetchingNextPage) {
        items.push({ type: 'loading' })
      }
    }

    return items
  }, [
    localSearchHistory,
    isGetKeywordSearchHistoryFetchingNextPage,
    isGetKeywordSearchHistoryByUserLoading,
    refreshing,
  ])

  // Render item based on ListItem type
  const renderItem = useCallback(
    ({ item }: { item: ListItem }) => {
      switch (item.type) {
        case 'search_history':
          const keyword = item.searchHistory.keyword as KeywordV2
          if (!keyword) return null

          const isDeleting = deletingItems.has(item.searchHistory.id)

          return (
            <KeywordItem
              keyword={keyword}
              isShowArchiveButton={false}
              renderCustomRightCard={
                <TouchableOpacity
                  className="shrink-0 flex-row items-center gap-x-2 self-center "
                  onPress={(e) => {
                    e.stopPropagation()
                    handleRemoveFromHistory(item.searchHistory.id)
                  }}
                  disabled={isDeleting}
                >
                  {isDeleting ? (
                    <ActivityIndicator size="small" />
                  ) : (
                    <View className="">
                      <DeleteIcon width={28} height={28} />
                    </View>
                  )}
                </TouchableOpacity>
              }
            />
          )

        case 'loading':
          return (
            <View className="items-center py-4">
              <ActivityIndicator size="small" />
            </View>
          )

        case 'loading_skeleton':
          return (
            <View className="flex flex-col gap-y-4 ">
              {new Array(8).fill(0).map((_, index) => (
                <Skeleton key={index} className="h-20 w-full" />
              ))}
            </View>
          )

        default:
          return null
      }
    },
    [handleRemoveFromHistory, deletingItems],
  )

  const renderEmptyComponent = useCallback(() => {
    if (isGetKeywordSearchHistoryByUserError) {
      return (
        <View className="items-center py-8">
          <Text size="body6" className="text-red-500">
            {t('MES-197')}
          </Text>
        </View>
      )
    }
    if (isGetKeywordSearchHistoryByUserLoading || isGetKeywordSearchHistoryFetchingNextPage) {
      return null
    }

    return (
      <View className="items-center py-8">
        <EmptyBoxIcon />
        <Text size="body6" variant="default">
          {t('MES-781')}
        </Text>
      </View>
    )
  }, [
    isGetKeywordSearchHistoryByUserError,
    isGetKeywordSearchHistoryByUserLoading,
    isGetKeywordSearchHistoryFetchingNextPage,
  ])

  const renderSeparator = useCallback(() => {
    return <View className="h-2" />
  }, [])

  const keyExtractor = useCallback((item: ListItem, index: number) => {
    if (item.type === 'search_history') {
      return `search_history-${item.searchHistory.id}`
    }
    return `${item.type}-${index}`
  }, [])

  // Switch sort by
  const handleSortBy = () => {
    setSortBy(
      sortBy === KEYWORD_SEARCH_HISTORY_SORT_BY.NEWEST
        ? KEYWORD_SEARCH_HISTORY_SORT_BY.OLDEST
        : KEYWORD_SEARCH_HISTORY_SORT_BY.NEWEST,
    )
  }

  const handleInvalidateQueries = useCallback(() => {
    queryClient.invalidateQueries({
      queryKey: [keywordSearchHistoryQueryKeys['keyword-search-history-by-user'].base()],
      type: 'active',
      refetchType: 'active',
      exact: false,
    })
  }, [shouldInvalidateQueries.current])

  useEffect(() => {
    return () => {
      if (shouldInvalidateQueries.current) {
        handleInvalidateQueries()
      }
    }
  }, [])

  return (
    <View className="flex-1 flex-col gap-y-3 pt-4">
      {/* Header */}
      <View className="flex flex-col gap-y-3 px-4">
        <View className="flex flex-col gap-y-3 bg-white">
          <View className="flex flex-col gap-y-3">
            <View className="flex flex-row items-center gap-x-2">
              {/* Search Input */}
              <SearchInput
                ref={searchInputRef}
                placeholder={t('MES-66')}
                value={searchInputValue}
                onChangeText={handleSearchInputChange}
                onClear={handleClearSearchInput}
                onSubmitEditing={() => {
                  setSearchQuery(searchInputValue)
                  setSearchTextValue(searchInputValue)
                }}
              />
            </View>

            {/* Panel */}
            <View className="flex flex-row items-center justify-between">
              {/* Sort By */}
              {totalSearchHistory > 0 && (
                <SortButton sortBy={sortBy} onPress={handleSortBy} showIcon={true} />
              )}

              {/* Delete All Button */}
              {totalSearchHistory > 0 && (
                <View className="flex flex-row ">
                  <TouchableOpacity
                    className="flex flex-row items-center gap-x-2"
                    onPress={handleDeleteAllWithConfirmation}
                    disabled={isDeleteAllKeywordSearchHistoryPending}
                  >
                    <TrashRedIcon width={16} height={16} />
                    <Text size="body6" variant="error">
                      {t('MES-778')}
                    </Text>
                  </TouchableOpacity>
                </View>
              )}
            </View>
          </View>
        </View>
      </View>

      {/* List */}
      <View className="mt-3 flex-1 px-4">
        <FlatList
          showsVerticalScrollIndicator={false}
          data={data}
          renderItem={renderItem}
          keyExtractor={keyExtractor}
          contentContainerStyle={{
            paddingBottom: 20,
          }}
          ItemSeparatorComponent={renderSeparator}
          ListEmptyComponent={renderEmptyComponent}
          onEndReached={handleLoadMore}
          onEndReachedThreshold={0.1}
          refreshControl={
            <RefreshControl
              refreshing={refreshing}
              onRefresh={handleRefresh}
              colors={[colors.primary['500']]}
              tintColor={colors.primary['500']}
              progressBackgroundColor="#FFFFFF"
            />
          }
        />
      </View>
    </View>
  )
}

// Delete All Popup

interface DeleteAllPopupProps {
  close?: () => void
  onPressDeleteAll?: (close?: () => void) => void
}
export const DeleteAllPopup = ({ close, onPressDeleteAll }: DeleteAllPopupProps) => {
  const { t } = useTranslation()

  return (
    <View className="flex flex-col items-center justify-center rounded-xl bg-white">
      <WarningIcon width={30} height={30} />
      <Text className="typo-body-3 mt-6 text-center">{t('MES-779')}</Text>
      <Text className="typo-body-7 mt-3 text-center">{t('MES-780')}</Text>
      <View className="mt-6 flex w-full flex-col gap-y-2">
        <Button
          className="w-full flex-1 rounded-lg"
          variant="solid"
          action="negative"
          onPress={() => onPressDeleteAll?.(close)}
        >
          <ButtonText>{t('MES-169')}</ButtonText>
        </Button>
        <Button
          variant="outline"
          className="w-full flex-1 rounded-lg border-custom-text-disabled"
          onPress={close}
        >
          <ButtonText>{t('MES-224')}</ButtonText>
        </Button>
      </View>
    </View>
  )
}

export const useOpenDeleteAllPopup = () => {
  const { openDialog } = useDialog()
  const openDeleteAllPopup = (onPressDeleteAll: (close?: () => void) => void) => {
    openDialog({
      children: ({ close }: { close: () => void }) => {
        return <DeleteAllPopup close={close} onPressDeleteAll={() => onPressDeleteAll?.(close)} />
      },
      variant: 'blank',
      contentSize: 'md',
      bodyClassName: 'p-0 m-0',
    })
  }
  return { openDeleteAllPopup }
}
