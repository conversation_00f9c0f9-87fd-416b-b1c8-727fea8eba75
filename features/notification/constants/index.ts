import BluePill from '@/assets/icons/blue-pill-icon.svg'
import GoldBell from '@/assets/icons/gold-bell-icon.svg'
import NewCommentNotiIcon from '@/assets/icons/new-comment-icon.svg'
import NewMessageNotiIcon from '@/assets/icons/new-message-noti-icon.svg'
import OrangeBookIcon from '@/assets/icons/orange-book-icon.svg'
import { LocaleEnum } from '@/enums/locale.enum'
import {
  PushNotificationMessagesPlatformGroupsEnum,
  PushNotificationMessagesSendToEnum,
  PushNotificationMessagesTabEnum,
  PushNotificationMessagesTypeEnum,
  PushNotificationTemplateEnum,
  PushNotificationTemplatePrefixKeyEnum,
} from '../enums/push-notification.enum'

export const PUSH_NOTIFICATION_SEND_TO_OPTIONS = [
  {
    value: PushNotificationMessagesSendToEnum.ALL,
    label: {
      [LocaleEnum.VI]: 'Tất cả',
      [LocaleEnum.JA]: 'すべて',
    },
  },
  {
    value: PushNotificationMessagesSendToEnum.PLATFORM_GROUPS,
    label: {
      [LocaleEnum.VI]: 'Nhóm nền tảng',
      [LocaleEnum.JA]: 'プラットフォームグループ',
    },
  },
  {
    value: PushNotificationMessagesSendToEnum.SPECIFIC_USERS,
    label: {
      [LocaleEnum.VI]: 'Người dùng cụ thể',
      [LocaleEnum.JA]: '特定のユーザー',
    },
  },
]

export const PUSH_NOTIFICATION_PLATFORM_GROUPS_OPTIONS = [
  {
    value: PushNotificationMessagesPlatformGroupsEnum.IOS,
    label: {
      [LocaleEnum.VI]: 'iOS',
      [LocaleEnum.JA]: 'iOS',
    },
  },
  {
    value: PushNotificationMessagesPlatformGroupsEnum.ANDROID,
    label: {
      [LocaleEnum.VI]: 'Android',
      [LocaleEnum.JA]: 'Android',
    },
  },
  {
    value: PushNotificationMessagesPlatformGroupsEnum.WEB,
    label: {
      [LocaleEnum.VI]: 'Web',
      [LocaleEnum.JA]: 'Web',
    },
  },
]

export const PUSH_NOTIFICATION_TYPE_OPTIONS = [
  {
    value: PushNotificationMessagesTypeEnum.NEW_POST,
    label: {
      [LocaleEnum.VI]: 'Bài viết mới',
      [LocaleEnum.JA]: '新しい投稿',
    },
    notes: [
      `Include prefix keys <<${PushNotificationTemplatePrefixKeyEnum.POST_TITLE}>>for the title and <<${PushNotificationTemplatePrefixKeyEnum.POST_DESCRIPTION}>>for the description`,
    ],
    translationKey: 'MES-19',
    icon: OrangeBookIcon,
    backgroundColor: '#FDE7CF',
    filterLabelKey: 'MES-943',
  },
  {
    value: PushNotificationMessagesTypeEnum.CHAT_MESSAGE,
    label: {
      [LocaleEnum.VI]: 'Tin nhắn chat',
      [LocaleEnum.JA]: 'チャットメッセージ',
    },
    translationKey: 'MES-836',
    icon: NewMessageNotiIcon,
    backgroundColor: '#D0DFFB',
    filterLabelKey: 'MES-944',
  },
  {
    value: PushNotificationMessagesTypeEnum.NEW_PRODUCT,
    label: {
      [LocaleEnum.VI]: 'Sản phẩm mới',
      [LocaleEnum.JA]: '新しい商品',
    },
    notes: [
      `Include prefix keys <<${PushNotificationTemplatePrefixKeyEnum.PRODUCT_TITLE}>>for the title and <<${PushNotificationTemplatePrefixKeyEnum.PRODUCT_DESCRIPTION}>>for the description`,
    ],
    translationKey: 'MES-660',
    icon: BluePill,
    backgroundColor: '#D0F4FB',
    filterLabelKey: 'MES-945',
  },
  {
    value: PushNotificationMessagesTypeEnum.GENERAL,
    label: {
      [LocaleEnum.VI]: 'Thông báo chung',
      [LocaleEnum.JA]: '一般通知',
    },
    translationKey: 'MES-263',
    icon: GoldBell,
    backgroundColor: '#FEF4CD',
    filterLabelKey: 'MES-946',
  },
  {
    value: PushNotificationMessagesTypeEnum.APP_SYSTEM,
    label: {
      [LocaleEnum.VI]: 'Thông báo hệ thống',
      [LocaleEnum.JA]: 'システム通知',
    },
    translationKey: 'MES-833',
    icon: GoldBell,
    backgroundColor: '#FEF4CD',
    filterLabelKey: 'MES-947',
  },
  {
    value: PushNotificationMessagesTypeEnum.NEW_COMMENT,
    label: {
      [LocaleEnum.VI]: 'Bình luận mới',
      [LocaleEnum.JA]: '新しいコメント',
    },
    translationKey: 'MES-948',
    icon: NewCommentNotiIcon,
    backgroundColor: '#E4F1F1',
    filterLabelKey: 'MES-948',
  },
  {
    value: PushNotificationMessagesTypeEnum.COMMENT_REPLY,
    label: {
      [LocaleEnum.VI]: 'Phản hồi bình luận',
      [LocaleEnum.JA]: 'コメントの返信',
    },
    translationKey: 'MES-949',
    icon: NewMessageNotiIcon,
    backgroundColor: '#D0DFFB',
    filterLabelKey: 'MES-949',
  },
]

type PushNotificationTypeOption = (typeof PUSH_NOTIFICATION_TYPE_OPTIONS)[number]
type PushNotificationTypeValue = PushNotificationMessagesTypeEnum | PushNotificationTemplateEnum

export const PUSH_NOTIFICATION_TYPE_OPTIONS_MAP = PUSH_NOTIFICATION_TYPE_OPTIONS.reduce(
  (acc, item) => {
    acc[item.value] = item
    return acc
  },
  {} as Record<PushNotificationTypeValue, PushNotificationTypeOption>,
)
export const USER_PUSH_NOTIFICATION_TAB_OPTIONS = [
  {
    value: PushNotificationMessagesTabEnum.ALL,
    translationKey: 'MES-141',
  },
  {
    value: PushNotificationMessagesTabEnum.NOT_VIEWED,
    translationKey: 'MES-831',
  },
  {
    value: PushNotificationMessagesTabEnum.VIEWED,
    translationKey: 'MES-830',
  },
]
