import { Faculty } from '@/types/faculty.type'
import { PaginatedDocs } from '@/types/global.type'
import { Params } from '@/types/http.type'
import { UseQueryOptions, useQuery } from '@tanstack/react-query'
import { AxiosRequestConfig } from 'axios'
import { facultyService } from '../../services/faculty.service'
import { facultyQueryKeys } from './queryKeys'

export const useGetFaculties = ({
  params = {},
  options = {},
  useQueryOptions,
}: {
  params?: Params
  options?: AxiosRequestConfig
  useQueryOptions?: Omit<UseQueryOptions<PaginatedDocs<Faculty> | null>, 'queryKey' | 'queryFn'>
} = {}) => {
  const {
    isError: isGetFacultiesError,
    isPending: isGetFacultiesLoading,
    data: faculties,
    ...rest
  } = useQuery({
    queryKey: [facultyQueryKeys['faculties'].base(), params],
    queryFn: async () =>
      facultyService.getFaculties({
        params: params,
        options: options,
      }),
    ...useQueryOptions,
  })
  return {
    isGetFacultiesError,
    isGetFacultiesLoading,
    faculties,
    ...rest,
  }
}
